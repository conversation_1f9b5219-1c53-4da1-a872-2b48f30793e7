"use strict";
const common_vendor = require("../common/vendor.js");
const useUserStore = common_vendor.defineStore("user", () => {
  const userInfo = common_vendor.ref();
  function setUser(val) {
    userInfo.value = val;
  }
  return { userInfo, setUser };
});
const useAreaStore = common_vendor.defineStore("area", () => {
  const areaName = common_vendor.ref();
  const areaCode = common_vendor.ref();
  const areaNameStr = common_vendor.index.getStorageSync("areaName");
  const areaCodeStr = common_vendor.index.getStorageSync("areaCode");
  if (areaNameStr) {
    areaName.value = areaNameStr;
  }
  if (areaCodeStr) {
    areaCode.value = areaCodeStr;
  }
  function setArea(code, name) {
    areaCode.value = code;
    areaName.value = name;
    common_vendor.index.setStorageSync("areaName", name);
    common_vendor.index.setStorageSync("areaCode", code);
  }
  return { areaName, areaCode, setArea };
});
const useHistoryStore = common_vendor.defineStore("history", () => {
  const historyList = common_vendor.ref([]);
  const historyAreaList = common_vendor.index.getStorageSync("historyAreaList");
  if (historyAreaList) {
    historyList.value = historyAreaList;
  }
  function pushHistory(area) {
    const index = historyList.value.findIndex((v) => v.code == area.code);
    if (index == -1) {
      historyList.value.unshift(area);
      if (historyList.value.length > 10) {
        historyList.value.pop();
      }
      common_vendor.index.setStorageSync("historyAreaList", historyList.value);
    }
  }
  return { historyList, pushHistory };
});
const useLocationStore = common_vendor.defineStore("location", () => {
  const location = common_vendor.ref("");
  const longitude = common_vendor.ref();
  const latitude = common_vendor.ref();
  function setLocation(val) {
    location.value = val;
  }
  function setLngLat(lng, lat) {
    longitude.value = lng;
    latitude.value = lat;
  }
  return { location, longitude, latitude, setLocation, setLngLat };
});
exports.useAreaStore = useAreaStore;
exports.useHistoryStore = useHistoryStore;
exports.useLocationStore = useLocationStore;
exports.useUserStore = useUserStore;
