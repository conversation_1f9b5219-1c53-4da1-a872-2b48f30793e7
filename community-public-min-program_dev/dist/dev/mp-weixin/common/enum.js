"use strict";
const PLATFORM_TYPE = "2";
var CameraType = /* @__PURE__ */ ((CameraType2) => {
  CameraType2["WATERMARK_CAMERA"] = "1";
  CameraType2["ID_CAMERA"] = "2";
  return CameraType2;
})(CameraType || {});
var AgreementType = /* @__PURE__ */ ((AgreementType2) => {
  AgreementType2["USER_AGREEMENT"] = "1";
  AgreementType2["PRIVACY_AGREEMENT"] = "2";
  return AgreementType2;
})(AgreementType || {});
var SceneType = /* @__PURE__ */ ((SceneType2) => {
  SceneType2["MAP"] = "map";
  SceneType2["AUTH"] = "auth";
  return SceneType2;
})(SceneType || {});
var PageType = /* @__PURE__ */ ((PageType2) => {
  PageType2["MINI_PROGRAM"] = "1";
  PageType2["H5"] = "2";
  PageType2["OTHER_MINI_PROGRAM"] = "3";
  return PageType2;
})(PageType || {});
var TabBarIndexType = /* @__PURE__ */ ((TabBarIndexType2) => {
  TabBarIndexType2[TabBarIndexType2["HOME"] = 0] = "HOME";
  TabBarIndexType2[TabBarIndexType2["MAP"] = 1] = "MAP";
  TabBarIndexType2[TabBarIndexType2["MY"] = 2] = "MY";
  TabBarIndexType2[TabBarIndexType2["FOUND"] = 3] = "FOUND";
  TabBarIndexType2[TabBarIndexType2["ASS"] = 4] = "ASS";
  TabBarIndexType2[TabBarIndexType2["ACTIVITY"] = 5] = "ACTIVITY";
  return TabBarIndexType2;
})(TabBarIndexType || {});
exports.AgreementType = AgreementType;
exports.CameraType = CameraType;
exports.PLATFORM_TYPE = PLATFORM_TYPE;
exports.PageType = PageType;
exports.SceneType = SceneType;
exports.TabBarIndexType = TabBarIndexType;
