"use strict";
let apiUrl = "";
let areaUrl = "";
let mapUrl = "";
let imgPath = "";
let umaDeBug = false;
{
  apiUrl = "https://qlzhsq.qlzhsq.cn:30400/prod-api";
  areaUrl = "https://qlzhsq.qlzhsq.cn:30400/areaView";
  mapUrl = "https://qlzhsq.qlzhsq.cn:30400";
  imgPath = "https://qlzhsq.qlzhsq.cn:30400";
}
const API_URL = apiUrl;
const AREA_URL = areaUrl;
const MAP_URL = mapUrl;
const IMG_PATH = imgPath;
const CLIENT_ID = "428a8310cd442757ae699df5d894f051";
const KEY = "b19275c0637cb430820ecacda278e759";
const PRIVATE_KEY = "cede681d7da22e9f7a8f490d5eb39236";
const UMA_CONFIG = {
  appKey: "65e994523ace4f7d7196b9de",
  useOpenid: true,
  // 是否使用openid进行统计，此项为false时将使用友盟+随机ID进行用户统计。使用openid来统计微信小程序的用户，会使统计的指标更为准确，对系统准确性要求高的应用推荐使用OpenID。
  autoGetOpenid: true,
  // 是否需要通过友盟后台获取openid，如若需要，请到友盟后台设置appId及secret
  debug: umaDeBug,
  //是否打开调试模式
  uploadUserInfo: true
  // 上传用户信息，上传后可以查看有头像的用户分享信息，同时在查看用户画像时，公域画像的准确性会提升。
};
exports.API_URL = API_URL;
exports.AREA_URL = AREA_URL;
exports.CLIENT_ID = CLIENT_ID;
exports.IMG_PATH = IMG_PATH;
exports.KEY = KEY;
exports.MAP_URL = MAP_URL;
exports.PRIVATE_KEY = PRIVATE_KEY;
exports.UMA_CONFIG = UMA_CONFIG;
