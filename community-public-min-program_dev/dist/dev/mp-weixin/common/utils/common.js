"use strict";
const common_vendor = require("../vendor.js");
const common_config = require("../config.js");
async function compress(size, src) {
  const maxSize = 4096e3;
  let option = {};
  if (size > maxSize) {
    const quality = maxSize / size;
    option.quality = Math.trunc(quality * 100);
  } else {
    option.quality = 100;
  }
  const imageInfo = await common_vendor.index.getImageInfo({
    src
  });
  const width = imageInfo.width;
  const height = imageInfo.height;
  const max = 4096;
  if (width > max && height > max) {
    if (width > height) {
      option.compressedWidth = max;
    } else {
      option.compressedHeight = max;
    }
  } else if (width > max) {
    option.compressedWidth = max;
  } else if (height > max) {
    option.compressedHeight = max;
  }
  option.src = src;
  return common_vendor.index.compressImage(option);
}
function getCurrentTime() {
  const now = new Date();
  const hours = ("0" + now.getHours()).slice(-2);
  const minutes = ("0" + now.getMinutes()).slice(-2);
  return hours + ":" + minutes;
}
function getCurrentDate() {
  const now = new Date();
  const year = now.getFullYear();
  const month = ("0" + (now.getMonth() + 1)).slice(-2);
  const day = ("0" + now.getDate()).slice(-2);
  ("0" + now.getHours()).slice(-2);
  ("0" + now.getMinutes()).slice(-2);
  ("0" + now.getSeconds()).slice(-2);
  return year + "-" + month + "-" + day;
}
function getCurrentWeek() {
  let arr = new Array("日", "一", "二", "三", "四", "五", "六");
  let week = new Date().getDay();
  return "星期" + arr[week];
}
function dateFormat(timestamp) {
  const date = new Date(timestamp * 1e3);
  const year = date.getFullYear() + "-";
  const month = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
  const day = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
  (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ":";
  date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return year + month + day;
}
function formatDate(date, type = 2) {
  if (!date) {
    return "";
  }
  if (typeof date === "string") {
    date = new Date(date);
  }
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return "";
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  if (type === 1) {
    return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
  } else if (type === 2) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else {
    return `${year}-${month}-${day}`;
  }
}
function isUrl(str) {
  return str && (str.indexOf("http://") != -1 || str.indexOf("https://") != -1) || false;
}
function img(path) {
  return isUrl(path) ? path : `${common_config.IMG_PATH}/${path}`;
}
exports.compress = compress;
exports.dateFormat = dateFormat;
exports.formatDate = formatDate;
exports.getCurrentDate = getCurrentDate;
exports.getCurrentTime = getCurrentTime;
exports.getCurrentWeek = getCurrentWeek;
exports.img = img;
