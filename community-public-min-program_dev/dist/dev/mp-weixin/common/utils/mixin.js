"use strict";
const common_utils_share = require("./share.js");
const mixin = {
  install(vue) {
    vue.mixin({
      data() {
        return {};
      },
      onLoad: (data) => {
      },
      onShow: () => {
        const { setShare } = common_utils_share.useShare();
        setShare({ route: common_utils_share.currRoute() });
      },
      onShareAppMessage() {
        common_utils_share.useShare().onShareAppMessage();
      },
      onShareTimeline() {
        common_utils_share.useShare().onShareTimeline();
      },
      methods: {}
    });
  }
};
exports.mixin = mixin;
