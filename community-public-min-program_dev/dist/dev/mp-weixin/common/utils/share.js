"use strict";
const common_vendor = require("../vendor.js");
const common_utils_common = require("./common.js");
const currRoute = () => {
  const pages = getCurrentPages();
  const route = pages[pages.length - 1];
  return route ? route.route : "";
};
const currShareRoute = () => {
  const pages = getCurrentPages();
  if (pages.length == 0) {
    return {
      path: "/",
      params: {}
    };
  }
  let currentRoute = pages[pages.length - 1].route;
  let currentParam = pages[pages.length - 1].options || {};
  let params = {};
  for (let key in currentParam) {
    params[key] = currentParam[key];
  }
  let currentPath = "/" + currentRoute;
  return {
    path: currentPath,
    params
  };
};
const useShare = common_vendor.defineStore("useShare", () => {
  const weappOptions = common_vendor.ref({});
  const currentRoute = common_vendor.ref("");
  const getQuery = () => {
    let query = currShareRoute().params;
    let wap_member_id = common_vendor.index.getStorageSync("wap_member_id");
    if (wap_member_id) {
      query.mid = wap_member_id;
    }
    let queryStr = [];
    for (let key in query) {
      queryStr.push(key + "=" + query[key]);
    }
    return queryStr;
  };
  const setShare = (options = {}) => {
    if (currRoute() == "" || options && options.route && options.route == currentRoute.value && !options.weapp) {
      return;
    }
    let queryStr = getQuery();
    currentRoute.value = currRoute() || "";
    weappOptions.value = {
      path: "/" + currRoute() + (queryStr.length > 0 ? "?" + queryStr.join("&") : ""),
      query: queryStr.join("&")
    };
    if (options && options.weapp) {
      weappOptions.value.title = options.weapp.title || "";
      weappOptions.value.query = options.weapp.path || queryStr.join("&");
      weappOptions.value.imageUrl = options.weapp.imageUrl ? common_utils_common.img(options.weapp.imageUrl) : "";
    }
  };
  const shareApp = (options = {}) => {
    common_vendor.onShareAppMessage((res) => {
      let config = weappOptions.value;
      if (!config)
        config = {};
      return {
        ...config,
        ...options
      };
    });
  };
  const shareTime = (options = {}) => {
    common_vendor.onShareTimeline(() => {
      let config = weappOptions.value;
      if (!config)
        config = {};
      return {
        ...config,
        ...options
      };
    });
  };
  return {
    setShare,
    onShareAppMessage: shareApp,
    onShareTimeline: shareTime
  };
});
exports.currRoute = currRoute;
exports.useShare = useShare;
