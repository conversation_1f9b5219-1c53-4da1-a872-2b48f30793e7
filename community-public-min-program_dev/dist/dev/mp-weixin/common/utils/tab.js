"use strict";
const common_vendor = require("../vendor.js");
const common_enum = require("../enum.js");
const currentTabIndex = common_vendor.ref(common_enum.TabBarIndexType.HOME);
function change(index) {
  currentTabIndex.value = index;
  let url = "";
  switch (index) {
    case common_enum.TabBarIndexType.HOME:
      url = "../../pages/index/index";
      break;
    case common_enum.TabBarIndexType.MAP:
      url = "../../pages/map/map";
      break;
    case common_enum.TabBarIndexType.MY:
      url = "../../pages/my/my";
      break;
    case common_enum.TabBarIndexType.FOUND:
      url = "../../pages/found/found";
      break;
    case common_enum.TabBarIndexType.ASS:
      url = "../../pages/mutualAssistance/mutualAssistance";
      break;
    case common_enum.TabBarIndexType.ACTIVITY:
      url = "../../pages/activity/activity";
      break;
  }
  common_vendor.index.switchTab({
    url
  });
}
exports.change = change;
exports.currentTabIndex = currentTabIndex;
