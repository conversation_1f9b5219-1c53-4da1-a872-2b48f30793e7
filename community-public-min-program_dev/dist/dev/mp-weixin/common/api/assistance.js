"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function addWishlist(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/wishlist/addWishlist`,
    method: "POST",
    data: params,
    await: true
  });
}
function pageWishlists(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/wishlist/pageWishlists`,
    method: "GET",
    data: params,
    await: true
  });
}
function getWishlistsInfo(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/wishlist/getWishlistsInfo/${id}`,
    method: "GET",
    await: true
  });
}
function getSingUp(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/wishlist/getSingUp/${id}`,
    method: "GET",
    await: true
  });
}
function addSingUp(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/wishlist/singUp`,
    method: "POST",
    data: params,
    await: true
  });
}
exports.addSingUp = addSingUp;
exports.addWishlist = addWishlist;
exports.getSingUp = getSingUp;
exports.getWishlistsInfo = getWishlistsInfo;
exports.pageWishlists = pageWishlists;
