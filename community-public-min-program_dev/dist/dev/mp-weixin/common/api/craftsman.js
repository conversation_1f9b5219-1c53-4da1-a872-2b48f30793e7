"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/craftsman/getList`,
    method: "GET",
    data: params,
    await: true
  });
}
function getPopularList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/craftsman/getPopularList`,
    method: "GET",
    data: params,
    await: true
  });
}
const getDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/craftsman/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const add = (params) => {
  return common_request.request({
    url: `/craftsman/applet/add`,
    method: "POST",
    data: params,
    await: true
  });
};
const getCraftsmanDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/craftsman/getCraftsmanDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const getMyList = (params) => {
  return common_request.request({
    url: `/craftsman/getMyList`,
    method: "GET",
    data: params,
    await: true
  });
};
exports.add = add;
exports.getCraftsmanDetails = getCraftsmanDetails;
exports.getDetails = getDetails;
exports.getList = getList;
exports.getMyList = getMyList;
exports.getPopularList = getPopularList;
