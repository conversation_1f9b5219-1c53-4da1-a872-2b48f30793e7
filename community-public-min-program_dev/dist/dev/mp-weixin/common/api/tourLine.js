"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
const getPageList = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/applet/communityTourLine/getPage`,
    method: "GET",
    data: params,
    await: true
  });
};
const getPageItemDetail = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/applet/communityTourLine/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const getDistrictData = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/applet/district/getDistrictByHeadChar?name=`,
    method: "GET",
    data: params,
    await: true
  });
};
exports.getDistrictData = getDistrictData;
exports.getPageItemDetail = getPageItemDetail;
exports.getPageList = getPageList;
