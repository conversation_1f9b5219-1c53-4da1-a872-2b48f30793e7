"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getNewsDataList() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/news/getNew`,
    method: "GET"
  });
}
function getBannerList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/banner/list`,
    method: "GET",
    data: params
  });
}
function getConfig(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/config/getAppletConfig`,
    method: "GET",
    data: params
  });
}
function getListNew(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/news/getListNew`,
    method: "GET",
    data: params
  });
}
function getListByPublic() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/menu/getListByPublic`,
    method: "GET"
  });
}
function getWatermarkList() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/watermark/camera/getList`,
    method: "POST"
  });
}
function addCameraUsage(params) {
  return common_request.request({
    url: `${common_config.API_URL}/cockpit/statistics/addCameraUsage`,
    method: "POST",
    data: params
  });
}
function addFamilyDoctorUsage(params) {
  return common_request.request({
    url: `${common_config.API_URL}/cockpit/statistics/addFamilyDoctorUsage`,
    method: "POST",
    data: params
  });
}
function getEasyServiceList() {
  return common_request.request({
    url: `${common_config.API_URL}/service/all`,
    method: "GET"
  });
}
exports.addCameraUsage = addCameraUsage;
exports.addFamilyDoctorUsage = addFamilyDoctorUsage;
exports.getBannerList = getBannerList;
exports.getConfig = getConfig;
exports.getEasyServiceList = getEasyServiceList;
exports.getListByPublic = getListByPublic;
exports.getListNew = getListNew;
exports.getNewsDataList = getNewsDataList;
exports.getWatermarkList = getWatermarkList;
