"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getChatList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/ai/im/list`,
    method: "GET",
    data: params
  });
}
function getSiteSearch(params) {
  return common_request.request({
    url: `${common_config.API_URL}/ai/site/search`,
    method: "GET",
    data: params
  });
}
function getMessageList(imId) {
  return common_request.request({
    // /ai/chat/list/{imId}
    url: `${common_config.API_URL}/ai/chat/list/${imId}`,
    method: "GET"
  });
}
exports.getChatList = getChatList;
exports.getMessageList = getMessageList;
exports.getSiteSearch = getSiteSearch;
