"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getrecentlyActivity(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/recentlyActivity/list`,
    method: "GET",
    data: params,
    await: true
  });
}
function pushActivity(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/pushActivity`,
    method: "POST",
    data: params,
    await: true
  });
}
function deleteActivity(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/deleteActivity`,
    method: "DELETE",
    data: params,
    await: true
  });
}
function getReviewList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/getReviewList`,
    method: "GET",
    data: params,
    await: true
  });
}
function getDetailsById(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/getById/${id}`,
    method: "GET",
    await: true
  });
}
function reviewData(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/reviewData`,
    method: "POST",
    data: params,
    await: true
  });
}
function getRecentlyHome() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/dataManage/recentlyHome/list`,
    method: "GET",
    await: true
  });
}
exports.deleteActivity = deleteActivity;
exports.getDetailsById = getDetailsById;
exports.getRecentlyHome = getRecentlyHome;
exports.getReviewList = getReviewList;
exports.getrecentlyActivity = getrecentlyActivity;
exports.pushActivity = pushActivity;
exports.reviewData = reviewData;
