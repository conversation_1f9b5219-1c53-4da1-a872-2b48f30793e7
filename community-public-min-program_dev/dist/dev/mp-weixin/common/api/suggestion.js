"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getSuggestInfo(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/getInfo/${id}`,
    method: "GET",
    await: true
  });
}
function getPageSuggest(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/getPage`,
    method: "GET",
    data: params,
    await: true
  });
}
function addSuggest(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/submission`,
    method: "PUT",
    data: params,
    await: true
  });
}
function insertSuggest(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/submissionUser`,
    method: "PUT",
    data: params,
    await: true
  });
}
function getExcellentSuggestions(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/leaveWordListExcellent/${id}`,
    method: "GET",
    await: true
  });
}
function getLeaveWordDetail(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/questionnaire/getLeaveWord/${id}`,
    method: "GET",
    await: true
  });
}
exports.addSuggest = addSuggest;
exports.getExcellentSuggestions = getExcellentSuggestions;
exports.getLeaveWordDetail = getLeaveWordDetail;
exports.getPageSuggest = getPageSuggest;
exports.getSuggestInfo = getSuggestInfo;
exports.insertSuggest = insertSuggest;
