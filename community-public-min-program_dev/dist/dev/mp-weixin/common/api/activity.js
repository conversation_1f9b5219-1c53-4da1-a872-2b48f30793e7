"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getActivityInfo(activityId) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/activity/activityInfo/${activityId}`,
    method: "GET",
    await: true
  });
}
function getPageActivity(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/activity/pageActivity`,
    method: "GET",
    data: params,
    await: true
  });
}
function addActivity(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/activity/singUp`,
    method: "POST",
    data: params,
    await: true
  });
}
exports.addActivity = addActivity;
exports.getActivityInfo = getActivityInfo;
exports.getPageActivity = getPageActivity;
