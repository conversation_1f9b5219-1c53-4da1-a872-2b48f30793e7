"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/market/getList`,
    method: "GET",
    data: params,
    await: true
  });
}
function getPopularList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/market/getPopularList`,
    method: "GET",
    data: params,
    await: true
  });
}
const getMarketDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/market/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const add = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/market/apply`,
    method: "POST",
    data: params,
    await: true
  });
};
function myApplyList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/market/myApplyList`,
    method: "GET",
    data: params,
    await: true
  });
}
function myApplyDelete(id) {
  return common_request.request({
    url: `${common_config.API_URL}/market/myApplyDelete/${id}`,
    method: "DELETE",
    await: true
  });
}
const shareDynamic = (id) => {
  return common_request.request({
    url: `${common_config.API_URL}/dataManage/shareActivity/${id}`,
    method: "GET"
  });
};
exports.add = add;
exports.getList = getList;
exports.getMarketDetails = getMarketDetails;
exports.getPopularList = getPopularList;
exports.myApplyDelete = myApplyDelete;
exports.myApplyList = myApplyList;
exports.shareDynamic = shareDynamic;
