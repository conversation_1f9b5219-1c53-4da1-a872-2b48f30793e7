"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/store/getList`,
    method: "GET",
    data: params,
    await: true
  });
}
function getPopularList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/store/getPopularList`,
    method: "GET",
    data: params,
    await: true
  });
}
const getDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/store/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const add = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/store/apply`,
    method: "POST",
    data: params,
    await: true
  });
};
function myApplyList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/store/myApplyList`,
    method: "GET",
    data: params,
    await: true
  });
}
function myApplyDelete(id) {
  return common_request.request({
    url: `${common_config.API_URL}/store/myApplyDelete/${id}`,
    method: "DELETE",
    await: true
  });
}
function createSession(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/conversations/created`,
    method: "POST",
    data: params,
    await: true
  });
}
function listMessage(id) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/conversations/listMessage/${id}`,
    method: "GET",
    await: true
  });
}
function sendMessage(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/conversations/sendMessage`,
    method: "POST",
    data: params,
    await: true
  });
}
function getConversationsList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/conversations/list`,
    method: "GET",
    data: params,
    await: true
  });
}
exports.add = add;
exports.createSession = createSession;
exports.getConversationsList = getConversationsList;
exports.getDetails = getDetails;
exports.getList = getList;
exports.getPopularList = getPopularList;
exports.listMessage = listMessage;
exports.myApplyDelete = myApplyDelete;
exports.myApplyList = myApplyList;
exports.sendMessage = sendMessage;
