"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/space/getList`,
    method: "GET",
    data: params,
    await: true
  });
}
function getPopularList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/space/getPopularList`,
    method: "GET",
    data: params,
    await: true
  });
}
const getDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/space/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
exports.getDetails = getDetails;
exports.getList = getList;
exports.getPopularList = getPopularList;
