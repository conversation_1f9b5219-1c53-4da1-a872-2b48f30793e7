"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
function getAllWorkers() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/getAllWorkers`,
    method: "GET"
  });
}
function getAllContact() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/getAllContact`,
    method: "GET"
  });
}
function callCommunity() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/callCommunity`,
    method: "POST"
  });
}
function callIsSubmit() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/callIsSubmit`,
    method: "POST"
  });
}
function sendMsg(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/sendMsg`,
    method: "POST",
    data: params
  });
}
function getPageMsg(workId, params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/getPageMsg/${workId}`,
    method: "GET",
    data: params
  });
}
exports.callCommunity = callCommunity;
exports.callIsSubmit = callIsSubmit;
exports.getAllContact = getAllContact;
exports.getAllWorkers = getAllWorkers;
exports.getPageMsg = getPageMsg;
exports.sendMsg = sendMsg;
