"use strict";
const common_vendor = require("../vendor.js");
const common_config = require("../config.js");
const common_request = require("../request.js");
const common_md5 = require("../md5.js");
function getToken() {
  return common_request.request({
    url: "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=NgB2Cb3JgHMsUTgFopSVF9LV&client_secret=zra6VUAPiYaWwcLLxvx43QCYQeg4vu2i",
    method: "POST"
  });
}
async function parsePhoto(base64) {
  let token = (await getToken()).access_token;
  console.log(token);
  let url = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_seg?access_token=" + token;
  return common_request.request({
    url,
    method: "POST",
    data: {
      image: encodeURI(base64.replace(/^data:image\/\w+;base64,/, "")),
      type: "foreground"
    },
    header: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    await: true
  });
}
function base64tofile(base64Str) {
  return new Promise((resolve, reject) => {
    let base64Image = base64Str.split(",")[1].replace(/[\r\n]/g, "");
    const fsm = common_vendor.wx$1.getFileSystemManager();
    const format = "png";
    const timestamp = new Date().getTime();
    const buffer = common_vendor.wx$1.base64ToArrayBuffer(base64Image), filePath = `${common_vendor.wx$1.env.USER_DATA_PATH}/${timestamp}share.${format}`;
    fsm.writeFile({
      filePath,
      data: buffer,
      encoding: "binary",
      success(res) {
        common_vendor.wx$1.getImageInfo({
          src: filePath,
          success: function(res2) {
            let img = res2.path;
            resolve(img);
            reject();
          },
          fail(e) {
            console.log("读取图片报错");
            console.log(e);
          },
          error(res2) {
            console.log(res2);
          }
        });
      },
      fail(e) {
        console.log(e);
      }
    });
  }).catch((e) => {
    console.log(e);
  });
}
function getLocation(params) {
  const data = `extensions=all&key=${common_config.KEY}&location=${params.longitude},${params.latitude}&output=json&radius=1000`;
  const sig = common_md5.md5.hex_md5(data + common_config.PRIVATE_KEY);
  return common_request.request({
    url: `https://restapi.amap.com/v3/geocode/regeo?${data}&sig=${sig}`,
    method: "POST",
    await: true
  });
}
function getAllDistrictStreet(code) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/district/getAllDistrictStreet`,
    method: "GET",
    data: { code }
  });
}
function getSysActivityLabel() {
  return common_request.request({
    url: `${common_config.API_URL}/system/dict/data/type/sys_activity_lable`,
    method: "GET"
  });
}
function getSysTargetAudience() {
  return common_request.request({
    url: `${common_config.API_URL}/system/dict/data/type/sys_target_audience`,
    method: "GET"
  });
}
function getSysQuestionnaireLabel() {
  return common_request.request({
    url: `${common_config.API_URL}/system/dict/data/type/sys_questionnaire_type`,
    method: "GET"
  });
}
function getSysQuestionnaireDepartment() {
  return common_request.request({
    url: `${common_config.API_URL}/system/dict/data/type/sys_questionnaire_department`,
    method: "GET"
  });
}
exports.base64tofile = base64tofile;
exports.getAllDistrictStreet = getAllDistrictStreet;
exports.getLocation = getLocation;
exports.getSysActivityLabel = getSysActivityLabel;
exports.getSysQuestionnaireDepartment = getSysQuestionnaireDepartment;
exports.getSysQuestionnaireLabel = getSysQuestionnaireLabel;
exports.getSysTargetAudience = getSysTargetAudience;
exports.parsePhoto = parsePhoto;
