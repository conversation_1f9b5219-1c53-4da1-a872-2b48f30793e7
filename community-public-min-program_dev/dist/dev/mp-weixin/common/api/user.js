"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_config = require("../config.js");
const common_request = require("../request.js");
require("../vendor.js");
function getOpenId(params) {
  return common_request.request({
    url: `${common_config.API_URL}/auth/appletAuth`,
    method: "POST",
    data: params
  });
}
function appletRegister(params) {
  return common_request.request({
    url: `${common_config.API_URL}/auth/appletRegister`,
    method: "POST",
    data: params
  });
}
function getUserInfo() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/resident/getUserInfo`,
    method: "GET"
  });
}
function updateUserInfo(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/resident/updateUserInfo`,
    method: "POST",
    data: params
  });
}
function getMessageList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/message/list`,
    method: "GET",
    data: params
  });
}
function addFeedback(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/userFeedback/add`,
    method: "POST",
    data: params,
    await: true
  });
}
function getNewest() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/notice/getNewest`,
    method: "GET"
  });
}
function getDetails(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/notice/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
}
function authentication(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/resident/authentication`,
    method: "POST",
    data: params
  });
}
function bindIdNumber(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/resident/idNumber`,
    method: "POST",
    data: params,
    await: true
  });
}
function getVillage(params) {
  return common_request.request({
    url: `${common_config.API_URL}/applet/community/getVillage`,
    method: "GET",
    data: params,
    await: true
  });
}
function getPhoneNumber(params) {
  return common_request.request({
    url: `${common_config.API_URL}/auth/getPhoneNumber`,
    method: "GET",
    data: params,
    await: true
  });
}
function getRoleListByUser() {
  return common_request.request({
    url: `${common_config.API_URL}/applet/menu/getRoleListByUser`,
    method: "GET",
    await: true
  });
}
exports.addFeedback = addFeedback;
exports.appletRegister = appletRegister;
exports.authentication = authentication;
exports.bindIdNumber = bindIdNumber;
exports.getDetails = getDetails;
exports.getMessageList = getMessageList;
exports.getNewest = getNewest;
exports.getOpenId = getOpenId;
exports.getPhoneNumber = getPhoneNumber;
exports.getRoleListByUser = getRoleListByUser;
exports.getUserInfo = getUserInfo;
exports.getVillage = getVillage;
exports.updateUserInfo = updateUserInfo;
