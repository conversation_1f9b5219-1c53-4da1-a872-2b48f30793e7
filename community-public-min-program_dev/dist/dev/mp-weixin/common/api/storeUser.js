"use strict";
const common_config = require("../config.js");
const common_request = require("../request.js");
const getDetails = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/storeUser/getDetails`,
    method: "GET",
    data: params,
    await: true
  });
};
const apply = (params) => {
  return common_request.request({
    url: `${common_config.API_URL}/storeUser/apply`,
    method: "POST",
    data: params,
    await: true
  });
};
const checkAuth = () => {
  return common_request.request({
    url: `${common_config.API_URL}/storeUser/checkAuth`,
    method: "POST",
    await: true
  });
};
function myApplyList(params) {
  return common_request.request({
    url: `${common_config.API_URL}/storeUser/myApplyList`,
    method: "GET",
    data: params,
    await: true
  });
}
exports.apply = apply;
exports.checkAuth = checkAuth;
exports.getDetails = getDetails;
exports.myApplyList = myApplyList;
