"use strict";
const common_vendor = require("./vendor.js");
const common_config = require("./config.js");
function request(params) {
  const withBaseURL = params.url.indexOf("http") == 0;
  params.url = withBaseURL ? params.url : common_config.API_URL + params.url;
  const token = common_vendor.index.getStorageSync("token");
  if (params.header) {
    params.header.clientid = common_config.CLIENT_ID;
  } else {
    params.header = {
      clientid: common_config.CLIENT_ID
    };
  }
  if (token) {
    params.header.Authorization = token;
  }
  if (params.await) {
    common_vendor.index.showLoading({
      title: "请稍后…",
      mask: true
    });
  }
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      url: params.url,
      method: params.method || "GET",
      data: params.data || "",
      header: params.header || {},
      success: (res) => {
        if (params.await) {
          common_vendor.index.hideLoading();
        }
        const responseData = res.data;
        const code = responseData.code;
        if (code === 401) {
          common_vendor.index.removeStorageSync("token");
          common_vendor.index.showModal({
            title: "",
            content: "您还未登录，是否去登录？",
            confirmText: "去登录",
            cancelText: "取消",
            confirmColor: "#07C160",
            success: (modalRes) => {
              if (modalRes.confirm) {
                common_vendor.index.reLaunch({
                  url: "/pages/auth/login/login"
                });
              }
              reject(responseData);
            },
            fail: () => {
              reject(responseData);
            }
          });
        } else if (code === 500) {
          common_vendor.index.showToast({
            icon: "none",
            title: responseData.msg ? responseData.msg : "请求异常，请稍后再试",
            position: "bottom"
          });
        } else {
          resolve(responseData);
        }
      },
      fail: (err) => {
        if (params.await) {
          common_vendor.index.hideLoading();
        }
        common_vendor.index.showToast({
          icon: "none",
          title: "服务器错误，请稍后再试",
          position: "bottom"
        });
      }
    });
  });
}
exports.request = request;
