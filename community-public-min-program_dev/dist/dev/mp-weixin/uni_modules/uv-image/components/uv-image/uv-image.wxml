<uv-transition wx:if="{{a}}" class="data-v-d4d4d927" u-s="{{['d']}}" u-i="d4d4d927-0" bind:__l="__l" u-p="{{A}}"><view class="{{['uv-image', 'data-v-d4d4d927', w]}}" bindtap="{{x}}" style="{{y + ';' + z}}"><image wx:if="{{b}}" src="{{c}}" mode="{{d}}" binderror="{{e}}" bindload="{{f}}" show-menu-by-longpress="{{g}}" lazy-load="{{h}}" class="uv-image__image data-v-d4d4d927" style="{{i}}" webp="{{j}}"></image><view wx:if="{{k}}" class="uv-image__loading data-v-d4d4d927" style="{{'border-radius:' + m + ';' + ('background-color:' + n) + ';' + ('width:' + o) + ';' + ('height:' + p)}}"><block wx:if="{{$slots.loading}}"><slot name="loading"></slot></block><block wx:else><uv-icon wx:if="{{l}}" class="data-v-d4d4d927" u-i="d4d4d927-1,d4d4d927-0" bind:__l="__l" u-p="{{l}}"></uv-icon></block></view><view wx:if="{{q}}" class="uv-image__error data-v-d4d4d927" style="{{'border-radius:' + s + ';' + ('width:' + t) + ';' + ('height:' + v)}}"><block wx:if="{{$slots.error}}"><slot name="error"></slot></block><block wx:else><uv-icon wx:if="{{r}}" class="data-v-d4d4d927" u-i="d4d4d927-2,d4d4d927-0" bind:__l="__l" u-p="{{r}}"></uv-icon></block></view></view></uv-transition>