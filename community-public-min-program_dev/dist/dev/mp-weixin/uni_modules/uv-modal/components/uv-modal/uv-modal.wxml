<uv-popup wx:if="{{A}}" class="r data-v-a3fffacb" u-s="{{['d']}}" u-r="modalPopup" bindchange="{{z}}" u-i="a3fffacb-0" bind:__l="__l" u-p="{{A}}"><view class="uv-modal data-v-a3fffacb" style="{{'width:' + x}}"><text wx:if="{{a}}" class="uv-modal__title data-v-a3fffacb">{{b}}</text><view class="uv-modal__content data-v-a3fffacb" style="{{'padding-top:' + g}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><text class="uv-modal__content__text data-v-a3fffacb" style="{{d + ';' + e + ';' + f}}">{{c}}</text></block></view><block wx:if="{{$slots.confirmButton}}"><slot name="confirmButton"></slot></block><block wx:else><uv-line class="data-v-a3fffacb" u-i="a3fffacb-1,a3fffacb-0" bind:__l="__l"></uv-line><view wx:if="{{h}}" class="uv-modal__button-group data-v-a3fffacb" style="{{'flex-direction:' + w}}"><view wx:if="{{i}}" hover-stay-time="{{150}}" hover-class="uv-modal__button-group__wrapper--hover" class="{{['uv-modal__button-group__wrapper', 'uv-modal__button-group__wrapper--cancel', 'data-v-a3fffacb', l]}}" bindtap="{{m}}"><text class="uv-modal__button-group__wrapper__text data-v-a3fffacb" style="{{'color:' + k}}">{{j}}</text></view><uv-line wx:if="{{n}}" class="data-v-a3fffacb" u-i="a3fffacb-2,a3fffacb-0" bind:__l="__l" u-p="{{o}}"></uv-line><view wx:if="{{p}}" hover-stay-time="{{150}}" hover-class="uv-modal__button-group__wrapper--hover" class="{{['uv-modal__button-group__wrapper', 'uv-modal__button-group__wrapper--confirm', 'data-v-a3fffacb', t]}}" bindtap="{{v}}"><uv-loading-icon wx:if="{{q}}" class="data-v-a3fffacb" u-i="a3fffacb-3,a3fffacb-0" bind:__l="__l"></uv-loading-icon><text wx:else class="uv-modal__button-group__wrapper__text data-v-a3fffacb" style="{{'color:' + s}}">{{r}}</text></view></view></block></view></uv-popup>