/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-a3fffacb, scroll-view.data-v-a3fffacb, swiper-item.data-v-a3fffacb {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-modal.data-v-a3fffacb {
  width: 650rpx;
  border-radius: 6px;
  overflow: hidden;
}
.uv-modal__title.data-v-a3fffacb {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  text-align: center;
  padding-top: 25px;
}
.uv-modal__content.data-v-a3fffacb {
  padding: 12px 25px 25px 25px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.uv-modal__content__text.data-v-a3fffacb {
  line-height: 48rpx;
  font-size: 15px;
  color: #606266;
  flex: 1;
}
.uv-modal__button-group.data-v-a3fffacb {
  display: flex;
  flex-direction: row;
  height: 48px;
}
.uv-modal__button-group__wrapper.data-v-a3fffacb {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 48px;
}
.uv-modal__button-group__wrapper--confirm.data-v-a3fffacb, .uv-modal__button-group__wrapper--only-cancel.data-v-a3fffacb {
  border-bottom-right-radius: 6px;
}
.uv-modal__button-group__wrapper--cancel.data-v-a3fffacb, .uv-modal__button-group__wrapper--only-confirm.data-v-a3fffacb {
  border-bottom-left-radius: 6px;
}
.uv-modal__button-group__wrapper--hover.data-v-a3fffacb {
  background-color: #f3f4f6;
}
.uv-modal__button-group__wrapper__text.data-v-a3fffacb {
  color: #606266;
  font-size: 16px;
  text-align: center;
}