/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-1dcca93c, scroll-view.data-v-1dcca93c, swiper-item.data-v-1dcca93c {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-swiper-indicator__wrapper.data-v-1dcca93c {
  display: flex;
  flex-direction: row;
}
.uv-swiper-indicator__wrapper--line.data-v-1dcca93c {
  border-radius: 100px;
  height: 4px;
}
.uv-swiper-indicator__wrapper--line__bar.data-v-1dcca93c {
  width: 22px;
  height: 4px;
  border-radius: 100px;
  background-color: #FFFFFF;
  transition: transform 0.3s;
}
.uv-swiper-indicator__wrapper__dot.data-v-1dcca93c {
  width: 5px;
  height: 5px;
  border-radius: 100px;
  margin: 0 4px;
}
.uv-swiper-indicator__wrapper__dot--active.data-v-1dcca93c {
  width: 12px;
}