/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uv-popup.data-v-2ee1c78c {
  position: fixed;
  z-index: 99;
}
.uv-popup.top.data-v-2ee1c78c, .uv-popup.left.data-v-2ee1c78c, .uv-popup.right.data-v-2ee1c78c {
  top: 0;
}
.uv-popup .uv-popup__content.data-v-2ee1c78c {
  display: block;
  overflow: hidden;
  position: relative;
}
.uv-popup .uv-popup__content.left.data-v-2ee1c78c, .uv-popup .uv-popup__content.right.data-v-2ee1c78c {
  padding-top: 0;
  flex: 1;
}
.uv-popup .uv-popup__content__close.data-v-2ee1c78c {
  position: absolute;
}
.uv-popup .uv-popup__content__close--hover.data-v-2ee1c78c {
  opacity: 0.4;
}
.uv-popup .uv-popup__content__close--top-left.data-v-2ee1c78c {
  top: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--top-right.data-v-2ee1c78c {
  top: 15px;
  right: 15px;
}
.uv-popup .uv-popup__content__close--bottom-left.data-v-2ee1c78c {
  bottom: 15px;
  left: 15px;
}
.uv-popup .uv-popup__content__close--bottom-right.data-v-2ee1c78c {
  right: 15px;
  bottom: 15px;
}
.fixforpc-z-index.data-v-2ee1c78c {
  z-index: 999;
}
.fixforpc-top.data-v-2ee1c78c {
  top: 0;
}