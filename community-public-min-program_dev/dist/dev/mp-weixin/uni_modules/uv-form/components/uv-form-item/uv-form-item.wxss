/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-333bc9b4, scroll-view.data-v-333bc9b4, swiper-item.data-v-333bc9b4 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-form-item.data-v-333bc9b4 {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #303133;
}
.uv-form-item__body.data-v-333bc9b4 {
  display: flex;
  flex-direction: row;
  padding: 10px 0;
}
.uv-form-item__body__left.data-v-333bc9b4 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-form-item__body__left__content.data-v-333bc9b4 {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 10rpx;
  flex: 1;
}
.uv-form-item__body__left__content__icon.data-v-333bc9b4 {
  margin-right: 8rpx;
}
.uv-form-item__body__left__content__required.data-v-333bc9b4 {
  position: absolute;
  left: -9px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}
.uv-form-item__body__left__content__label.data-v-333bc9b4 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  color: #303133;
  font-size: 15px;
}
.uv-form-item__body__right.data-v-333bc9b4 {
  flex: 1;
}
.uv-form-item__body__right__content.data-v-333bc9b4 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.uv-form-item__body__right__content__slot.data-v-333bc9b4 {
  flex: 1;
}
.uv-form-item__body__right__content__icon.data-v-333bc9b4 {
  margin-left: 10rpx;
  color: #c0c4cc;
  font-size: 30rpx;
}
.uv-form-item__body__right__message__box.data-v-333bc9b4 {
  height: 16px;
  line-height: 16px;
}
.uv-form-item__body__right__message.data-v-333bc9b4 {
  margin-top: -6px;
  line-height: 24px;
  font-size: 12px;
  color: #f56c6c;
}