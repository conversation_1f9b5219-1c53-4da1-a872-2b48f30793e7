/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uv-border.data-v-44f8fa45 {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.uv-border-bottom.data-v-44f8fa45 {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
view.data-v-44f8fa45, scroll-view.data-v-44f8fa45, swiper-item.data-v-44f8fa45 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-input.data-v-44f8fa45 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.uv-input--radius.data-v-44f8fa45, .uv-input--square.data-v-44f8fa45 {
  border-radius: 4px;
}
.uv-input--no-radius.data-v-44f8fa45 {
  border-radius: 0;
}
.uv-input--circle.data-v-44f8fa45 {
  border-radius: 100px;
}
.uv-input__content.data-v-44f8fa45 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uv-input__content__field-wrapper.data-v-44f8fa45 {
  position: relative;
  display: flex;
  flex-direction: row;
  margin: 0;
  flex: 1;
}
.uv-input__content__field-wrapper__field.data-v-44f8fa45 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.uv-input__content__clear.data-v-44f8fa45 {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.82);
  margin-left: 15px;
}
.uv-input__content__subfix-icon.data-v-44f8fa45 {
  margin-left: 4px;
}
.uv-input__content__prefix-icon.data-v-44f8fa45 {
  margin-right: 4px;
}