<view class="{{['uv-input', 'data-v-44f8fa45', I]}}" style="{{J}}"><view class="uv-input__content data-v-44f8fa45"><view class="uv-input__content__prefix-icon data-v-44f8fa45"><block wx:if="{{$slots.prefix}}"><slot name="prefix"></slot></block><block wx:else><uv-icon wx:if="{{a}}" class="data-v-44f8fa45" u-i="44f8fa45-0" bind:__l="__l" u-p="{{b}}"></uv-icon></block></view><view class="uv-input__content__field-wrapper data-v-44f8fa45" bindtap="{{C}}"><block wx:if="{{r0}}"><input class="uv-input__content__field-wrapper__field data-v-44f8fa45" style="{{c}}" type="{{d}}" focus="{{e}}" cursor="{{f}}" value="{{g}}" auto-blur="{{h}}" disabled="{{i}}" maxlength="{{j}}" placeholder="{{k}}" placeholder-style="{{l}}" placeholder-class="{{m}}" confirm-type="{{n}}" confirm-hold="{{o}}" hold-keyboard="{{p}}" cursor-spacing="{{q}}" adjust-position="{{r}}" selection-end="{{s}}" selection-start="{{t}}" password="{{v}}" ignoreCompositionEvent="{{w}}" bindinput="{{x}}" bindblur="{{y}}" bindfocus="{{z}}" bindconfirm="{{A}}" bindkeyboardheightchange="{{B}}"/></block></view><view wx:if="{{D}}" class="uv-input__content__clear data-v-44f8fa45" bindtap="{{F}}"><uv-icon wx:if="{{E}}" class="data-v-44f8fa45" u-i="44f8fa45-1" bind:__l="__l" u-p="{{E}}"></uv-icon></view><view class="uv-input__content__subfix-icon data-v-44f8fa45"><block wx:if="{{$slots.suffix}}"><slot name="suffix"></slot></block><block wx:else><uv-icon wx:if="{{G}}" class="data-v-44f8fa45" u-i="44f8fa45-2" bind:__l="__l" u-p="{{H}}"></uv-icon></block></view></view></view>