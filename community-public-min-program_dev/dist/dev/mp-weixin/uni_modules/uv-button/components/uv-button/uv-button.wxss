/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uv-reset-button.data-v-fe288ac1 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.uv-reset-button.data-v-fe288ac1::after {
  border: none;
}
view.data-v-fe288ac1, scroll-view.data-v-fe288ac1, swiper-item.data-v-fe288ac1 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-button-wrapper.data-v-fe288ac1 {
  position: relative;
}
.uv-button-wrapper--dis.data-v-fe288ac1 {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9;
}
.uv-button.data-v-fe288ac1 {
  width: 100%;
}
.uv-button__text.data-v-fe288ac1 {
  white-space: nowrap;
  line-height: 1;
}
.uv-button.data-v-fe288ac1:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 0;
  content: " ";
  background-color: #000;
  border-color: #000;
}
.uv-button--active.data-v-fe288ac1:before {
  opacity: 0.15;
}
.uv-button__icon + .uv-button__text.data-v-fe288ac1:not(:empty), .uv-button__loading-text.data-v-fe288ac1 {
  margin-left: 4px;
}
.uv-button--plain.uv-button--primary.data-v-fe288ac1 {
  color: #3c9cff;
}
.uv-button--plain.uv-button--info.data-v-fe288ac1 {
  color: #909399;
}
.uv-button--plain.uv-button--success.data-v-fe288ac1 {
  color: #5ac725;
}
.uv-button--plain.uv-button--error.data-v-fe288ac1 {
  color: #f56c6c;
}
.uv-button--plain.uv-button--warning.data-v-fe288ac1 {
  color: #f9ae3d;
}
.uv-button.data-v-fe288ac1 {
  height: 40px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
.uv-button__text.data-v-fe288ac1 {
  font-size: 15px;
}
.uv-button__loading-text.data-v-fe288ac1 {
  font-size: 15px;
  margin-left: 4px;
}
.uv-button--large.data-v-fe288ac1 {
  width: 100%;
  height: 50px;
  padding: 0 15px;
}
.uv-button--normal.data-v-fe288ac1 {
  padding: 0 12px;
  font-size: 14px;
}
.uv-button--small.data-v-fe288ac1 {
  min-width: 60px;
  height: 30px;
  padding: 0px 8px;
  font-size: 12px;
}
.uv-button--mini.data-v-fe288ac1 {
  height: 22px;
  font-size: 10px;
  min-width: 50px;
  padding: 0px 8px;
}
.uv-button--disabled.data-v-fe288ac1 {
  opacity: 0.5;
}
.uv-button--info.data-v-fe288ac1 {
  color: #323233;
  background-color: #fff;
  border-color: #ebedf0;
  border-width: 1px;
  border-style: solid;
}
.uv-button--success.data-v-fe288ac1 {
  color: #fff;
  background-color: #5ac725;
  border-color: #5ac725;
  border-width: 1px;
  border-style: solid;
}
.uv-button--primary.data-v-fe288ac1 {
  color: #fff;
  background-color: #3c9cff;
  border-color: #3c9cff;
  border-width: 1px;
  border-style: solid;
}
.uv-button--error.data-v-fe288ac1 {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  border-width: 1px;
  border-style: solid;
}
.uv-button--warning.data-v-fe288ac1 {
  color: #fff;
  background-color: #f9ae3d;
  border-color: #f9ae3d;
  border-width: 1px;
  border-style: solid;
}
.uv-button--block.data-v-fe288ac1 {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.uv-button--circle.data-v-fe288ac1 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
}
.uv-button--square.data-v-fe288ac1 {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.uv-button__icon.data-v-fe288ac1 {
  min-width: 1em;
  line-height: inherit !important;
  vertical-align: top;
}
.uv-button--plain.data-v-fe288ac1 {
  background-color: #fff;
}
.uv-button--hairline.data-v-fe288ac1 {
  border-width: 0.5px !important;
}