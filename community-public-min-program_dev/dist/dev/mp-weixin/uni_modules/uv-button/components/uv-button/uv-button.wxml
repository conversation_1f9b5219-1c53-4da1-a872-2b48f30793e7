<view class="uv-button-wrapper data-v-fe288ac1" style="{{Q}}"><view wx:if="{{a}}" class="uv-button-wrapper--dis data-v-fe288ac1"></view><button hover-start-time="{{l}}" hover-stay-time="{{m}}" form-type="{{n}}" open-type="{{o}}" app-parameter="{{p}}" hover-stop-propagation="{{q}}" send-message-title="{{r}}" send-message-path="{{s}}" lang="{{t}}" data-name="{{v}}" session-from="{{w}}" send-message-img="{{x}}" show-message-card="{{y}}" bindgetphonenumber="{{z}}" bindgetuserinfo="{{A}}" binderror="{{B}}" bindopensetting="{{C}}" bindlaunchapp="{{D}}" bindcontact="{{E}}" bindchooseavatar="{{F}}" bindagreeprivacyauthorization="{{G}}" bindaddgroupapp="{{H}}" bindchooseaddress="{{I}}" bindsubscribe="{{J}}" bindlogin="{{K}}" bindim="{{L}}" hover-class="uv-button--active" style="{{M + ';' + N}}" bindtap="{{O}}" class="{{['uv-button', 'uv-reset-button', 'data-v-fe288ac1', P]}}"><block wx:if="{{b}}"><uv-loading-icon wx:if="{{c}}" class="data-v-fe288ac1" u-i="fe288ac1-0" bind:__l="__l" u-p="{{c}}"></uv-loading-icon><text class="uv-button__loading-text data-v-fe288ac1" style="{{e + ';' + f}}">{{d}}</text></block><block wx:else><uv-icon wx:if="{{g}}" class="data-v-fe288ac1" u-i="fe288ac1-1" bind:__l="__l" u-p="{{h}}"></uv-icon><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><text class="uv-button__text data-v-fe288ac1" style="{{j + ';' + k}}">{{i}}</text></block><slot name="suffix"></slot></block></button></view>