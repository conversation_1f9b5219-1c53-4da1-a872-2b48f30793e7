/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-6500c6eb, scroll-view.data-v-6500c6eb, swiper-item.data-v-6500c6eb {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-badge.data-v-6500c6eb {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  display: flex;
  flex-direction: row;
  line-height: 11px;
  text-align: center;
  font-size: 11px;
  color: #FFFFFF;
}
.uv-badge--dot.data-v-6500c6eb {
  height: 8px;
  width: 8px;
}
.uv-badge--inverted.data-v-6500c6eb {
  font-size: 13px;
}
.uv-badge--not-dot.data-v-6500c6eb {
  padding: 2px 5px;
}
.uv-badge--horn.data-v-6500c6eb {
  border-bottom-left-radius: 0;
}
.uv-badge--primary.data-v-6500c6eb {
  background-color: #3c9cff;
}
.uv-badge--primary--inverted.data-v-6500c6eb {
  color: #3c9cff;
}
.uv-badge--error.data-v-6500c6eb {
  background-color: #f56c6c;
}
.uv-badge--error--inverted.data-v-6500c6eb {
  color: #f56c6c;
}
.uv-badge--success.data-v-6500c6eb {
  background-color: #5ac725;
}
.uv-badge--success--inverted.data-v-6500c6eb {
  color: #5ac725;
}
.uv-badge--info.data-v-6500c6eb {
  background-color: #909399;
}
.uv-badge--info--inverted.data-v-6500c6eb {
  color: #909399;
}
.uv-badge--warning.data-v-6500c6eb {
  background-color: #f9ae3d;
}
.uv-badge--warning--inverted.data-v-6500c6eb {
  color: #f9ae3d;
}