<uv-popup wx:if="{{o}}" class="r data-v-9bd00d10" u-s="{{['d']}}" u-r="pickerPopup" bindchange="{{n}}" u-i="9bd00d10-0" bind:__l="__l" u-p="{{o}}"><view class="uv-picker data-v-9bd00d10"><uv-toolbar wx:if="{{a}}" class="data-v-9bd00d10" bindcancel="{{b}}" bindconfirm="{{c}}" u-i="9bd00d10-1,9bd00d10-0" bind:__l="__l" u-p="{{d}}"></uv-toolbar><picker-view class="uv-picker__view data-v-9bd00d10" indicatorStyle="{{f}}" value="{{g}}" immediateChange="{{h}}" style="{{'height:' + i}}" bindchange="{{j}}"><picker-view-column wx:for="{{e}}" wx:for-item="item" wx:key="c" class="uv-picker__view__column data-v-9bd00d10"><block wx:if="{{item.a}}"><text wx:for="{{item.b}}" wx:for-item="item1" wx:key="b" class="uv-picker__view__column__item uv-line-1 data-v-9bd00d10" style="{{item1.c + ';' + item1.d}}">{{item1.a}}</text></block></picker-view-column></picker-view><view wx:if="{{k}}" class="uv-picker--loading data-v-9bd00d10"><uv-loading-icon wx:if="{{l}}" class="data-v-9bd00d10" u-i="9bd00d10-2,9bd00d10-0" bind:__l="__l" u-p="{{l}}"></uv-loading-icon></view></view></uv-popup>