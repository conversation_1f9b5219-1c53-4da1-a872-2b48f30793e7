<view bindtap="{{s}}" class="{{['uv-icon', 'data-v-5ca9e013', t]}}"><image wx:if="{{a}}" class="uv-icon__img data-v-5ca9e013" src="{{b}}" mode="{{c}}" style="{{d + ';' + e}}"></image><text wx:else class="{{['uv-icon__icon', 'data-v-5ca9e013', g]}}" style="{{h + ';' + i}}" hover-class="{{j}}">{{f}}</text><text wx:if="{{k}}" class="uv-icon__label data-v-5ca9e013" style="{{'color:' + m + ';' + ('font-size:' + n) + ';' + ('margin-left:' + o) + ';' + ('margin-top:' + p) + ';' + ('margin-right:' + q) + ';' + ('margin-bottom:' + r)}}">{{l}}</text></view>