"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "webview",
  setup(__props) {
    const webviewUrl = common_vendor.ref("");
    const pageTitle = common_vendor.ref("加载中...");
    common_vendor.onLoad((options) => {
      if (options.url) {
        webviewUrl.value = decodeURIComponent(options.url);
      }
      if (options.title) {
        pageTitle.value = decodeURIComponent(options.title);
        common_vendor.index.setNavigationBarTitle({
          title: pageTitle.value
        });
      }
    });
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function handleMessage(event) {
      console.log("webview message:", event);
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "arrow-left",
          color: "#333",
          size: "20"
        }),
        b: common_vendor.o(goBack),
        c: common_vendor.t(pageTitle.value),
        d: webviewUrl.value,
        e: common_vendor.o(handleMessage)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e9fdf7dd"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/common/webview/webview.vue"]]);
wx.createPage(MiniProgramPage);
