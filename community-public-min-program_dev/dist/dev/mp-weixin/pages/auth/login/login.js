"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const stores_store = require("../../../stores/store.js");
const common_api_user = require("../../../common/api/user.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_avatar2 = common_vendor.resolveComponent("uv-avatar");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_uv_icon2 + _easycom_uv_avatar2 + _easycom_uv_form_item2 + _easycom_uv_input2 + _easycom_uv_form2 + _easycom_uv_button2 + _easycom_uv_popup2)();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_avatar = () => "../../../uni_modules/uv-avatar/components/uv-avatar/uv-avatar.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_popup = () => "../../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_uv_avatar + _easycom_uv_form_item + _easycom_uv_input + _easycom_uv_form + _easycom_uv_button + _easycom_uv_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const agreePrivacy = common_vendor.ref(false);
    const authPopup = common_vendor.ref();
    const formRef = common_vendor.ref();
    const userInfoObj = common_vendor.ref({});
    const form = common_vendor.reactive({
      avatar: "",
      phone: ""
    });
    const rules = Object.freeze({
      avatar: {
        type: "string",
        required: true,
        message: "请上传您的头像",
        trigger: ["blur", "change"]
      },
      phone: {
        type: "string",
        required: true,
        message: "请填写您的电话号码",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      }
    });
    common_vendor.onLoad(() => {
    });
    function toggleAgreement() {
      agreePrivacy.value = !agreePrivacy.value;
    }
    async function wxAuth() {
      var _a;
      if (!agreePrivacy.value) {
        common_vendor.index.showToast({
          title: "请先同意服务协议和隐私政策",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在登录中...",
        mask: true
      });
      try {
        const code = await getLoginCode();
        const loginInfo = await getLoginToken(code);
        console.log(loginInfo, "loginInfo");
        let token = loginInfo.access_token;
        if (!token) {
          common_vendor.index.hideLoading();
          (_a = authPopup.value) == null ? void 0 : _a.open();
          userInfoObj.value.openid = loginInfo.openid;
          return;
        }
        common_vendor.index.setStorageSync("token", token);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "登录成功"
        });
        await getUserInfoByToken();
        setTimeout(() => {
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }, 1e3);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "none"
        });
      }
    }
    function getLoginCode() {
      return new Promise((resolve, reject) => {
        common_vendor.index.login({
          success(response) {
            resolve(response.code);
          },
          fail(err) {
            reject(err);
          }
        });
      });
    }
    async function getLoginToken(code) {
      const res = await common_api_user.getOpenId({ xcxCode: code });
      if ((res == null ? void 0 : res.code) === 200) {
        return res.data;
      } else {
        throw new Error((res == null ? void 0 : res.msg) || "获取登录信息失败");
      }
    }
    async function getUserInfoByToken() {
      const token = common_vendor.index.getStorageSync("token");
      if (token) {
        const res = await common_api_user.getUserInfo();
        userStore.setUser(res.data);
        if (res.data) {
          common_vendor.index.setStorageSync("userInfo", res.data);
        }
      }
    }
    async function getAvatar(e) {
      const res = await uploadFile(e.detail.avatarUrl);
      form.avatar = res.url;
    }
    async function getphonenumber(e) {
      const res = await common_api_user.getPhoneNumber({ code: e.detail.code });
      if ((res == null ? void 0 : res.code) === 200) {
        form.phone = res.data;
      } else {
        throw new Error((res == null ? void 0 : res.msg) || "获取手机号失败");
      }
    }
    function uploadFile(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          fail: function(error) {
            reject(error);
          }
        });
      });
    }
    function confirmAuth() {
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "正在登录中...",
          mask: true
        });
        try {
          common_vendor.index.getUserProfile({
            desc: "用于完善会员资料",
            success: async (res) => {
              var _a;
              userStore.setUser(res.userInfo);
              const result2 = await register(userInfoObj.value.openid);
              common_vendor.index.setStorageSync("token", result2.access_token);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "登录成功"
              });
              (_a = authPopup.value) == null ? void 0 : _a.close();
              await getUserInfoByToken();
              setTimeout(() => {
                common_vendor.index.reLaunch({
                  url: "/pages/index/index"
                });
              }, 1e3);
            }
          });
        } catch (error) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
        }
      }).catch((err) => {
        console.log(err, "表单验证失败");
      });
    }
    async function refuseAuth() {
      var _a;
      common_vendor.index.showLoading({
        title: "正在登录中...",
        mask: true
      });
      try {
        const result = await common_api_user.appletRegister({
          openId: userInfoObj.value.openid,
          nickname: "",
          gender: 0,
          city: "",
          province: "",
          avatar: "",
          phone: ""
        });
        console.log(result);
        common_vendor.index.setStorageSync("token", result.data.access_token);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "登录成功"
        });
        (_a = authPopup.value) == null ? void 0 : _a.close();
        await getUserInfoByToken();
        setTimeout(() => {
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }, 1e3);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "none"
        });
      }
    }
    async function register(openid) {
      const res = await common_api_user.appletRegister({
        openId: openid,
        nickname: userStore.userInfo.nickName,
        gender: userStore.userInfo.gender,
        city: userStore.userInfo.city,
        province: userStore.userInfo.province,
        avatar: form.avatar,
        phone: form.phone
      });
      return res.data;
    }
    function openServiceAgreement() {
      const url = "https://qlzhsq.qlzhsq.cn:30210/agreement/user/";
      common_vendor.index.navigateTo({
        url: `/pages/common/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent("服务协议")}`
      });
    }
    function openPrivacyPolicy() {
      const url = "https://qlzhsq.qlzhsq.cn:30210/privacy/user/";
      common_vendor.index.navigateTo({
        url: `/pages/common/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent("隐私政策")}`
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(wxAuth),
        b: agreePrivacy.value
      }, agreePrivacy.value ? {
        c: common_vendor.p({
          name: "checkmark",
          color: "#ffffff",
          size: "10"
        })
      } : {}, {
        d: agreePrivacy.value ? 1 : "",
        e: common_vendor.o(openServiceAgreement),
        f: common_vendor.o(openPrivacyPolicy),
        g: common_vendor.o(toggleAgreement),
        h: common_vendor.p({
          src: form.avatar,
          size: "80"
        }),
        i: common_vendor.o(getAvatar),
        j: common_vendor.p({
          prop: "avatar"
        }),
        k: common_vendor.o(($event) => form.phone = $event),
        l: common_vendor.p({
          border: "surround",
          placeholder: "请授权您的电话",
          readonly: true,
          modelValue: form.phone
        }),
        m: common_vendor.o(getphonenumber),
        n: common_vendor.p({
          prop: "phone"
        }),
        o: common_vendor.sr(formRef, "49b47a98-2,49b47a98-1", {
          "k": "formRef"
        }),
        p: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        }),
        q: common_vendor.o(confirmAuth),
        r: common_vendor.p({
          type: "warning",
          text: "允许",
          customStyle: "margin-bottom: 10px;"
        }),
        s: common_vendor.o(refuseAuth),
        t: common_vendor.p({
          type: "default",
          text: "拒绝"
        }),
        v: common_vendor.sr(authPopup, "49b47a98-1", {
          "k": "authPopup"
        }),
        w: common_vendor.p({
          mode: "bottom",
          closeOnClickOverlay: false,
          closeable: false,
          round: "20"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-49b47a98"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/auth/login/login.vue"]]);
wx.createPage(MiniProgramPage);
