<view class="login-container data-v-49b47a98"><view class="login-content data-v-49b47a98"><view class="login-title data-v-49b47a98"><text class="data-v-49b47a98">登录崃生活</text></view><view class="login-buttons data-v-49b47a98"><view class="quick-login-btn data-v-49b47a98" bindtap="{{a}}"><text class="data-v-49b47a98">一键快捷登录</text></view></view><view class="agreement-section data-v-49b47a98"><view class="agreement-checkbox data-v-49b47a98" bindtap="{{g}}"><view class="{{['checkbox', 'data-v-49b47a98', d && 'checked']}}"><uv-icon wx:if="{{b}}" class="data-v-49b47a98" u-i="49b47a98-0" bind:__l="__l" u-p="{{c}}"></uv-icon></view><view class="agreement-text data-v-49b47a98"><text class="data-v-49b47a98">已阅读并同意</text><text class="agreement-link data-v-49b47a98" catchtap="{{e}}">《崃生活服务协议》</text><text class="data-v-49b47a98">和</text><text class="agreement-link data-v-49b47a98" catchtap="{{f}}">《隐私政策》</text><text class="data-v-49b47a98">允许崃生活统一管理本人账号信息</text></view></view></view></view><uv-popup wx:if="{{w}}" class="r data-v-49b47a98" u-s="{{['d']}}" u-r="authPopup" u-i="49b47a98-1" bind:__l="__l" u-p="{{w}}"><view class="auth-popup data-v-49b47a98"><view class="auth-header data-v-49b47a98"><text class="auth-title data-v-49b47a98">微信头像与电话授权</text></view><uv-form wx:if="{{p}}" class="r data-v-49b47a98" u-s="{{['d']}}" u-r="formRef" u-i="49b47a98-2,49b47a98-1" bind:__l="__l" u-p="{{p}}"><uv-form-item wx:if="{{j}}" class="data-v-49b47a98" u-s="{{['d']}}" u-i="49b47a98-3,49b47a98-2" bind:__l="__l" u-p="{{j}}"><button class="data-v-49b47a98" plain open-type="chooseAvatar" bindchooseavatar="{{i}}"><uv-avatar wx:if="{{h}}" class="data-v-49b47a98" u-i="49b47a98-4,49b47a98-3" bind:__l="__l" u-p="{{h}}"></uv-avatar></button></uv-form-item><uv-form-item wx:if="{{n}}" class="data-v-49b47a98" u-s="{{['d']}}" u-i="49b47a98-5,49b47a98-2" bind:__l="__l" u-p="{{n}}"><button class="data-v-49b47a98" plain open-type="getPhoneNumber" bindgetphonenumber="{{m}}"><uv-input wx:if="{{l}}" class="data-v-49b47a98" u-i="49b47a98-6,49b47a98-5" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"></uv-input></button></uv-form-item></uv-form><view class="auth-buttons data-v-49b47a98"><uv-button wx:if="{{r}}" class="data-v-49b47a98" bindtap="{{q}}" u-i="49b47a98-7,49b47a98-1" bind:__l="__l" u-p="{{r}}"></uv-button><uv-button wx:if="{{t}}" class="data-v-49b47a98" bindtap="{{s}}" u-i="49b47a98-8,49b47a98-1" bind:__l="__l" u-p="{{t}}"></uv-button></view></view></uv-popup></view>