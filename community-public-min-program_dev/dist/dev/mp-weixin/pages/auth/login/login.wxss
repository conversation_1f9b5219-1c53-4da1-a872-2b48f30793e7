/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-49b47a98 {
  min-height: 100vh;
  background: linear-gradient(180deg, #F5F3E7 0%, #E8DCC0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
}
.login-content.data-v-49b47a98 {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-title.data-v-49b47a98 {
  margin-bottom: 120rpx;
}
.login-title text.data-v-49b47a98 {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
}
.login-buttons.data-v-49b47a98 {
  width: 100%;
  margin-bottom: 80rpx;
}
.quick-login-btn.data-v-49b47a98 {
  width: 100%;
  height: 88rpx;
  background: #FF9F18;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}
.quick-login-btn text.data-v-49b47a98 {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}
.quick-login-btn.data-v-49b47a98:active {
  opacity: 0.8;
}
.phone-login-text.data-v-49b47a98 {
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  line-height: normal;
}
.phone-login-text.data-v-49b47a98::after {
  border: none;
}
.agreement-section.data-v-49b47a98 {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.agreement-checkbox.data-v-49b47a98 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  width: 100%;
  justify-content: center;
}
.checkbox.data-v-49b47a98 {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #cccccc;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.checkbox.checked.data-v-49b47a98 {
  background: #FF9F18;
  border-color: #FF9F18;
}
.agreement-text.data-v-49b47a98 {
  flex: 1;
  line-height: 36rpx;
}
.agreement-text text.data-v-49b47a98 {
  font-size: 24rpx;
  color: #666666;
}
.agreement-link.data-v-49b47a98 {
  color: #007AFF !important;
}
.agreement-desc.data-v-49b47a98 {
  text-align: center;
}
.agreement-desc text.data-v-49b47a98 {
  font-size: 22rpx;
  color: #999999;
}

/* 授权弹框样式 */
.auth-popup.data-v-49b47a98 {
  width: 100%;
  background: #ffffff;
  padding: 40rpx 32rpx 60rpx;
  box-sizing: border-box;
}
.auth-header.data-v-49b47a98 {
  text-align: center;
  margin-bottom: 32rpx;
}
.auth-title.data-v-49b47a98 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.auth-buttons.data-v-49b47a98 {
  margin-top: 40rpx;
}

/* 表单样式重写 */
.data-v-49b47a98 .uv-form-item__body {
  display: flex;
  flex-direction: row !important;
  justify-content: center;
  margin-bottom: 20rpx;
}
.data-v-49b47a98 .uv-form-item__body__left {
  width: auto !important;
  margin-right: 0 !important;
}
.data-v-49b47a98 .uv-form-item__body__right__message {
  margin-left: 0 !important;
  text-align: center;
}
.data-v-49b47a98 button {
  padding: 0;
  border: none;
  background: transparent;
  width: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
}