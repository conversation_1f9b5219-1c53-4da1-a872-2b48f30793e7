"use strict";
const common_vendor = require("../../common/vendor.js");
const common_utils_tab = require("../../common/utils/tab.js");
const common_enum = require("../../common/enum.js");
const stores_store = require("../../stores/store.js");
const common_api_activity = require("../../common/api/activity.js");
require("../../common/config.js");
const pages_activity_common = require("./common.js");
require("../../common/request.js");
require("../../common/api/system.js");
require("../../common/md5.js");
if (!Array) {
  const _easycom_uv_tabs2 = common_vendor.resolveComponent("uv-tabs");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_tabs2 + _easycom_uv_icon2 + _easycom_z_paging2)();
}
const _easycom_uv_tabs = () => "../../uni_modules/uv-tabs/components/uv-tabs/uv-tabs.js";
const _easycom_uv_icon = () => "../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_tabs + _easycom_uv_icon + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "activity",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const areaStore = stores_store.useAreaStore();
    const isShow = common_vendor.ref(false);
    const tabs = Object.freeze([
      { name: "全部", type: "all" },
      { name: "进行中", type: "ing" },
      { name: "已结束", type: "end" },
      { name: "我参与的", type: "participate" }
    ]);
    const searchObj = common_vendor.reactive({
      queryType: "all",
      districtCode: areaStore.areaCode,
      pageNum: 1,
      title: ""
    });
    const dataList = common_vendor.ref([]);
    const paging = common_vendor.ref();
    const scrollHeight = common_vendor.ref();
    let labelList = common_vendor.reactive({});
    function toPage(url, id = "") {
      if (!common_vendor.index.getStorageSync("token")) {
        goLogin();
        return;
      }
      common_vendor.index.navigateTo({
        url: url + `?id=${id}`
      });
    }
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 10;
    };
    const debounce = (fn, delay = 1e3) => {
      let timer;
      return function() {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(() => {
          fn();
          timer = null;
        }, delay);
      };
    };
    const searchKey = debounce(() => {
      searchObj.pageNum = 1;
      getPageList();
    }, 300);
    const changeTabs = (e) => {
      console.log(e);
      searchObj.queryType = e.type;
      searchObj.pageNum = 1;
      getPageList();
    };
    const queryList = (pageNo, pageSize) => {
      searchObj.pageNum = pageNo;
      getPageList();
    };
    const getPageList = async () => {
      const res = await common_api_activity.getPageActivity({
        ...searchObj,
        pageSize: 10
      });
      if ((res == null ? void 0 : res.code) === 200) {
        res.rows.map((item) => {
          item.labelText = labelList[item.label];
        });
        paging.value.complete(res.rows);
      } else {
        throw console.log(res);
      }
    };
    const goAuth = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未进行实名认证，是否去认证？",
        confirmText: "去认证",
        cancelText: "取消",
        confirmColor: "#2BBC4A",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/my/auth/auth"
            });
          }
        }
      });
    };
    const goLogin = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未登录，请先登录。",
        confirmText: "去登录",
        cancelText: "取消",
        confirmColor: "#2BBC4A",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.switchTab({
              url: "/pages/my/my"
            });
          }
        }
      });
    };
    common_vendor.onLoad(async () => {
      var _a;
      isShow.value = true;
      if (!common_vendor.index.getStorageSync("token")) {
        goLogin();
        return;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0) {
        goAuth();
        return;
      }
      if (common_vendor.index.getStorageSync("token")) {
        labelList = await pages_activity_common.getSysLabelList();
        getPageList();
      }
    });
    common_vendor.onShow(async () => {
      var _a;
      if (!common_vendor.index.getStorageSync("token") && isShow.value) {
        goLogin();
        return;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0 && isShow.value) {
        goAuth();
        return;
      }
      getScreenHeight();
      if (common_vendor.index.getStorageSync("isBack")) {
        searchObj.pageNum = 1;
        labelList = await pages_activity_common.getSysLabelList();
        getPageList();
        common_vendor.index.setStorageSync("isBack", false);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o([
          ($event) => searchObj.title = $event.detail.value,
          //@ts-ignore
          (...args) => common_vendor.unref(searchKey) && common_vendor.unref(searchKey)(...args)
        ]),
        b: searchObj.title,
        c: common_vendor.o(changeTabs),
        d: common_vendor.p({
          list: common_vendor.unref(tabs),
          lineColor: "#ffffff",
          activeStyle: {
            color: "#FFFFFF",
            fontSize: "40rpx",
            fontWeight: "bold"
          },
          inactiveStyle: {
            color: "#FFFFFF",
            fontSize: "32rpx"
          }
        }),
        e: common_vendor.f(dataList.value, (item, i, i0) => {
          return {
            a: item.coverImage,
            b: common_vendor.t(item.labelText),
            c: common_vendor.t(common_vendor.unref(pages_activity_common.handleStatus)(item.status).text),
            d: common_vendor.unref(pages_activity_common.handleStatus)(item.status).color,
            e: common_vendor.t(item.title),
            f: common_vendor.t(item.subtitle),
            g: "bf4bb263-2-" + i0 + ",bf4bb263-0",
            h: common_vendor.t(item.activityStartTime + "~" + item.activityEndTime),
            i: "bf4bb263-3-" + i0 + ",bf4bb263-0",
            j: common_vendor.t(item.location),
            k: common_vendor.f(item.registrationVos, (info, index, i1) => {
              return common_vendor.e(i < 6 ? {
                a: 26 * index - 2 + "px",
                b: index + 1,
                c: info.avatar
              } : {}, {
                d: index
              });
            }),
            l: i < 6,
            m: common_vendor.t((item == null ? void 0 : item.participate) ? item == null ? void 0 : item.participate : 0),
            n: common_vendor.o(($event) => toPage("/pages/subpackA/release/activity-details", item.activityId), i),
            o: i
          };
        }),
        f: common_vendor.p({
          name: "clock",
          color: "#999999",
          size: "14"
        }),
        g: common_vendor.p({
          name: "map",
          color: "#999999",
          size: "14"
        }),
        h: common_vendor.sr(paging, "bf4bb263-0", {
          "k": "paging"
        }),
        i: common_vendor.o(queryList),
        j: common_vendor.o(($event) => dataList.value = $event),
        k: common_vendor.p({
          ["paging-style"]: {
            "height": scrollHeight.value + "px"
          },
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        l: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).HOME,
        m: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME,
        n: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME ? "active" : ""),
        o: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).HOME)),
        p: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ASS,
        q: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS,
        r: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS ? "active" : ""),
        s: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ASS)),
        t: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        v: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        w: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND ? "active" : ""),
        x: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).FOUND)),
        y: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        z: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        A: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY ? "active" : ""),
        B: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY)),
        C: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).MY,
        D: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY,
        E: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY ? "active" : ""),
        F: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).MY))
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bf4bb263"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/activity/activity.vue"]]);
wx.createPage(MiniProgramPage);
