/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.time.data-v-bf4bb263, .location.data-v-bf4bb263 {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 40rpx;
}
.content_box.data-v-bf4bb263 {
  padding: 16rpx;
  box-sizing: border-box;
}
.overflow-hidden.data-v-bf4bb263 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.bg.data-v-bf4bb263 {
  width: 100%;
  height: 636rpx;
  background: url("https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/03/06/fa45fe37e96a49eaa29f240078a30ed2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: fixed;
}
.activity_view.data-v-bf4bb263 {
  padding: 22rpx 32rpx;
  width: 100%;
  height: 100vh;
  background: linear-gradient(#F5F5F5 100%, #F5F5F5 100%);
}
.activity_view .uv-input.data-v-bf4bb263 {
  width: 100%;
}
.activity_view .uv-input uv-input.data-v-bf4bb263 {
  width: 100% !important;
}
.uv-input.data-v-bf4bb263 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  box-sizing: border-box;
  padding: 0 16rpx;
  height: 60rpx;
  box-sizing: border-box;
  margin-bottom: 28rpx;
}
.custom-input.data-v-bf4bb263 {
  width: 100%;
  height: 100%;
  color: #333333;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: none;
  outline: none;
  padding: 15rpx 0;
}
.content_view.data-v-bf4bb263 {
  margin-top: 26rpx;
  padding: 0 32rpx;
}
.content_view .tab_content.data-v-bf4bb263 {
  margin-top: 32rpx;
  width: 100%;
}
.content_view .tab_content .mg_bottom.data-v-bf4bb263 {
  margin-bottom: 12rpx;
}
.content_view .tab_content .content_bg.data-v-bf4bb263 {
  margin-bottom: 16rpx;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 16rpx 16rpx;
}
.content_view .tab_content .img_bg.data-v-bf4bb263 {
  position: relative;
}
.content_view .tab_content .img_bg image.data-v-bf4bb263 {
  width: 686rpx;
  height: 280rpx;
}
.content_view .tab_content .img_bg .left_top.data-v-bf4bb263 {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  font-size: 20rpx;
  background: #FF9F18;
  color: #FFFFFF;
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
}
.content_view .tab_content .img_bg .right_top.data-v-bf4bb263 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
}
.content_view .tab_content .title.data-v-bf4bb263 {
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 44rpx;
  margin-bottom: 2rpx;
}
.content_view .tab_content .name.data-v-bf4bb263 {
  font-weight: 400;
  font-size: 24rpx;
  color: #CCCCCC;
  line-height: 40rpx;
}
.content_view .tab_content .remark.data-v-bf4bb263 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #999999;
  line-height: 46rpx;
  text-align: left;
  font-style: normal;
}

/* 全局样式，确保占位符文本样式 */
.white-placeholder {
  color: #ffffff !important;
}
