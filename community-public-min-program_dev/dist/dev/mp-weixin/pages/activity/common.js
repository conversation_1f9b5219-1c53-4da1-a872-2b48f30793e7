"use strict";
const common_api_system = require("../../common/api/system.js");
const handleStatus = (status) => {
  switch (status) {
    case "-1":
      return { text: "未开始", color: "#0f86d0" };
    case "0":
      return { text: "进行中", color: "#04B578" };
    case "1":
      return { text: "已结束", color: "#999999" };
  }
};
const getSysLabelList = async () => {
  let labelList = {};
  const res = await common_api_system.getSysActivityLabel();
  if ((res == null ? void 0 : res.code) === 200) {
    res.data.map((item) => {
      labelList[item.dictValue] = item.dictLabel;
    });
    return labelList;
  } else {
    throw console.log(res);
  }
};
const getSysAudience = async () => {
  let labelList = {};
  const res = await common_api_system.getSysTargetAudience();
  if ((res == null ? void 0 : res.code) === 200) {
    res.data.map((item) => {
      labelList[item.dictValue] = item.dictLabel;
    });
    return labelList;
  } else {
    throw console.log(res);
  }
};
exports.getSysAudience = getSysAudience;
exports.getSysLabelList = getSysLabelList;
exports.handleStatus = handleStatus;
