/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main-content {
  padding-bottom: 100rpx;
}
.flex_warp {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.user_icon {
  width: 96rpx;
  height: 108rpx;
  margin: 0 auto 8rpx;
  display: block;
  border-radius: 6px;
}
.right-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto;
  border-radius: 6px;
}
.top {
  padding: 8px 0 16px 0;
  text-align: center;
  color: #ffffff;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  display: flex;
  flex-direction: column;
}
.top .title {
  font-size: 24px;
}
.top .desc {
  font-size: 12px;
  font-style: normal;
  line-height: 20px;
}
.middle_user {
  margin-top: -8px;
  background: #FFFFFF;
  border-radius: 16px 16px 0px 0px;
  padding-top: 16px;
}
.middle_user .call_bg {
  background-image: url("https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/03/11/95cebcc204f34658b590d2b00e8483ab.png");
  background-repeat: no-repeat;
  width: 100%;
  height: 221rpx;
  background-size: 622rpx 100%;
  background-position-x: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 80rpx;
  box-sizing: border-box;
}
.middle_user .call_bg .call-text {
  font-weight: 500;
  font-size: 36rpx;
  color: #FF9F18;
  line-height: 52rpx;
  display: block;
}
.middle_user .call_bg .call-text-desc {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
}
.middle_user .grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8rpx;
  padding: 8rpx 10rpx;
  box-sizing: border-box;
  margin-top: 32rpx;
}
.middle_user .grid-item {
  text-align: center;
  font-style: normal;
  font-family: PingFangSC, PingFang SC;
  margin-bottom: 32rpx;
}
.middle_user .grid-item .user_title {
  width: 100%;
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 44rpx;
  display: block;
}
.middle_user .grid-item .user_bg {
  width: 100%;
}
.middle_user .grid-item .user_bg text {
  font-weight: 400;
  font-size: 20rpx;
  line-height: 32rpx;
  padding: 4rpx 8rpx;
  background: rgba(255, 159, 24, 0.2);
  border-radius: 4rpx;
  color: #FF9F18;
  line-height: 32rpx;
}
.middle_user .grid-item .user_desc {
  width: 155rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  display: block;
  line-height: 32rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.contact_view_ai {
  padding: 8rpx 32rpx;
  box-sizing: border-box;
}
.contact_view_ai .item {
  box-sizing: border-box;
  border-radius: 24rpx;
  padding: 30rpx 32rpx;
  background: rgb(255, 223, 190);
  display: flex;
  align-items: center;
}
.contact_view_ai .item .left {
  flex: 1;
}
.contact_view_ai .item .left .ai-title {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  margin-bottom: 4rpx;
}
.contact_view_ai .item .left .remark {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  line-height: 32rpx;
  display: block;
}
.contact_view_ai .item .head {
  display: flex;
  align-items: center;
}
.contact_view_ai .item .icon_min {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.contact_view_ai .item .name {
  font-weight: 500;
  font-size: 36rpx;
  color: #222222;
  line-height: 52rpx;
}
.contact_view_ai .item .right-icon {
  display: block;
  width: 81rpx;
  height: 81rpx;
}
.contact_view {
  width: 100%;
  padding: 8rpx 32rpx;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx 14rpx;
}
.contact_view .item {
  height: 108rpx;
  box-sizing: border-box;
  padding: 30rpx 24rpx;
  border-radius: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.contact_view .item image {
  border-radius: 10px;
}
.contact_view .left {
  width: 84%;
  font-family: PingFangSC, PingFang SC;
  font-style: normal;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.contact_view .left .icon_min {
  width: 48rpx;
  height: 48rpx;
  margin-right: 4rpx;
}
.contact_view .left .name {
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
}
.contact_view .left .remark {
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  line-height: 28px;
}