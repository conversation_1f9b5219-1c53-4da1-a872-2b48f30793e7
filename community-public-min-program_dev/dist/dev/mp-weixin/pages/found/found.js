"use strict";
const common_vendor = require("../../common/vendor.js");
const common_utils_tab = require("../../common/utils/tab.js");
const common_enum = require("../../common/enum.js");
const common_api_found = require("../../common/api/found.js");
const stores_store = require("../../stores/store.js");
require("../../common/config.js");
require("../../common/request.js");
if (!Array) {
  const _easycom_a_popup2 = common_vendor.resolveComponent("a-popup");
  _easycom_a_popup2();
}
const _easycom_a_popup = () => "../../components/a-popup/a-popup.js";
if (!Math) {
  _easycom_a_popup();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "found",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const scrollHeight = common_vendor.ref();
    let allWorker = common_vendor.ref([]);
    common_vendor.ref();
    let contactList = common_vendor.ref(), popup = common_vendor.ref(), isCall = common_vendor.ref(false);
    const contentCall = common_vendor.ref("您还未进行实名认证，是否去认证？"), cancelText = common_vendor.ref("取消"), confirmText = common_vendor.ref("已知晓"), isShow = common_vendor.ref(false);
    const flatWorkers = common_vendor.computed(() => {
      return allWorker.value.flat();
    });
    function authConfirm() {
      var _a;
      if (!common_vendor.index.getStorageSync("token")) {
        common_vendor.index.switchTab({
          url: "/pages/my/my"
        });
      } else {
        if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) !== 0) {
          return;
        }
        common_vendor.index.navigateTo({
          url: "/pages/my/auth/auth"
        });
      }
    }
    const toAi = () => {
      if (!isPass()) {
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/ai/ai"
      });
    };
    const toDetais = (item) => {
      if (!isPass()) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/subpackA/message/message?phone=${item.phone}&workersId=${item.workersId}&telephone=${item.telephone}&avatarUrl=${item.avatarUrl}&name=${item.name}&title=${item.title}&remark=${item.remark}`
      });
    };
    const callManage = async () => {
      var _a;
      if (!isPass()) {
        return;
      }
      const workerResult = await common_api_found.getAllWorkers();
      console.log("获取工作人员数据结果:", workerResult);
      if ((workerResult == null ? void 0 : workerResult.code) === 200 && workerResult.data) {
        let foundCallableWorker = false;
        let phoneNumber = "";
        let callableWorkerName = "";
        console.log("工作人员数据:", workerResult.data);
        console.log("工作人员数据类型:", typeof workerResult.data);
        console.log("工作人员数据长度:", workerResult.data.length);
        for (const worker of workerResult.data) {
          console.log("检查工作人员:", {
            name: worker.name,
            callFlag: worker.callFlag,
            callFlagType: typeof worker.callFlag,
            phone: worker.phone,
            telephone: worker.telephone,
            workersId: worker.workersId
          });
          if (worker.callFlag === "Y" || worker.callFlag === "y") {
            foundCallableWorker = true;
            callableWorkerName = worker.name;
            phoneNumber = worker.phone || worker.telephone;
            console.log("找到callFlag=Y的工作人员:", {
              name: worker.name,
              phone: worker.phone,
              telephone: worker.telephone,
              finalPhoneNumber: phoneNumber
            });
            if (phoneNumber && phoneNumber.trim() !== "") {
              console.log("开始拨打电话:", phoneNumber);
              const callPromises = [
                // 拨打电话
                new Promise((resolve, reject) => {
                  common_vendor.index.makePhoneCall({
                    phoneNumber,
                    success: function() {
                      console.log("拨打电话成功");
                      resolve("phone_success");
                    },
                    fail: function(err) {
                      console.error("拨打电话失败:", err);
                      reject(err);
                    }
                  });
                }),
                // 调用呼叫接口
                common_api_found.callCommunity()
              ];
              try {
                const results = await Promise.allSettled(callPromises);
                const phoneResult = results[0];
                const apiResult = results[1];
                let successMessage = "";
                let hasError = false;
                if (phoneResult.status === "fulfilled") {
                  successMessage += "电话拨打成功";
                } else {
                  hasError = true;
                  successMessage += "电话拨打失败";
                }
                if (apiResult.status === "fulfilled" && ((_a = apiResult.value) == null ? void 0 : _a.code) === 200) {
                  successMessage += "，呼叫接口调用成功";
                } else {
                  if (successMessage)
                    successMessage += "，";
                  successMessage += "呼叫接口调用失败";
                  hasError = true;
                }
                if (!hasError) {
                  contentCall.value = "已成功拨打电话并通知社区工作人员。";
                } else {
                  contentCall.value = successMessage + "，请稍后重试。";
                }
                popup.value.open();
              } catch (error) {
                console.error("呼叫过程出错:", error);
                contentCall.value = "呼叫过程出现错误，请稍后重试。";
                popup.value.open();
              }
              break;
            } else {
              console.log("找到callFlag=Y的工作人员但没有电话号码:", callableWorkerName);
            }
          }
        }
        console.log("循环结束，结果:", {
          foundCallableWorker,
          phoneNumber,
          callableWorkerName
        });
        if (!foundCallableWorker) {
          console.log("没有找到callFlag=Y的工作人员，执行原来的逻辑");
          const result = await common_api_found.callCommunity();
          if ((result == null ? void 0 : result.code) === 200) {
            contentCall.value = "已呼叫成功。";
            popup.value.open();
          }
        } else if (foundCallableWorker && !phoneNumber) {
          console.log("找到callFlag=Y的工作人员但没有电话号码");
          contentCall.value = `工作人员${callableWorkerName}暂无联系电话，请稍后重试。`;
          popup.value.open();
        }
      } else {
        const result = await common_api_found.callCommunity();
        if ((result == null ? void 0 : result.code) === 200) {
          contentCall.value = "已呼叫成功。";
          popup.value.open();
        }
      }
    };
    const callPhone = (item) => {
      if (!isPass()) {
        return;
      }
      if (!item.telephone && !item.phone) {
        contentCall.value = "当前无配置联系方式。";
        popup.value.open();
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: item.phone ? item.phone : item.telephone
      });
    };
    const callIs = async () => {
      const result = await common_api_found.callIsSubmit();
      if ((result == null ? void 0 : result.code) === 200) {
        isCall.value = result.data;
      }
    };
    const getWorker = async () => {
      const result = await common_api_found.getAllWorkers();
      console.log(result);
      if ((result == null ? void 0 : result.code) === 200 && result.data) {
        allWorker.value = [result.data];
        if (result.data && result.data.length > 0) {
          console.log("工作人员数据样例:", result.data[0]);
        }
      }
    };
    const getContact = async () => {
      const result = await common_api_found.getAllContact();
      if ((result == null ? void 0 : result.code) === 200) {
        contactList.value = result.data;
      } else {
        throw console.log(result);
      }
    };
    const confirm = () => {
      cancelText.value = "";
      confirmText.value = "已知晓";
    };
    const isPass = () => {
      var _a;
      if (!common_vendor.index.getStorageSync("token")) {
        contentCall.value = "您还未登录，请先登录。";
        confirmText.value = "去登录";
        popup.value.open();
        return false;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0) {
        contentCall.value = "您还未进行实名认证，是否去认证？";
        confirmText.value = "去认证";
        popup.value.open();
        return false;
      }
      confirm();
      return true;
    };
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 50;
    };
    common_vendor.onReady(() => {
      isPass();
      isShow.value = true;
    });
    common_vendor.onShow(() => {
      if (isShow.value) {
        isPass();
      }
      getScreenHeight();
      if (common_vendor.index.getStorageSync("token")) {
        getWorker();
        getContact();
        callIs();
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(callManage),
        b: common_vendor.f(common_vendor.unref(flatWorkers), (item, k0, i0) => {
          return {
            a: item.avatarUrl,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.title),
            d: item.title,
            e: common_vendor.t(item.remark),
            f: common_vendor.o(($event) => toDetais(item), item.workersId),
            g: item.workersId
          };
        }),
        c: common_vendor.f(common_vendor.unref(contactList), (item, index, i0) => {
          return {
            a: item.iconUrl,
            b: common_vendor.t(item.name),
            c: item.phoneIcon,
            d: common_vendor.o(($event) => callPhone(item), index),
            e: item.colour,
            f: index
          };
        }),
        d: common_vendor.o(toAi),
        e: scrollHeight.value + "px",
        f: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).HOME,
        g: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME,
        h: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME ? "active" : ""),
        i: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).HOME)),
        j: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ASS,
        k: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS,
        l: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS ? "active" : ""),
        m: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ASS)),
        n: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        o: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        p: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND ? "active" : ""),
        q: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).FOUND)),
        r: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        s: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        t: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY ? "active" : ""),
        v: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY)),
        w: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).MY,
        x: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY,
        y: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY ? "active" : ""),
        z: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).MY)),
        A: common_vendor.sr(popup, "e0d9e5b2-0", {
          "k": "popup"
        }),
        B: common_vendor.o(authConfirm),
        C: common_vendor.p({
          content: contentCall.value,
          cancelText: cancelText.value,
          confimText: confirmText.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/found/found.vue"]]);
wx.createPage(MiniProgramPage);
