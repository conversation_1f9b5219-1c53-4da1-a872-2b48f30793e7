/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 隐私弹框样式 */
.privacy-popup {
  width: 100%;
  background: #ffffff;
  padding: 40rpx 32rpx 60rpx;
  box-sizing: border-box;
}
.privacy-header {
  text-align: center;
  margin-bottom: 32rpx;
}
.privacy-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  line-height: 50rpx;
}
.privacy-content {
  margin-bottom: 40rpx;
}
.privacy-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: left;
}
.privacy-link {
  color: #007AFF;
  text-decoration: underline;
}
.privacy-buttons {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}
.privacy-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
}
.privacy-btn-cancel {
  background: #F5F5F5;
  color: #666666;
}
.privacy-btn-confirm {
  background: #FF9F18;
  color: #ffffff;
}
.privacy-btn:active {
  opacity: 0.8;
}
.menu-btn {
  font-weight: 400;
  font-size: 28rpx;
  color: #FF9F18;
  line-height: 48rpx;
  background: #ffffff;
  border-radius: 4rpx;
  padding: 0 15rpx;
  height: 48rpx;
  text-align: center;
  margin-left: 10rpx;
  position: absolute;
  right: 40rpx;
  top: 0;
}
.overflow-hidden {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.uv-navbar__content__left {
  padding: 0 18rpx !important;
}
.float_img {
  position: absolute;
  top: -150px;
  left: 46rpx;
  width: 282rpx;
  height: 164rpx;
  display: block;
}
.top-ngv {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4rpx;
  position: relative;
}
.top-ngv .iconfont {
  position: absolute;
  left: 5rpx;
  top: -15rpx;
}
.top-ngv text {
  display: block;
  margin-left: 35rpx;
}
.top_flex {
  display: flex;
  padding: 0 8rpx;
  box-sizing: border-box;
  align-items: center;
}
.top-location-box {
  width: 100%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  border-radius: 4rpx;
  position: absolute;
  top: -80rpx;
  left: 16rpx;
}
.top-location-box image:first-of-type {
  width: 26rpx;
  height: 31rpx;
  margin-right: 12rpx;
}
.content {
  background-color: #F5F5F5;
}
.content .banner-box {
  position: relative;
}
.content .notice-box {
  display: flex;
  align-items: center;
  height: 64rpx;
  margin: 0 32rpx 24rpx;
  background: #FFF3DF;
  border-radius: 4px;
  padding: 16rpx 10rpx;
  box-sizing: border-box;
  border: 2rpx solid #FF9F18;
}
.content .notice-box > view {
  display: flex;
  align-items: center;
  color: #333333;
}
.content .notice-box > view image {
  width: 32rpx;
  height: 32rpx;
}
.content .notice-box > view:last-of-type {
  margin-left: 20rpx;
  font-size: 26rpx;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.content .flex_warp {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.content .activity_tab {
  background: #F5F5F5;
}
.content .activity_tab .tag {
  padding: 8px 14px;
  font-size: 28rpx;
  color: #999999;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
  margin: 0 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.content .activity_tab .tag_active {
  background: #ffffff;
  color: #222222;
  font-weight: 550;
}
.content .activity_tab .tab_content {
  background: linear-gradient(180deg, #ffffff 460rpx, #f5f5f5 100%);
  width: 100%;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
}
.content .activity_tab .tab_content .content_bg {
  margin-bottom: 16rpx;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}
.content .activity_tab .tab_content .img_bg {
  position: relative;
}
.content .activity_tab .tab_content .img_bg .right_top {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: #FFFFFF;
  line-height: 32rpx;
  color: #FFFFFF;
  background: #FF9F18;
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
}
.content .activity_tab .tab_content .img_bg .left_bottom {
  width: 99.6%;
  position: absolute;
  bottom: 10rpx;
  height: 40rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.5);
  box-sizing: border-box;
}
.content .activity_tab .tab_content image {
  width: 100%;
  height: 280rpx;
}
.content .activity_tab .tab_content .title {
  font-size: 30rpx;
  font-weight: 500;
  color: #222222;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  margin-bottom: 4rpx;
  box-sizing: border-box;
  padding: 0 16rpx;
}
.content .activity_tab .tab_content .name {
  font-weight: 400;
  font-size: 20rpx;
  color: #CCCCCC;
  line-height: 32rpx;
  text-align: left;
  font-style: normal;
  box-sizing: border-box;
  padding: 0 16rpx;
}
.content .activity_tab .tab_content .remark {
  font-size: 26rpx;
  color: #999999;
  line-height: 40rpx;
  margin-bottom: 20rpx;
  text-align: left;
  font-style: normal;
  box-sizing: border-box;
  padding: 0 16rpx;
}
.content .activity_tab .tab_content .time {
  font-size: 28rpx;
  color: #999999;
  line-height: 32rpx;
  text-align: left;
  padding: 0 16rpx 16rpx;
}
.content .menu-list {
  position: relative;
  top: -20px;
  background-color: #F5F5F5;
  border-top-right-radius: 32rpx;
  border-top-left-radius: 32rpx;
  padding: 32rpx 0;
}
.content .menu-list .scroll_item {
  display: inline-block;
  width: 100%;
}
.content .menu-list .scroll-view-item_H {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}
.content .menu-list .scroll-view-item_H text {
  width: 100%;
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}
.content .menu-list .scroll-view-item_H image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 8rpx;
}
.content .title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 65rpx 18rpx 0 18rpx;
}
.content .title-box .left {
  display: flex;
  align-items: center;
}
.content .title-box .left .line {
  width: 4rpx;
  height: 29rpx;
  border-radius: 10rpx;
  background: #2BBC4A;
  margin-right: 18rpx;
}
.content .title-box .left > text {
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
}
.content .title-box .more {
  font-size: 28rpx;
  color: #999999;
  display: flex;
  align-items: center;
}
.content .title-box .more > text {
  margin-right: 6rpx;
}
.content .life-box {
  margin: 35rpx 18rpx;
  display: flex;
  justify-content: space-between;
}
.content .life-box > view {
  width: 345rpx;
  height: 347rpx;
  position: relative;
}
.content .life-box > view .join-btn {
  position: absolute;
  width: 224rpx;
  left: 50%;
  height: 66rpx;
  bottom: 70rpx;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-text-stroke-width: 0.3px;
  font-size: 28rpx;
  color: #F89E3D;
  background: #FFFFFF;
  border-radius: 66rpx;
  border: 8rpx solid rgba(255, 194, 128, 0.8);
}
.content .life-box > view .join-btn > text:first-of-type {
  margin-right: 15rpx;
}
.content .life-box > view .join-btn > image {
  width: 14rpx;
  height: 25rpx;
}
.content .life-box .desc {
  position: absolute;
  left: 32rpx;
  top: 46rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: #FFFFFF;
}
.content .life-box .desc > view:first-of-type {
  -webkit-text-stroke-width: 0.3px;
  font-size: 28rpx;
  color: #FFFFFF;
  margin-bottom: 6rpx;
}
.content .life-box .right-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content .life-box .right-card > view {
  width: 345rpx;
  height: 164rpx;
  position: relative;
}
.content .life-box .right-card > view .mg {
  top: 26rpx;
}
.content .life-box .right-card > view .right-icon {
  width: 20rpx;
  height: 20rpx;
  margin-top: 18rpx;
}
.content .life-box .life-bg {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.content .news-box {
  margin: 18rpx;
  padding-bottom: 30rpx;
}
.content .news-box .item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #ECECEC;
}
.content .news-box .item > view {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content .news-box .item > view > text {
  -webkit-text-stroke-width: 0.2px;
  font-size: 28rpx;
  color: #555555;
  line-height: 40rpx;
}
.content .news-box .item > view > view {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .news-box .item > image {
  flex-shrink: 0;
  width: 217rpx;
  height: 165rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.content .community-circle-btn {
  margin: 16rpx 18rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.content .community-circle-btn .circle-btn-content {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
}
.content .community-circle-btn .circle-btn-content .circle-btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.content .community-circle-btn .circle-btn-content .circle-btn-text {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
}
.content .community-circle-btn .circle-btn-content .circle-btn-text .circle-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
}
.content .community-circle-btn .circle-btn-content .circle-btn-text .circle-subtitle {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.9;
}
.content .community-circle-btn .circle-btn-content .circle-btn-arrow {
  position: relative;
  z-index: 2;
  margin-left: auto;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}