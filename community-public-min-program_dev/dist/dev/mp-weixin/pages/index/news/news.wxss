/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content_bg {
  width: 100%;
  padding: 20rpx 32rpx;
  display: flex;
  flex-direction: column;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}
.content_bg .img_bg {
  position: relative;
}
.content_bg .img_bg .right_top {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  background: #FF9F18;
  border-radius: 4rpx;
  padding: 4rpx 6rpx;
}
.content_bg .img_bg .left_bottom {
  width: 99.5%;
  position: absolute;
  bottom: 10rpx;
  padding-left: 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  background: #000000;
  opacity: 0.5;
}
.content_bg image {
  width: 686rpx;
  height: 280rpx;
}
.content_bg .title {
  font-family: AppleColorEmoji;
  font-size: 28rpx;
  font-weight: 550;
  color: #222222;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  margin-left: 10rpx;
}
.content_bg .name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 20rpx;
  color: #CCCCCC;
  line-height: 32rpx;
  text-align: left;
  font-style: normal;
  margin-left: 10rpx;
}
.content_bg .remark {
  font-family: AppleColorEmoji;
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
  margin-bottom: 32rpx;
  text-align: left;
  font-style: normal;
  margin-left: 10rpx;
}