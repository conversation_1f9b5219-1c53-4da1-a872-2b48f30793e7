"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_index = require("../../../common/api/index.js");
const common_utils_common = require("../../../common/utils/common.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  _easycom_z_paging2();
}
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  _easycom_z_paging();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "news",
  setup(__props) {
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    common_vendor.onLoad(() => {
    });
    const queryList = (pageNo, pageSize) => {
      const offset = (pageNo - 1) * pageSize;
      getNews(offset, pageSize);
    };
    function getNews(offset, count) {
      common_api_index.getListNew({
        offset,
        count
      }).then((res) => {
        console.log(res);
        paging.value.complete(res.data);
      });
    }
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(dataList.value, (item, i, i0) => {
          return common_vendor.e({
            a: item.thumbUrl,
            b: item.type
          }, item.type ? {
            c: common_vendor.t(item.type)
          } : {}, {
            d: common_vendor.t(item.judge),
            e: common_vendor.t(common_vendor.unref(common_utils_common.dateFormat)(item.updateTime)),
            f: common_vendor.t(item.title),
            g: common_vendor.t(item.name),
            h: common_vendor.t(item.remark),
            i: common_vendor.o(($event) => toPage("detail/detail?url=" + encodeURIComponent(item.url)), i),
            j: i
          });
        }),
        b: common_vendor.sr(paging, "f5e10af8-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => dataList.value = $event),
        e: common_vendor.p({
          modelValue: dataList.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/news/news.vue"]]);
wx.createPage(MiniProgramPage);
