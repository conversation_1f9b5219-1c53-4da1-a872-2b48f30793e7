"use strict";
const common_vendor = require("../../common/vendor.js");
const common_utils_tab = require("../../common/utils/tab.js");
const common_api_system = require("../../common/api/system.js");
const stores_store = require("../../stores/store.js");
const common_api_user = require("../../common/api/user.js");
const common_api_history = require("../../common/api/history.js");
const common_api_index = require("../../common/api/index.js");
const common_utils_common = require("../../common/utils/common.js");
const common_enum = require("../../common/enum.js");
require("../../common/config.js");
require("../../common/request.js");
require("../../common/md5.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_uv_swiper2 + _easycom_uv_icon2 + _easycom_uv_popup2)();
}
const _easycom_uv_swiper = () => "../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_icon = () => "../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_popup = () => "../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_icon + _easycom_uv_popup)();
}
const _sfc_defineComponent = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const bannerList = common_vendor.ref([]), bannerDesc = common_vendor.ref();
    const text = common_vendor.ref("");
    const isAuthLocation = common_vendor.ref(false);
    const userStore = stores_store.useUserStore();
    const areaStore = stores_store.useAreaStore();
    const userDistrictName = common_vendor.ref("");
    const displayCommunityName = common_vendor.ref("请选择社区");
    const isShowingUpperLevel = common_vendor.ref(false);
    const isLogin = common_vendor.ref(false);
    common_vendor.ref([]);
    const newNotice = common_vendor.ref();
    const menuList = common_vendor.ref([]), menuCount = common_vendor.ref(), activeIndicator = common_vendor.ref(1);
    let scrollHeight = common_vendor.ref();
    const privacyPopup = common_vendor.ref();
    const hasAcceptedPrivacy = common_vendor.ref(false);
    const form = common_vendor.ref({ districtCode: "" });
    const areaName = common_vendor.ref("");
    const communityButtonText = common_vendor.computed(() => {
      var _a, _b, _c;
      console.log("计算社区按钮文本，用户信息:", userStore.userInfo);
      console.log("districtName:", (_a = userStore.userInfo) == null ? void 0 : _a.districtName);
      console.log("isShowingUpperLevel:", isShowingUpperLevel.value);
      if (isShowingUpperLevel.value) {
        return "我的社区";
      } else {
        return ((_b = userStore.userInfo) == null ? void 0 : _b.cityName) || ((_c = userStore.userInfo) == null ? void 0 : _c.provinceName) || "邛崃市";
      }
    });
    common_vendor.onShow(async () => {
      var _a, _b;
      const token = common_vendor.index.getStorageSync("token");
      isLogin.value = token ? true : false;
      if (token && !userStore.userInfo) {
        await getUserInfoByToken();
      }
      if (userStore.userInfo && !text.value) {
        getNotice();
      }
      if (userStore.userInfo && userStore.userInfo.districtName) {
        userDistrictName.value = userStore.userInfo.districtName;
        if (!isShowingUpperLevel.value) {
          displayCommunityName.value = userStore.userInfo.districtName;
        } else {
          const upperLevelName = ((_a = userStore.userInfo) == null ? void 0 : _a.cityName) || ((_b = userStore.userInfo) == null ? void 0 : _b.provinceName) || "邛崃市";
          displayCommunityName.value = upperLevelName;
        }
      } else {
        userDistrictName.value = areaStore.areaName || "";
        displayCommunityName.value = areaStore.areaName || "请选择社区";
        isShowingUpperLevel.value = false;
      }
    });
    common_vendor.onLoad(async () => {
      common_vendor.index.hideTabBar();
      getScreenHeight();
      await getUserInfoByToken();
      common_api_index.getListByPublic().then((res) => {
        let remainder = Math.floor(res.data.length / 8);
        let template = res.data.length % 8 > 0 ? 1 : 0;
        menuCount.value = remainder + template;
        for (let i = 0; i < menuCount.value; i++) {
          const start = i * 8;
          menuList.value[i] = res.data.slice(start, start + 8);
        }
      });
      common_utils_tab.change(0);
      getAuth();
      if (userStore.userInfo) {
        getNotice();
      }
      getBanner();
      getConfigSetting();
      getNewsList();
      getNewsData();
      getRecentlyData();
      checkPrivacyAgreement();
      common_vendor.index.$on("selectionRegion", async (data) => {
        form.value.districtCode = data.code;
        areaName.value = data.name;
        displayCommunityName.value = data.name;
        areaStore.setArea(data.code, data.name);
        isShowingUpperLevel.value = false;
        console.log("选择了新的社区:", data.name, "代码:", data.code);
      });
    });
    common_vendor.watch(
      () => areaStore.areaName,
      (newVal, oldVal) => {
        if (newVal != oldVal) {
          getBanner();
          displayCommunityName.value = newVal || "请选择社区";
          isShowingUpperLevel.value = false;
        }
      }
    );
    common_vendor.watch(
      () => userStore.userInfo,
      (newVal, oldVal) => {
        isLogin.value = !!newVal && !!common_vendor.index.getStorageSync("token");
        if (newVal && !oldVal) {
          getNotice();
        }
        console.log("用户信息变化:", newVal);
        console.log("districtName:", newVal == null ? void 0 : newVal.districtName);
        console.log("cityName:", newVal == null ? void 0 : newVal.cityName);
        console.log("provinceName:", newVal == null ? void 0 : newVal.provinceName);
      }
    );
    common_vendor.onShareAppMessage(() => {
      return {
        path: "pages/index/index"
      };
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    const tabObj = common_vendor.reactive({
      tabList: [{
        name: "社区动态",
        count: 1
      }, {
        name: "最新",
        count: 2
      }, {
        name: "新闻",
        count: 3
      }],
      tabActive: 1,
      dynamics: [],
      lastNews: [],
      recentlyList: [],
      newsList: [],
      dataList: [],
      setTab: async (name) => {
        tabObj.tabActive = name;
        console.log("切换标签页:", name);
        if (name === 1) {
          tabObj.dataList = tabObj.recentlyList;
          console.log("显示社区动态数据:", tabObj.dataList);
        } else if (name === 3) {
          tabObj.dataList = tabObj.dynamics;
          console.log("显示新闻数据:", tabObj.dataList);
        } else if (name === 2) {
          console.log("点击最新标签，重新获取最新新闻数据");
          await getNewsData();
          tabObj.dataList = tabObj.newsList;
          console.log("显示最新新闻数据:", tabObj.dataList);
        }
      }
    });
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 30;
      console.log(scrollHeight.value, systemInfo.windowHeight);
    };
    const scrollEvent = (e) => {
      if (e.detail.scrollLeft === 0) {
        activeIndicator.value = 1;
      }
      if (e.detail.scrollLeft > 300) {
        activeIndicator.value = Math.floor(e.detail.scrollWidth / e.detail.scrollLeft);
      }
    };
    const getBanner = async () => {
      bannerList.value = [];
      const res = await common_api_index.getBannerList({ code: areaStore.areaCode });
      if ((res == null ? void 0 : res.code) === 200) {
        bannerList.value = res.data;
      } else {
        throw console.log(res);
      }
    };
    const getConfigSetting = async () => {
      const res = await common_api_index.getConfig({ code: "applet_home_label" });
      if ((res == null ? void 0 : res.code) === 200) {
        console.log(res, "banner");
        bannerDesc.value = res.data.configUrl;
      } else {
        throw console.log(res);
      }
    };
    const getNewsList = async () => {
      const res = await common_api_index.getListNew({
        offset: 0,
        count: 5
      });
      if ((res == null ? void 0 : res.code) === 200) {
        tabObj.dynamics = res.data.length >= 6 ? res.data.slice(0, 5) : res.data;
      } else {
        throw console.log(res);
      }
    };
    const getNewsData = async () => {
      const res = await common_api_index.getNewsDataList();
      if ((res == null ? void 0 : res.code) === 200) {
        console.log(res, "最新新闻");
        res.data.map((item) => {
          if (item.content) {
            item.content = cleanHtmlContent(item.content);
          }
          if (item.createTime) {
            item.createTime = new Date(item.createTime).toISOString().slice(0, 10);
          }
        });
        tabObj.newsList = res.data.length >= 6 ? res.data.slice(0, 5) : res.data;
      } else {
        throw console.log(res);
      }
    };
    const getRecentlyData = async () => {
      const res = await common_api_history.getRecentlyHome();
      if ((res == null ? void 0 : res.code) === 200) {
        console.log(res, "社区动态");
        res.rows.map((item) => {
          if (item.content) {
            item.content = cleanHtmlContent(item.content);
          }
          if (item.createTime) {
            item.createTime = new Date(item.createTime).toISOString().slice(0, 10);
          }
        });
        tabObj.recentlyList = res.rows.length >= 6 ? res.rows.slice(0, 5) : res.rows;
        tabObj.dataList = tabObj.recentlyList;
      } else {
        throw console.log(res);
      }
    };
    function removeAllTags(htmlString) {
      if (!htmlString)
        return "";
      return htmlString.replace(/<[^>]*>/g, "");
    }
    function cleanHtmlContent(content) {
      if (!content)
        return "";
      let cleanContent = removeAllTags(content);
      cleanContent = cleanContent.replaceAll("&nbsp;", "");
      cleanContent = cleanContent.replaceAll("&amp;", "&");
      cleanContent = cleanContent.replaceAll("&lt;", "<");
      cleanContent = cleanContent.replaceAll("&gt;", ">");
      cleanContent = cleanContent.replaceAll("&quot;", '"');
      cleanContent = cleanContent.replaceAll("&#39;", "'");
      cleanContent = cleanContent.replaceAll("&hellip;", "...");
      cleanContent = cleanContent.replace(/\s+/g, " ").trim();
      return cleanContent;
    }
    async function getUserInfoByToken() {
      const token = common_vendor.index.getStorageSync("token");
      if (token) {
        await common_api_user.getUserInfo().then((res) => {
          var _a;
          console.log("获取用户信息响应:", res);
          console.log("用户信息数据:", res.data);
          console.log("用户districtName:", (_a = res.data) == null ? void 0 : _a.districtName);
          userStore.setUser(res.data);
          if (res.data) {
            common_vendor.index.setStorageSync("userInfo", res.data);
            console.log("用户信息已保存到storage:", res.data);
          }
          if (res.data && res.data.districtName) {
            userDistrictName.value = res.data.districtName;
          } else {
            userDistrictName.value = areaStore.areaName || "";
          }
        });
      } else {
        userDistrictName.value = areaStore.areaName || "";
      }
    }
    function getNotice() {
      common_api_user.getNewest().then((res) => {
        newNotice.value = res.data;
      });
    }
    function getAuth() {
      common_vendor.index.getSetting({
        success(res) {
          if (res.authSetting["scope.record"])
            ;
          else {
            common_vendor.index.authorize({
              scope: "scope.record",
              success() {
                console.log("获取录音权限");
              }
            });
          }
          if (res.authSetting["scope.userLocation"]) {
            isAuthLocation.value = true;
            if (!areaStore.areaName) {
              getCurrentLocation();
            }
          } else {
            common_vendor.index.authorize({
              scope: "scope.userLocation",
              success() {
                getCurrentLocation();
              }
            });
          }
        }
      });
    }
    function getCurrentLocation() {
      common_vendor.index.getLocation({
        type: "wgs84",
        success: async function(res) {
          console.log("当前位置的经度：" + res.longitude);
          console.log("当前位置的纬度：" + res.latitude);
          common_api_system.getLocation({ longitude: res.longitude, latitude: res.latitude }).then(({ regeocode }) => {
            const { province, city, district, towncode, township, streetNumber } = regeocode.addressComponent;
            const location = city + district + township;
            areaStore.setArea(towncode, location);
            common_vendor.index.setStorageSync("latitude", res.latitude);
            common_vendor.index.setStorageSync("longitude", res.longitude);
          });
        },
        fail(e) {
          console.log(e);
        }
      });
    }
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    function switchToMyCommunity() {
      var _a, _b, _c, _d;
      if (!userStore.userInfo || !userStore.userInfo.districtName) {
        toPage("area/area");
        return;
      }
      if (isShowingUpperLevel.value) {
        displayCommunityName.value = userStore.userInfo.districtName;
        isShowingUpperLevel.value = false;
        if (userStore.userInfo.districtCode) {
          areaStore.setArea(userStore.userInfo.districtCode, userStore.userInfo.districtName);
        }
        console.log("切换回用户社区:", userStore.userInfo.districtName);
      } else {
        const upperLevelName = ((_a = userStore.userInfo) == null ? void 0 : _a.cityName) || ((_b = userStore.userInfo) == null ? void 0 : _b.provinceName) || "邛崃市";
        const upperLevelCode = ((_c = userStore.userInfo) == null ? void 0 : _c.cityCode) || ((_d = userStore.userInfo) == null ? void 0 : _d.provinceCode) || "510183";
        displayCommunityName.value = upperLevelName;
        isShowingUpperLevel.value = true;
        areaStore.setArea(upperLevelCode, upperLevelName);
        console.log("切换到上级区域:", upperLevelName);
        console.log("上级区域代码:", upperLevelCode);
      }
    }
    function goToLogin() {
      common_vendor.index.reLaunch({
        url: "/pages/auth/login/login"
      });
    }
    const bannerClick = (index) => {
      toMenuPage(bannerList[index]);
    };
    const toDetails = (item) => {
      let url = "";
      switch (item.type) {
        case 1:
          url = "/pages/subpackA/market/details?id=" + item.id;
          break;
        case 2:
          url = "/pages/subpackA/market/details?id=" + item.id;
          break;
        case 3:
          url = "/pages/subpackA/release/activity-details?id=" + item.id;
          break;
        case 4:
          url = `/pages/map/guide/guide?toDetails=1&tourLineId=${item.id}&communityName=${item.title}`;
          break;
        case 5:
          url = "/pages/subpackA/store/details?id=" + item.id;
          break;
        default:
          url = `/pages/subpackA/history/details?id=${item.activityId}`;
          break;
      }
      common_vendor.index.navigateTo({
        url
      });
    };
    function toMenuPage(item) {
      var _a;
      let url = item.menuPath;
      switch (item.jumpType) {
        case common_enum.PageType.MINI_PROGRAM:
          if (item.menuPath == "/pages/map/guide/guide?showLine=true") {
            toGuide(url);
            break;
          }
          toPage(url);
          break;
        case common_enum.PageType.H5:
          if (item.menuName == "家庭医生") {
            if (!userStore.userInfo) {
              common_vendor.index.showModal({
                title: "",
                content: "您还未登录，是否去登录？",
                confirmText: "去登录",
                cancelText: "取消",
                confirmColor: "#07C160",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    common_vendor.index.reLaunch({
                      url: "/pages/auth/login/login"
                    });
                  }
                }
              });
              break;
            }
            if (userStore.userInfo.authentication == 0) {
              common_vendor.index.showModal({
                title: "",
                content: "您还未进行实名认证，是否去认证？",
                confirmText: "去认证",
                cancelText: "取消",
                confirmColor: "#2BBC4A",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    common_vendor.index.navigateTo({
                      url: "/pages/my/auth/auth"
                    });
                  }
                }
              });
              break;
            }
            common_api_index.addFamilyDoctorUsage({
              districtCode: (_a = userStore.userInfo) == null ? void 0 : _a.districtCode,
              platformType: common_enum.PLATFORM_TYPE
            }).then((res) => {
              console.log("plus one. doctor.");
            });
          }
          toPage(`/pages/index/h5/h5?url=${encodeURIComponent(url)}`);
          break;
        case common_enum.PageType.OTHER_MINI_PROGRAM:
          console.log(url, "url");
          const urlParts = url.split("&");
          const appId = urlParts[0];
          const path = urlParts[1];
          common_vendor.index.navigateToMiniProgram({
            appId,
            path,
            envVersion: "release",
            success: function(res) {
              console.log("跳转小程序成功", res);
            },
            fail: function(err) {
              console.log("跳转小程序失败", err);
              common_vendor.index.showToast({
                title: "跳转失败",
                icon: "none"
              });
            }
          });
          break;
      }
    }
    const toNav = () => {
      const longitude = common_vendor.index.getStorageSync("longitude");
      const latitude = common_vendor.index.getStorageSync("latitude");
      const areaName2 = common_vendor.index.getStorageSync("areaName");
      common_vendor.index.navigateTo({
        url: `/pages/map/navigation/navigation?longitude=${longitude}&latitude=${latitude}&name=${areaName2}&address=${areaName2}`
      });
    };
    function toGuide(url) {
      common_vendor.index.reLaunch({
        url
      });
    }
    function checkPrivacyAgreement() {
      const hasAgreed = common_vendor.index.getStorageSync("hasAcceptedPrivacy");
      if (!hasAgreed) {
        setTimeout(() => {
          var _a;
          (_a = privacyPopup.value) == null ? void 0 : _a.open();
        }, 500);
      }
    }
    function acceptPrivacy() {
      var _a;
      common_vendor.index.setStorageSync("hasAcceptedPrivacy", true);
      hasAcceptedPrivacy.value = true;
      (_a = privacyPopup.value) == null ? void 0 : _a.close();
    }
    function rejectPrivacy() {
      common_vendor.index.showModal({
        title: "温馨提示",
        content: "不同意服务协议和隐私政策将无法使用本应用的功能和服务",
        confirmText: "重新考虑",
        cancelText: "仍要退出",
        success: (res) => {
          var _a;
          if (res.confirm)
            ;
          else {
            (_a = privacyPopup.value) == null ? void 0 : _a.close();
          }
        }
      });
    }
    function openServiceAgreement() {
      const url = "https://qlzhsq.qlzhsq.cn:30210/agreement/user/";
      common_vendor.index.navigateTo({
        url: `/pages/common/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent("服务协议")}`
      });
    }
    function openPrivacyPolicy() {
      const url = "https://qlzhsq.qlzhsq.cn:30210/privacy/user/";
      common_vendor.index.navigateTo({
        url: `/pages/common/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent("隐私政策")}`
      });
    }
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.o(bannerClick),
        b: common_vendor.p({
          height: "486rpx",
          interval: 1e4,
          list: bannerList.value,
          keyName: "bannerImg"
        }),
        c: bannerDesc.value,
        d: isLogin.value ? "#FF9F18" : "#333333",
        e: common_vendor.t(displayCommunityName.value),
        f: isLogin.value ? "#FF9F18" : "",
        g: isLogin.value ? "#fff" : "",
        h: common_vendor.o(($event) => toPage("area/area")),
        i: isLogin.value ? "#FF9F18" : "",
        j: isLogin.value ? "#FF9F18" : "#333333",
        k: isLogin.value ? "#fff" : "rgba(255,255,255,0.6)",
        l: common_vendor.o(toNav),
        m: !isLogin.value
      }, !isLogin.value ? {
        n: common_vendor.o(goToLogin)
      } : {
        o: common_vendor.t(common_vendor.unref(communityButtonText)),
        p: common_vendor.o(switchToMyCommunity)
      }, {
        q: common_vendor.f(menuCount.value, (info, k0, i0) => {
          return {
            a: common_vendor.f(menuList.value[info - 1], (item, i, i1) => {
              return {
                a: item.menuIcon,
                b: common_vendor.t(item.menuName),
                c: common_vendor.o(($event) => toMenuPage(item), i),
                d: i
              };
            }),
            b: info
          };
        }),
        r: common_vendor.o(scrollEvent),
        s: common_vendor.f(menuCount.value, (info, k0, i0) => {
          return {
            a: activeIndicator.value === info ? "#FF9F18" : "#eaeaea",
            b: info
          };
        }),
        t: common_vendor.t((_a = newNotice.value) == null ? void 0 : _a.noticeTitle),
        v: common_vendor.p({
          name: "arrow-right",
          color: "#333333"
        }),
        w: common_vendor.o(($event) => {
          var _a2;
          return toPage("/pages/my/message/detail/detail?noticeId=" + ((_a2 = newNotice.value) == null ? void 0 : _a2.noticeId));
        }),
        x: common_vendor.f(tabObj.tabList, (item, i, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: common_vendor.n(item.count === tabObj.tabActive ? "tag_active" : ""),
            c: common_vendor.o(($event) => tabObj.setTab(item.count), i),
            d: i
          };
        }),
        y: common_vendor.o(($event) => toPage("news/news")),
        z: common_vendor.p({
          name: "arrow-right",
          color: "#999999"
        }),
        A: tabObj.tabActive === 3
      }, tabObj.tabActive === 3 ? {
        B: common_vendor.f(tabObj.dataList, (item, i, i0) => {
          return common_vendor.e({
            a: item.thumbUrl,
            b: item.type
          }, item.type ? {
            c: common_vendor.t(item.type)
          } : {}, {
            d: common_vendor.t(item.judge),
            e: common_vendor.t(common_vendor.unref(common_utils_common.dateFormat)(item.updateTime)),
            f: common_vendor.t(item.title),
            g: common_vendor.t(item.name),
            h: common_vendor.t(item.remark),
            i,
            j: common_vendor.o(($event) => toPage("news/detail/detail?url=" + encodeURIComponent(item.url)), i)
          });
        })
      } : {}, {
        C: tabObj.tabActive !== 3
      }, tabObj.tabActive !== 3 ? {
        D: common_vendor.f(tabObj.dataList, (item, i, i0) => {
          return common_vendor.e({
            a: (item == null ? void 0 : item.coverImages) || (item == null ? void 0 : item.coverImage),
            b: item.type
          }, item.type ? {
            c: common_vendor.t(item.typeName)
          } : {}, {
            d: common_vendor.t(item == null ? void 0 : item.viewCount),
            e: common_vendor.t(item == null ? void 0 : item.browseNumber),
            f: common_vendor.t(item.createTime),
            g: common_vendor.t(item.title),
            h: common_vendor.t(item.content),
            i: common_vendor.t(item.districtName),
            j: i,
            k: common_vendor.o(($event) => toDetails(item), i)
          });
        })
      } : {}, {
        E: common_vendor.unref(scrollHeight) + "px",
        F: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).HOME,
        G: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME,
        H: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME ? "active" : ""),
        I: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).HOME)),
        J: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ASS,
        K: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS,
        L: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS ? "active" : ""),
        M: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ASS)),
        N: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        O: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        P: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND ? "active" : ""),
        Q: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).FOUND)),
        R: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        S: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        T: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY ? "active" : ""),
        U: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY)),
        V: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).MY,
        W: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY,
        X: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY ? "active" : ""),
        Y: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).MY)),
        Z: common_vendor.o(openServiceAgreement),
        aa: common_vendor.o(openPrivacyPolicy),
        ab: common_vendor.o(rejectPrivacy),
        ac: common_vendor.o(acceptPrivacy),
        ad: common_vendor.sr(privacyPopup, "0493a3c7-3", {
          "k": "privacyPopup"
        }),
        ae: common_vendor.p({
          mode: "bottom",
          closeOnClickOverlay: false,
          closeable: false,
          round: "20"
        })
      });
    };
  }
});
_sfc_defineComponent.__runtimeHooks = 2;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/index.vue"]]);
wx.createPage(MiniProgramPage);
