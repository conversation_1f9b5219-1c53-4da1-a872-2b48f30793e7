<view><view class="content main-content" style="{{'height:' + E}}"><view class="banner-box"><uv-swiper wx:if="{{b}}" bindclick="{{a}}" u-i="0493a3c7-0" bind:__l="__l" u-p="{{b}}"></uv-swiper></view><view class="menu-list"><image src="{{c}}" mode="" class="float_img"></image><view class="top-location-box top_flex"><view class="top-ngv top_flex" style="{{'background:' + g}}" bindtap="{{h}}"><icon style="{{'color:' + d + ';' + 'font-size:33rpx;margin-right:6rpx'}}" class="iconfont"></icon><text style="{{'color:' + f}}">{{e}}</text></view><view class="top-ngv top_flex" style="{{'background:' + k + ';' + 'margin-left:10rpx'}}" bindtap="{{l}}"><text style="{{'color:' + i}}">导航</text><icon style="{{'color:' + j + ';' + 'font-size:33rpx;margin-right:6rpx'}}" class="iconfont"></icon></view><view wx:if="{{m}}" class="top-ngv top_flex" style="border-radius:4rpx;width:108rpx;position:absolute;right:60rpx;margin-left:10rpx;background:#F93A4A;color:#ffffff;padding:0 12rpx;height:44rpx" bindtap="{{n}}"> 未登录 </view><view wx:else class="menu-btn" bindtap="{{p}}">{{o}}</view></view><scroll-view ref="scrollRef" scroll-x="true" style="white-space:nowrap" bindscroll="{{r}}"><block wx:for="{{q}}" wx:for-item="info" wx:key="b"><view class="scroll_item"><view style="width:100%" class="flex_warp"><view wx:for="{{info.a}}" wx:for-item="item" wx:key="d" class="scroll-view-item_H" bindtap="{{item.c}}"><image src="{{item.a}}" mode=""></image><text>{{item.b}}</text></view></view></view></block></scroll-view><view style="width:100%;height:8rpx;margin-top:4px;display:flex;justify-content:center;align-items:center"><block wx:for="{{s}}" wx:for-item="info" wx:key="b"><text style="{{'width:16rpx;height:8rpx;border-radius:4rpx' + ';' + ('background:' + info.a)}}"></text></block></view></view><view class="notice-box"><view class=""><image src="/static/index/speaker.png" mode=""></image></view><view bindtap="{{w}}"><text class="uv-line-1">{{t}}</text><uv-icon wx:if="{{v}}" u-i="0493a3c7-1" bind:__l="__l" u-p="{{v}}"></uv-icon></view></view><view class="activity_tab"><view class="flex_warp"><view style="width:90%" class="flex_warp"><block wx:for="{{x}}" wx:for-item="item" wx:key="d"><view class="{{['tag', item.b]}}" bindtap="{{item.c}}"><text>{{item.a}}</text></view></block></view><uv-icon wx:if="{{z}}" bindtap="{{y}}" u-i="0493a3c7-2" bind:__l="__l" u-p="{{z}}"></uv-icon></view><view class="tab_content"><block wx:if="{{A}}"><view wx:for="{{B}}" wx:for-item="item" wx:key="i" class="content_bg" bindtap="{{item.j}}"><view class="img_bg"><image src="{{item.a}}" style="border-top-right-radius:20rpx;border-top-left-radius:20rpx" mode=""></image><text wx:if="{{item.b}}" class="right_top">{{item.c}}</text><view class="left_bottom"><text style="width:80%;display:inline-block">{{item.d}}</text><text>{{item.e}}</text></view></view><text class="title">{{item.f}}</text><text class="name">{{item.g}}</text><text class="remark">{{item.h}}</text></view></block><block wx:if="{{C}}"><view wx:for="{{D}}" wx:for-item="item" wx:key="j" class="content_bg" bindtap="{{item.k}}"><view class="img_bg"><image src="{{item.a}}" style="border-top-right-radius:20rpx;border-top-left-radius:20rpx" mode=""></image><text wx:if="{{item.b}}" class="right_top">{{item.c}}</text><view class="left_bottom"><text>浏览量：{{item.d}}{{item.e}}</text><text>{{item.f}}</text></view></view><text class="title">{{item.g}}</text><text class="name"></text><text class="remark overflow-hidden">{{item.h}}</text><text class="time">所属辖区：{{item.i}}</text></view></block></view></view></view><view class="tabbar"><view class="tabbar-box"><view class="" bindtap="{{I}}"><image hidden="{{!F}}" class="home-icon" src="/static/tabbar/home.png"></image><image hidden="{{!G}}" class="home-icon" src="/static/tabbar/home_fill.png"></image><view class="{{['icon-text', H]}}"><text>首页</text></view></view><view class="" bindtap="{{M}}"><image hidden="{{!J}}" class="home-icon" src="/static/tabbar/help.png"></image><image hidden="{{!K}}" class="home-icon" src="/static/tabbar/help_fill.png"></image><view class=""></view><view class="{{['icon-text', L]}}"><text>连心桥</text></view></view><view class="" bindtap="{{Q}}"><view class="map-box"><image hidden="{{!N}}" class="home-icon" src="/static/tabbar/found_fill.png"></image><image hidden="{{!O}}" class="home-icon" src="/static/tabbar/found_fill.png"></image></view><view class=""></view><view class="{{['icon-text', 'center-icon', P]}}"><text>找社区</text></view></view><view class="" bindtap="{{U}}"><image hidden="{{!R}}" class="home-icon" src="/static/tabbar/activity.png"></image><image hidden="{{!S}}" class="home-icon" src="/static/tabbar/activity_fill.png"></image><view class="{{['icon-text', T]}}"><text>社区活动</text></view></view><view class="" bindtap="{{Y}}"><image hidden="{{!V}}" class="home-icon" src="/static/tabbar/user.png"></image><image hidden="{{!W}}" class="home-icon" src="/static/tabbar/user_fill.png"></image><view class="{{['icon-text', X]}}"><text>我的</text></view></view></view></view><uv-popup wx:if="{{ae}}" class="r" u-s="{{['d']}}" u-r="privacyPopup" u-i="0493a3c7-3" bind:__l="__l" u-p="{{ae}}"><view class="privacy-popup"><view class="privacy-header"><text class="privacy-title">服务协议和隐私政策</text></view><view class="privacy-content"><text class="privacy-text"> 感谢您信任并使用本程序，我们根据相关法律法规制定了个人信息与隐私保护的相关政策。在开始使用本程序前请仔细阅读 <text class="privacy-link" bindtap="{{Z}}">《服务协议》</text>、<text class="privacy-link" bindtap="{{aa}}">《隐私政策》</text>，以便我们向您提供更优质的服务。请您确认并同意后，再开始使用 我们的产品与服务！ </text></view><view class="privacy-buttons"><view class="privacy-btn privacy-btn-cancel" bindtap="{{ab}}"><text>拒绝</text></view><view class="privacy-btn privacy-btn-confirm" bindtap="{{ac}}"><text>同意</text></view></view></view></uv-popup></view>