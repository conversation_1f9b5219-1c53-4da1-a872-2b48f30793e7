"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_system = require("../../../common/api/system.js");
const common_api_index = require("../../../common/api/index.js");
const stores_store = require("../../../stores/store.js");
const common_enum = require("../../../common/enum.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_cc_watermark12 = common_vendor.resolveComponent("cc-watermark1");
  const _easycom_cc_watermark22 = common_vendor.resolveComponent("cc-watermark2");
  const _easycom_cc_watermark32 = common_vendor.resolveComponent("cc-watermark3");
  const _easycom_cc_watermark42 = common_vendor.resolveComponent("cc-watermark4");
  const _easycom_cc_watermark52 = common_vendor.resolveComponent("cc-watermark5");
  const _easycom_cc_watermark62 = common_vendor.resolveComponent("cc-watermark6");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_cc_watermark12 + _easycom_cc_watermark22 + _easycom_cc_watermark32 + _easycom_cc_watermark42 + _easycom_cc_watermark52 + _easycom_cc_watermark62 + _easycom_uv_popup2)();
}
const _easycom_cc_watermark1 = () => "../../../components/cc-watermark1/cc-watermark1.js";
const _easycom_cc_watermark2 = () => "../../../components/cc-watermark2/cc-watermark2.js";
const _easycom_cc_watermark3 = () => "../../../components/cc-watermark3/cc-watermark3.js";
const _easycom_cc_watermark4 = () => "../../../components/cc-watermark4/cc-watermark4.js";
const _easycom_cc_watermark5 = () => "../../../components/cc-watermark5/cc-watermark5.js";
const _easycom_cc_watermark6 = () => "../../../components/cc-watermark6/cc-watermark6.js";
const _easycom_uv_popup = () => "../../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_cc_watermark1 + _easycom_cc_watermark2 + _easycom_cc_watermark3 + _easycom_cc_watermark4 + _easycom_cc_watermark5 + _easycom_cc_watermark6 + _easycom_uv_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "watermark-camera",
  setup(__props) {
    const isAuthCamera = common_vendor.ref(true);
    const isAuthLocation = common_vendor.ref(false);
    const currentLocation = common_vendor.ref("");
    const position = common_vendor.ref("back");
    const userStore = stores_store.useUserStore();
    const locationStore = stores_store.useLocationStore();
    const watermarkPopup = common_vendor.ref();
    const watermark = common_vendor.ref();
    const templateWxml = common_vendor.ref();
    const currentTemplateIndex = common_vendor.ref(0);
    const templateList = common_vendor.ref([{
      img: "/static/camera/watermark/t1.png"
    }, {
      img: "/static/camera/watermark/t2.png"
    }, {
      img: "/static/camera/watermark/t3.png"
    }, {
      img: "/static/camera/watermark/t4.png"
    }, {
      img: "/static/camera/watermark/t5.png"
    }, {
      img: "/static/camera/watermark/t6.png"
    }]);
    const drawingParams = common_vendor.reactive({
      tempWatermarkPath: "",
      width: "",
      height: ""
    });
    common_vendor.onLoad(() => {
      var _a;
      common_api_index.addCameraUsage({
        districtCode: (_a = userStore.userInfo) == null ? void 0 : _a.districtCode,
        cameraType: common_enum.CameraType.WATERMARK_CAMERA,
        platformType: common_enum.PLATFORM_TYPE
      }).then((res) => {
        console.log("plus one.");
      });
      getAuth();
    });
    function submit(params) {
      const { tempWatermarkPath, width, height } = params;
      drawingParams.tempWatermarkPath = tempWatermarkPath;
      drawingParams.width = width;
      drawingParams.height = height;
    }
    function pipeline(wxml) {
      templateWxml.value = wxml;
    }
    function changeWatermark(index) {
      templateWxml.value = "";
      currentTemplateIndex.value = index;
      watermarkPopup.value.close();
    }
    function openWatermarkPopup() {
      watermarkPopup.value.open();
    }
    function getAuth() {
      common_vendor.index.getSetting({
        success(res) {
          if (res.authSetting["scope.userLocation"]) {
            isAuthLocation.value = true;
            getCurrentLocation();
          } else {
            common_vendor.index.authorize({
              scope: "scope.userLocation",
              success() {
                getCurrentLocation();
              }
            });
          }
        }
      });
    }
    function getCurrentLocation() {
      common_vendor.index.getLocation({
        type: "wgs84",
        success: async function(res) {
          console.log("当前位置的经度：" + res.longitude);
          console.log("当前位置的纬度：" + res.latitude);
          locationStore.setLngLat(res.longitude, res.longitude);
          common_api_system.getLocation({ longitude: res.longitude, latitude: res.latitude }).then(({ regeocode }) => {
            const { province, city, district, township } = regeocode.addressComponent;
            currentLocation.value = city + district + township;
            locationStore.setLocation(currentLocation.value);
          });
        },
        fail(e) {
          console.log(e);
        }
      });
    }
    function authCamera() {
      common_vendor.index.openSetting({
        success(res) {
          if (res.authSetting["scope.camera"]) {
            common_vendor.index.redirectTo({
              url: "/pages/index/watermark-camera/watermark-camera"
            });
          } else {
            common_vendor.index.navigateBack({
              delta: 1
            });
          }
        }
      });
    }
    function error(e) {
      isAuthCamera.value = false;
    }
    function turn() {
      if (position.value == "back") {
        position.value = "front";
      } else {
        position.value = "back";
      }
    }
    async function takePhoto() {
      await watermark.value.render(templateWxml.value);
      templateWxml.value = "";
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      const ctx = common_vendor.index.createCameraContext();
      ctx.takePhoto({
        quality: "original",
        success: async (res) => {
          const tempImagePath = res.tempImagePath;
          const compressRes = await compress(res.width, 1080, tempImagePath);
          const imageInfo = await common_vendor.index.getImageInfo({
            src: compressRes.tempFilePath
          });
          const width = imageInfo.width;
          const height = imageInfo.height;
          common_vendor.wx$1.createSelectorQuery().select("#myCanvas").fields({ node: true, size: true }).exec(async (canvasRes) => {
            const canvas = canvasRes[0].node;
            const ctx2 = canvas.getContext("2d");
            canvas.width = width;
            canvas.height = height;
            const targetImg = await Promise.all([renderImg(canvas, compressRes.tempFilePath), renderImg(canvas, drawingParams.tempWatermarkPath)]);
            ctx2.drawImage(targetImg[0], 0, 0, width, height);
            const templateHeight = drawingParams.height;
            ctx2.drawImage(targetImg[1], 30, height - templateHeight - 30, drawingParams.width, templateHeight);
            common_vendor.wx$1.canvasToTempFilePath({
              destWidth: width,
              destHeight: height,
              canvas,
              success: (res1) => {
                common_vendor.index.saveImageToPhotosAlbum({
                  filePath: res1.tempFilePath,
                  success: function() {
                    console.log("save success");
                    common_vendor.index.hideLoading();
                    common_vendor.index.showToast({
                      title: "保存成功",
                      duration: 2e3
                    });
                  }
                });
              }
            });
          });
        }
      });
    }
    function renderImg(canvas, url) {
      return new Promise((resolve, reject) => {
        const img = canvas.createImage();
        img.src = url;
        img.onload = () => {
          resolve(img);
        };
      });
    }
    async function compress(width, maxWidth, src) {
      let option = {
        quality: 100
      };
      if (width > maxWidth) {
        option.compressedWidth = maxWidth;
      }
      option.src = src;
      return common_vendor.index.compressImage(option);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: currentTemplateIndex.value == 0
      }, currentTemplateIndex.value == 0 ? {
        b: common_vendor.sr(watermark, "7751ca38-0", {
          "k": "watermark"
        }),
        c: common_vendor.o(submit),
        d: common_vendor.p({
          location: common_vendor.unref(locationStore).location
        }),
        e: common_vendor.o(pipeline),
        f: common_vendor.p({
          reduce: true,
          location: common_vendor.unref(locationStore).location
        })
      } : currentTemplateIndex.value == 1 ? {
        h: common_vendor.sr(watermark, "7751ca38-2", {
          "k": "watermark"
        }),
        i: common_vendor.o(submit),
        j: common_vendor.o(pipeline),
        k: common_vendor.p({
          reduce: true
        })
      } : currentTemplateIndex.value == 2 ? {
        m: common_vendor.sr(watermark, "7751ca38-4", {
          "k": "watermark"
        }),
        n: common_vendor.o(submit),
        o: common_vendor.o(pipeline),
        p: common_vendor.p({
          reduce: true
        })
      } : currentTemplateIndex.value == 3 ? {
        r: common_vendor.sr(watermark, "7751ca38-6", {
          "k": "watermark"
        }),
        s: common_vendor.o(submit),
        t: common_vendor.p({
          location: common_vendor.unref(locationStore).location,
          lnglat: common_vendor.unref(locationStore).latitude + "°N," + common_vendor.unref(locationStore).longitude + "°E"
        }),
        v: common_vendor.o(pipeline),
        w: common_vendor.p({
          reduce: true,
          location: common_vendor.unref(locationStore).location,
          lnglat: common_vendor.unref(locationStore).latitude + "°N," + common_vendor.unref(locationStore).longitude + "°E"
        })
      } : currentTemplateIndex.value == 4 ? {
        y: common_vendor.sr(watermark, "7751ca38-8", {
          "k": "watermark"
        }),
        z: common_vendor.o(submit),
        A: common_vendor.p({
          location: common_vendor.unref(locationStore).location
        }),
        B: common_vendor.o(pipeline),
        C: common_vendor.p({
          reduce: true,
          location: common_vendor.unref(locationStore).location
        })
      } : currentTemplateIndex.value == 5 ? {
        E: common_vendor.sr(watermark, "7751ca38-10", {
          "k": "watermark"
        }),
        F: common_vendor.o(submit),
        G: common_vendor.p({
          location: common_vendor.unref(locationStore).location
        }),
        H: common_vendor.o(pipeline),
        I: common_vendor.p({
          reduce: true,
          location: common_vendor.unref(locationStore).location
        })
      } : {}, {
        g: currentTemplateIndex.value == 1,
        l: currentTemplateIndex.value == 2,
        q: currentTemplateIndex.value == 3,
        x: currentTemplateIndex.value == 4,
        D: currentTemplateIndex.value == 5,
        J: position.value,
        K: common_vendor.o(error),
        L: common_vendor.o(($event) => openWatermarkPopup()),
        M: isAuthCamera.value
      }, isAuthCamera.value ? {
        N: common_vendor.o(($event) => takePhoto())
      } : {
        O: common_vendor.o(($event) => authCamera())
      }, {
        P: common_vendor.o(($event) => turn()),
        Q: common_vendor.f(templateList.value, (item, index, i0) => {
          return {
            a: item.img,
            b: common_vendor.o(($event) => changeWatermark(index), index),
            c: index
          };
        }),
        R: templateList.value.length % 2 != 0
      }, templateList.value.length % 2 != 0 ? {} : {}, {
        S: common_vendor.sr(watermarkPopup, "7751ca38-12", {
          "k": "watermarkPopup"
        }),
        T: common_vendor.p({
          mode: "bottom",
          round: "10",
          bgColor: "#f6f6f6",
          closeable: true
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/watermark-camera/watermark-camera.vue"]]);
wx.createPage(MiniProgramPage);
