/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bottom-box {
  height: 260rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.bottom-box .left {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bottom-box .left .watermark-icon {
  width: 68rpx;
  height: 68rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom-box .left .watermark-icon image {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
}
.bottom-box .left .icon-box {
  color: #2BBC4A;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 34rpx;
  font-weight: bold;
  border-radius: 12rpx;
  border: 7rpx solid #2BBC4A;
  box-sizing: border-box;
}
.bottom-box .left > text {
  color: #2BBC4A;
  font-size: 28rpx;
  margin-top: 10rpx;
  font-weight: bold;
}
.bottom-box .auth {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #2BBC4A;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom-box .auth > view {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 10rpx solid #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
}
.bottom-box .auth > view > text:last-of-type {
  margin-top: 4rpx;
}
.bottom-box .overturn {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bottom-box .overturn > text {
  color: #2BBC4A;
  font-size: 28rpx;
  margin-top: 10rpx;
  font-weight: bold;
}
.bottom-box .overturn .camera-icon {
  width: 68rpx;
  height: 68rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom-box .overturn .camera-icon image {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
}
.custom {
  position: absolute;
  left: 48rpx;
  bottom: 290rpx;
  color: #fff;
}
.custom .label {
  padding: 10rpx;
  background: #2BBC4A;
  font-weight: bold;
}
.popup-form {
  padding: 40rpx;
}
.watermark-popup {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  padding: 60rpx 0;
}
.watermark-item {
  width: 47%;
  height: 240rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.watermark-item image {
  width: 90%;
  height: 90%;
}