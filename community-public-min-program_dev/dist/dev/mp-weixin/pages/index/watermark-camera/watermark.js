"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const stores_store = require("../../../stores/store.js");
const common_api_index = require("../../../common/api/index.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_image2 = common_vendor.resolveComponent("uv-image");
  _easycom_uv_image2();
}
const _easycom_uv_image = () => "../../../uni_modules/uv-image/components/uv-image/uv-image.js";
if (!Math) {
  _easycom_uv_image();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "watermark",
  setup(__props) {
    const currentWatermarkIndex = common_vendor.ref(0);
    const locationStore = stores_store.useLocationStore();
    const watermarkList = common_vendor.ref([]);
    const customWatermarkList = common_vendor.ref([]);
    common_vendor.onLoad(() => {
      let list = common_vendor.index.getStorageSync("watermarkList");
      if (list) {
        customWatermarkList.value = list;
      }
      common_api_index.getWatermarkList().then((res) => {
        res.data.forEach((item) => {
          watermarkList.value.push({
            params: item.templateData ? JSON.parse(item.templateData) : "",
            ...item
          });
        });
      });
    });
    function selectCustomWatermark(item, index) {
      currentWatermarkIndex.value = Number(index);
      common_vendor.index.$emit("selectingTemplate", item);
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
    function selectWatermark(item, index) {
      currentWatermarkIndex.value = Number(index);
      common_vendor.index.$emit("selectingTemplate", {
        templateType: item.templateType,
        ...item.params
      });
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: customWatermarkList.value.length > 0
      }, customWatermarkList.value.length > 0 ? {} : {}, {
        b: common_vendor.f(customWatermarkList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.templateType == "3"
          }, item.templateType == "3" ? {
            b: common_vendor.t(item.text),
            c: item.fontSize + "rpx",
            d: item.color,
            e: item.bgColor
          } : item.templateType == "2" ? {
            g: "5ea4bc1e-0-" + i0,
            h: common_vendor.p({
              customStyle: {
                flexShrink: 0
              },
              src: item.watermarkImage,
              width: "30px",
              height: "30px"
            })
          } : {}, {
            f: item.templateType == "2",
            i: common_vendor.t(item.templateName),
            j: index,
            k: common_vendor.o(($event) => selectCustomWatermark(item, index), index)
          });
        }),
        c: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentTime)()),
        d: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentDate)()),
        e: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentWeek)()),
        f: common_vendor.t(common_vendor.unref(locationStore).location),
        g: common_vendor.f(watermarkList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.templateType == "3"
          }, item.templateType == "3" ? {
            b: common_vendor.t(item.params.text),
            c: item.params.fontSize + "px",
            d: item.params.color,
            e: item.params.bgColor
          } : item.templateType == "2" ? {
            g: "5ea4bc1e-1-" + i0,
            h: common_vendor.p({
              customStyle: {
                flexShrink: 0
              },
              src: item.params.watermarkImage,
              width: "30px",
              height: "30px"
            })
          } : {}, {
            f: item.templateType == "2",
            i: common_vendor.t(item.templateName),
            j: index,
            k: common_vendor.o(($event) => selectWatermark(item, index), index)
          });
        }),
        h: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentTime)()),
        i: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentDate)()),
        j: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentWeek)()),
        k: common_vendor.t(common_vendor.unref(locationStore).location)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/watermark-camera/watermark.vue"]]);
wx.createPage(MiniProgramPage);
