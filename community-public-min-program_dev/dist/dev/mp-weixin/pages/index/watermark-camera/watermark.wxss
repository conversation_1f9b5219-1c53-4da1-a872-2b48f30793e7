/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #F6F6F6;
}
.content {
  display: flex;
  flex-wrap: wrap;
  display: flex;
  justify-content: space-between;
  padding: 0 18rpx;
}
.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 18rpx 0 18rpx;
}
.item {
  background-color: #Fff;
  width: calc(50% - 10rpx);
  border-radius: 20rpx;
  margin-top: 30rpx;
  padding: 14rpx;
}
.item:nth-of-type(2n) {
  margin-right: 0;
}
.item .mark {
  height: 210rpx;
  background: #cccccc;
  border-radius: 20rpx;
  margin-bottom: 14rpx;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.item .mark > view {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
}
.item .mark > view .label {
  background: #2BBC4A;
  padding: 6rpx 10rpx;
}
.item .mark > view .time {
  font-size: 36rpx;
  margin: 6rpx 0;
}
.item .mark > view .date,
.item .mark > view .location {
  font-size: 24rpx;
}
.item .text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
}
.uv-checkbox-group {
  flex: none !important;
}