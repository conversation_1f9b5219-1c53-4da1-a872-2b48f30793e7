"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_enum = require("../../../common/enum.js");
const stores_store = require("../../../stores/store.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "area",
  setup(__props) {
    const url = common_vendor.ref(common_config.AREA_URL);
    const scene = common_vendor.ref("");
    const areaStore = stores_store.useAreaStore();
    const historyStore = stores_store.useHistoryStore();
    common_vendor.onLoad((options) => {
      scene.value = options.scene;
      url.value += "?history=" + JSON.stringify(historyStore.historyList);
    });
    function getMessage(e) {
      console.log("接受webview消息");
      const data = e.detail.data[e.detail.data.length - 1];
      switch (scene.value) {
        case common_enum.SceneType.MAP:
          areaStore.setArea(data.code, data.name);
          common_vendor.index.setStorageSync("latitude", data.latitude);
          common_vendor.index.setStorageSync("longitude", data.longitude);
          common_vendor.index.reLaunch({
            url: "/pages/map/guide/guide"
          });
          break;
        case common_enum.SceneType.AUTH:
          common_vendor.index.$emit("selectionRegion", data);
          break;
        default:
          areaStore.setArea(data.code, data.name);
          common_vendor.index.setStorageSync("latitude", data.latitude);
          common_vendor.index.setStorageSync("longitude", data.longitude);
          historyStore.pushHistory({
            code: data.code,
            name: data.name
          });
      }
    }
    return (_ctx, _cache) => {
      return {
        a: url.value,
        b: common_vendor.o(getMessage)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/area/area.vue"]]);
wx.createPage(MiniProgramPage);
