"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "h5",
  setup(__props) {
    const url = common_vendor.ref();
    common_vendor.onLoad((options) => {
      url.value = decodeURIComponent(options == null ? void 0 : options.url);
    });
    return (_ctx, _cache) => {
      return {
        a: url.value
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/h5/h5.vue"]]);
wx.createPage(MiniProgramPage);
