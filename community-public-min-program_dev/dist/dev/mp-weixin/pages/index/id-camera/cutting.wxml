<view class="data-v-645131eb"><view class="button data-v-645131eb"><uv-form wx:if="{{g}}" class="r data-v-645131eb" u-s="{{['d']}}" u-r="form" u-i="645131eb-0" bind:__l="__l" u-p="{{g}}"><uv-form-item wx:if="{{e}}" class="data-v-645131eb" u-s="{{['right','d']}}" bindclick="{{d}}" u-i="645131eb-1,645131eb-0" bind:__l="__l" u-p="{{e}}"><uv-input wx:if="{{b}}" class="data-v-645131eb" u-i="645131eb-2,645131eb-1" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"></uv-input><uv-icon class="data-v-645131eb" u-i="645131eb-3,645131eb-1" bind:__l="__l" u-p="{{c}}" slot="right"></uv-icon></uv-form-item></uv-form><uv-picker wx:if="{{j}}" class="r data-v-645131eb" u-r="picker" bindconfirm="{{i}}" u-i="645131eb-4" bind:__l="__l" u-p="{{j}}"></uv-picker></view><view bindtap="{{l}}" class="image data-v-645131eb"><uv-button wx:if="{{k}}" class="data-v-645131eb" u-i="645131eb-5" bind:__l="__l" u-p="{{k}}"></uv-button></view><me-cutter wx:if="{{o}}" class="data-v-645131eb" bindok="{{m}}" bindcancel="{{n}}" u-i="645131eb-6" bind:__l="__l" u-p="{{o}}"></me-cutter></view>