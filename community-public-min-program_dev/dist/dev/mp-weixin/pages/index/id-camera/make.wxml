<me-camera wx:if="{{a}}" class="data-v-b483cc60" bindphoto="{{b}}" bindshow="{{c}}" u-i="b483cc60-0" bind:__l="__l"></me-camera><view wx:else class="data-v-b483cc60"><view class="content data-v-b483cc60"><view class="form-card data-v-b483cc60"><uv-form wx:if="{{j}}" class="r data-v-b483cc60" u-s="{{['d']}}" u-r="form" u-i="b483cc60-1" bind:__l="__l" u-p="{{j}}"><uv-form-item wx:if="{{h}}" class="data-v-b483cc60" u-s="{{['right','d']}}" bindclick="{{g}}" u-i="b483cc60-2,b483cc60-1" bind:__l="__l" u-p="{{h}}"><uv-input wx:if="{{e}}" class="data-v-b483cc60" u-i="b483cc60-3,b483cc60-2" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"></uv-input><uv-icon class="data-v-b483cc60" u-i="b483cc60-4,b483cc60-2" bind:__l="__l" u-p="{{f}}" slot="right"></uv-icon></uv-form-item></uv-form><uv-picker wx:if="{{m}}" class="r data-v-b483cc60" u-r="picker" bindconfirm="{{l}}" u-i="b483cc60-5" bind:__l="__l" u-p="{{m}}"></uv-picker></view><view class="title data-v-b483cc60">图片详情</view><view class="main data-v-b483cc60"><view class="data-v-b483cc60"><text class="data-v-b483cc60">图片类型：</text><text class="data-v-b483cc60">{{n}}</text></view><view class="data-v-b483cc60"><text class="data-v-b483cc60">像素尺寸：</text><text class="data-v-b483cc60">{{o}}</text></view><view class="data-v-b483cc60"><text class="data-v-b483cc60">文件大小：</text><text class="data-v-b483cc60">无要求</text></view><view class="data-v-b483cc60"><text class="data-v-b483cc60">文件格式：</text><text class="data-v-b483cc60">jpg，png</text></view><view class="data-v-b483cc60"><text class="data-v-b483cc60">其他要求：</text><text class="data-v-b483cc60">人像整齐</text></view></view><view class="button data-v-b483cc60"><uv-button wx:if="{{q}}" class="data-v-b483cc60" bindclick="{{p}}" u-i="b483cc60-6" bind:__l="__l" u-p="{{q}}"></uv-button></view></view><me-cutter wx:if="{{t}}" class="data-v-b483cc60" bindok="{{r}}" bindcancel="{{s}}" u-i="b483cc60-7" bind:__l="__l" u-p="{{t}}"></me-cutter></view>