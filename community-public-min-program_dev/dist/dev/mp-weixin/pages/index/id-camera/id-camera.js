"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_data = require("../../../common/data.js");
const common_api_index = require("../../../common/api/index.js");
const stores_store = require("../../../stores/store.js");
const common_enum = require("../../../common/enum.js");
require("../../../common/config.js");
require("../../../common/request.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "id-camera",
  setup(__props) {
    const areaStore = stores_store.useAreaStore();
    common_vendor.onLoad(() => {
      common_api_index.addCameraUsage({
        districtCode: areaStore.areaCode,
        cameraType: common_enum.CameraType.ID_CAMERA,
        platformType: common_enum.PLATFORM_TYPE
      }).then((res) => {
        console.log("plus one.");
      });
    });
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => toPage("make")),
        b: common_vendor.o(($event) => toPage("change")),
        c: common_vendor.f(common_vendor.unref(common_data.list), (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.desc),
            c: index
          };
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/id-camera/id-camera.vue"]]);
wx.createPage(MiniProgramPage);
