
page {
		background: #f6f6f6;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-b483cc60 {
  margin: 30rpx 18rpx;
}
.content .preview.data-v-b483cc60 {
  height: 300rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f6f7fb;
  border-radius: 16rpx;
}
.content .title.data-v-b483cc60 {
  font-size: 30rp;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.content .form-card.data-v-b483cc60 {
  margin: 30rpx 0;
}
.content .main.data-v-b483cc60 {
  margin-bottom: 60rpx;
  display: flex;
  color: #333;
  flex-direction: column;
  background-color: #fff;
  padding: 0 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.content .main > view.data-v-b483cc60 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.content .main > view.data-v-b483cc60:last-of-type {
  margin-bottom: 30rpx;
}