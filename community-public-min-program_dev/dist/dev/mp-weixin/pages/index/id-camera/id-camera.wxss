/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #f6f6f6;
}
.content .heard {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin: 60rpx 0;
}
.content .heard .heard-card {
  width: 160rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}
.content .heard .heard-card > view {
  width: 180rpx;
  padding: 10rpx 0;
  border-radius: 10rpx;
  background-color: #fff;
  color: #333;
  box-shadow: 0px 2px 5px 0px rgba(153, 153, 153, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.content .heard .heard-card > image {
  width: 170rpx;
  height: 170rpx;
  flex-shrink: 0;
  margin-bottom: 40rpx;
  border-radius: 50%;
}
.content .heard .heard-card .color1 {
  box-shadow: 0px 2px 5px 0px rgba(254, 82, 64, 0.5);
}
.content .heard .heard-card .color2 {
  box-shadow: 0px 2px 5px 0px rgba(110, 110, 249, 0.5);
}
.content .heard .heard-card .color3 {
  box-shadow: 0px 2px 5px 0px rgba(36, 206, 158, 0.5);
}
.content .text {
  margin: 0 18rpx;
}
.content .text .text-heard {
  font-size: 30rpx;
  font-weight: bold;
  margin: 20rpx 0;
  color: #333;
}
.content .text .text-card {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.content .text .text-card .card-item {
  margin-bottom: 30rpx;
  padding: 18rpx;
  line-height: 50rpx;
  border-radius: 10rpx;
  background: #fefefe;
  width: calc(50% - 10rpx);
  font-size: 28rpx;
}
.content .text .text-card .card-item > view:last-of-type {
  font-size: 26rpx;
  color: #999;
}