"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_system = require("../../../common/api/system.js");
const common_utils_common = require("../../../common/utils/common.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_me_cutter2 = common_vendor.resolveComponent("me-cutter");
  (_easycom_uv_button2 + _easycom_me_cutter2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_me_cutter = () => "../../../components/me-cutter/me-cutter.js";
if (!Math) {
  (_easycom_uv_button + _easycom_me_cutter)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "change",
  setup(__props) {
    const _base64 = common_vendor.ref("");
    const data = common_vendor.reactive({
      width: 0,
      height: 0,
      size: {}
    });
    function getImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album"],
        success: (res) => {
          const src = res.tempFilePaths[0];
          common_vendor.index.getFileInfo({
            filePath: src,
            success: async (info) => {
              const compressRes = await common_utils_common.compress(info.size, src);
              const tempFilePath = compressRes.tempFilePath;
              common_vendor.index.getImageInfo({
                src: tempFilePath,
                success: (imageInfo) => {
                  data.size.value = `${imageInfo.width}*${imageInfo.height}`;
                  photo(tempFilePath);
                }
              });
            }
          });
        }
      });
    }
    function cut(base64, width, height) {
      data.width = width;
      data.height = height;
      common_api_system.parsePhoto(base64).then((res) => {
        var data2 = "data:image/png;base64," + res.foreground;
        common_api_system.base64tofile(data2).then((res2) => {
          _base64.value = res2;
        });
      });
    }
    function onok(e) {
      var base64 = e.path;
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: base64,
        success() {
          common_vendor.index.showToast({
            title: "保存成功"
          });
        }
      });
    }
    function oncancle() {
      _base64.value = "";
    }
    function photo(url) {
      const size = data.size.value.split("*");
      const base64 = "data:image/jpeg;base64," + common_vendor.wx$1.getFileSystemManager().readFileSync(url, "base64");
      cut(base64, size[0], size[1]);
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          ["custom-style"]: {
            borderRadius: "10rpx",
            boxShadow: "0rpx 5rpx 10rpx 0rpx rgba(43, 188, 74, 0.4)"
          },
          color: "#2BBC4A",
          text: "选择图片",
          icon: "photo-fill",
          iconColor: "#ffffff",
          iconSize: "34",
          size: "large"
        }),
        b: common_vendor.o(($event) => getImage()),
        c: common_vendor.o(onok),
        d: common_vendor.o(oncancle),
        e: common_vendor.p({
          maxSize: data.width,
          url: _base64.value,
          fixed: true,
          width: data.width,
          height: data.height
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d9fd1c1a"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/index/id-camera/change.vue"]]);
wx.createPage(MiniProgramPage);
