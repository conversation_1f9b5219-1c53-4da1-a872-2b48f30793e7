"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_store = require("../../stores/store.js");
const common_api_assistance = require("../../common/api/assistance.js");
const common_utils_common = require("../../common/utils/common.js");
const pages_activity_common = require("../activity/common.js");
const common_utils_tab = require("../../common/utils/tab.js");
const common_enum = require("../../common/enum.js");
require("../../common/config.js");
require("../../common/request.js");
require("../../common/api/system.js");
require("../../common/md5.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_tabs2 = common_vendor.resolveComponent("uv-tabs");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_button2 + _easycom_uv_tabs2 + _easycom_uv_icon2 + _easycom_z_paging2)();
}
const _easycom_uv_button = () => "../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_tabs = () => "../../uni_modules/uv-tabs/components/uv-tabs/uv-tabs.js";
const _easycom_uv_icon = () => "../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_tabs + _easycom_uv_icon + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "mutualAssistance",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const areaStore = stores_store.useAreaStore();
    const isShow = common_vendor.ref(false);
    const tabs = Object.freeze([
      { name: "全部", type: "all" },
      { name: "征集中", type: "ing" },
      { name: "已结束", type: "end" },
      { name: "我的发布", type: "me" },
      { name: "我参与的", type: "participate" }
    ]);
    const searchObj = common_vendor.reactive({
      wishType: 0,
      queryType: "all",
      districtCode: areaStore.areaCode,
      pageNum: 1
    });
    const dataList = common_vendor.ref([]);
    const paging = common_vendor.ref();
    const scrollHeight = common_vendor.ref();
    common_vendor.watch(
      () => areaStore.areaCode,
      (newVal, oldVal) => {
        console.log(newVal, oldVal);
        if (newVal != oldVal) {
          searchObj.pageNum = 1;
          searchObj.districtCode = newVal;
          getPageList();
        }
      }
    );
    const handleReview = (reviewStatus) => {
      switch (reviewStatus) {
        case "pending":
          return { text: "待审核", color: "blue" };
        case "approved":
          return { text: "通过", color: "green" };
        case "rejected":
          return { text: "拒绝", color: "red" };
      }
    };
    function toPage(url, id = "") {
      if (!common_vendor.index.getStorageSync("token")) {
        goLogin();
        return;
      }
      common_vendor.index.navigateTo({
        url: url + `?id=${id}`
      });
    }
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 50;
    };
    const handleTab = (tab) => {
      searchObj.wishType = tab;
      searchObj.pageNum = 1;
      getPageList();
    };
    const changeTabs = (e) => {
      searchObj.queryType = e.type;
      searchObj.pageNum = 1;
      getPageList();
    };
    const queryList = (pageNo, pageSize) => {
      searchObj.pageNum = pageNo;
      getPageList();
    };
    const getPageList = async () => {
      const res = await common_api_assistance.pageWishlists({
        ...searchObj,
        pageSize: 10
      });
      if ((res == null ? void 0 : res.code) === 200) {
        res.rows.map((item) => {
          const date = new Date(item.registrationDeadline).getTime();
          item.registrationDeadline = common_utils_common.formatDate(new Date(date), 1);
        });
        paging.value.complete(res.rows);
      } else {
        throw console.log(res);
      }
    };
    const goAuth = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未进行实名认证，是否去认证？",
        confirmText: "去认证",
        cancelText: "取消",
        confirmColor: "#2BBC4A",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/my/auth/auth"
            });
          }
        }
      });
    };
    const goLogin = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未登录，请先登录。",
        confirmText: "去登录",
        cancelText: "取消",
        confirmColor: "#2BBC4A",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.switchTab({
              url: "/pages/my/my"
            });
          }
        }
      });
    };
    common_vendor.onLoad(() => {
      var _a;
      isShow.value = true;
      if (!common_vendor.index.getStorageSync("token")) {
        goLogin();
        return;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0) {
        goAuth();
        return;
      }
      if (common_vendor.index.getStorageSync("token")) {
        getPageList();
      }
    });
    common_vendor.onShow(() => {
      var _a;
      if (!common_vendor.index.getStorageSync("token") && isShow.value) {
        goLogin();
        return;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0 && isShow.value) {
        goAuth();
      }
      getScreenHeight();
      if (common_vendor.index.getStorageSync("isBack")) {
        searchObj.pageNum = 1;
        getPageList();
        common_vendor.index.setStorageSync("isBack", false);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => toPage("/pages/subpackA/release/release")),
        b: common_vendor.t(common_vendor.unref(areaStore).areaName),
        c: common_vendor.o(($event) => toPage("/pages/index/area/area")),
        d: common_vendor.o(($event) => handleTab(0)),
        e: common_vendor.n(searchObj.wishType === 0 ? "active" : "btn"),
        f: common_vendor.o(($event) => handleTab(1)),
        g: common_vendor.n(searchObj.wishType === 1 ? "active" : "btn"),
        h: common_vendor.o(changeTabs),
        i: common_vendor.p({
          list: common_vendor.unref(tabs),
          lineColor: "#FF9F18",
          activeStyle: {
            color: "#333333",
            fontWeight: "bold",
            transform: "scale(1.05)"
          },
          inactiveStyle: {
            color: "#999999",
            transform: "scale(1)"
          }
        }),
        j: common_vendor.f(dataList.value, (item, i, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: item.reviewStatus == "pending"
          }, item.reviewStatus == "pending" ? {
            c: common_vendor.t(handleReview(item.reviewStatus).text),
            d: handleReview(item.reviewStatus).color
          } : {}, {
            e: common_vendor.t(common_vendor.unref(pages_activity_common.handleStatus)(item.status).text),
            f: common_vendor.unref(pages_activity_common.handleStatus)(item.status).color,
            g: common_vendor.t(item.content),
            h: "cd1e00ca-3-" + i0 + ",cd1e00ca-0",
            i: common_vendor.t(item.registrationDeadline),
            j: "cd1e00ca-4-" + i0 + ",cd1e00ca-0",
            k: common_vendor.t(item.participate ? item.participate : 0),
            l: common_vendor.t(item.registrationCount),
            m: common_vendor.t(item.districtName),
            n: common_vendor.o(($event) => toPage("/pages/subpackA/release/assistance-details", item.id), i),
            o: i
          });
        }),
        k: common_vendor.p({
          name: "clock-fill",
          color: "#999999",
          size: "14"
        }),
        l: common_vendor.p({
          name: "account-fill",
          color: "#999999",
          size: "14"
        }),
        m: common_vendor.sr(paging, "cd1e00ca-0", {
          "k": "paging"
        }),
        n: common_vendor.o(queryList),
        o: common_vendor.o(($event) => dataList.value = $event),
        p: common_vendor.p({
          ["paging-style"]: {
            "height": scrollHeight.value + "px"
          },
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        q: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).HOME,
        r: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME,
        s: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME ? "active" : ""),
        t: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).HOME)),
        v: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ASS,
        w: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS,
        x: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS ? "active" : ""),
        y: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ASS)),
        z: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        A: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        B: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND ? "active" : ""),
        C: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).FOUND)),
        D: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        E: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        F: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY ? "active" : ""),
        G: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY)),
        H: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).MY,
        I: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY,
        J: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY ? "active" : ""),
        K: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).MY))
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cd1e00ca"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/mutualAssistance/mutualAssistance.vue"]]);
wx.createPage(MiniProgramPage);
