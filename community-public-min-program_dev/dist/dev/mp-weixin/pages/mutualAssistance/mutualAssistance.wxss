/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.overflow-hidden.data-v-cd1e00ca {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.top.data-v-cd1e00ca {
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  padding: 24rpx 32rpx 60rpx 32rpx;
}
.top image.data-v-cd1e00ca {
  width: 686rpx;
  height: 318rpx;
  border-radius: 16rpx;
}
.top .title.data-v-cd1e00ca {
  display: flex;
  flex-direction: column;
}
.top .title view.data-v-cd1e00ca {
  margin: 4rpx 0;
}
.top .title .button.data-v-cd1e00ca {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.top .title .button text.data-v-cd1e00ca {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 48rpx;
  color: #FFFFFF;
}
.top .title .button.data-v-cd1e00ca .uv-button--info {
  font-weight: 400;
  font-size: 28rpx;
  color: #FF9F18;
  height: 60rpx !important;
  border-radius: 8rpx !important;
}
.top .title .top-location-box.data-v-cd1e00ca {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
}
.top .title .top-location-box image.data-v-cd1e00ca:first-of-type {
  width: 26rpx;
  height: 31rpx;
  margin-right: 12rpx;
}
.top .title .top-location-box image.data-v-cd1e00ca:last-of-type {
  width: 25rpx;
  height: 21rpx;
  margin-left: 22rpx;
}
.top .title .tab.data-v-cd1e00ca {
  margin-top: 16px;
}
.top .title .tab .active.data-v-cd1e00ca {
  width: 128rpx;
  height: 73rpx;
  line-height: 73rpx;
  background: url("data:image/png;base64,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");
  background-repeat: no-repeat;
  background-size: 100% 108%;
  font-weight: 500;
  font-size: 32rpx;
  color: #FF9F18;
  border: none;
  padding: 15rpx 32rpx 20rpx 32rpx;
}
.top .title .tab .btn.data-v-cd1e00ca {
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
  border-radius: 32rpx;
  border: 2rpx solid #FFFFFF;
  padding: 8rpx 26rpx;
}
.content_view.data-v-cd1e00ca {
  margin-top: -26rpx;
  padding: 28rpx 32rpx;
  background: #F5F5F5;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view .tab_content.data-v-cd1e00ca {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%, #FFFFFF 100%);
  width: 100%;
}
.content_view .tab_content .content_bg.data-v-cd1e00ca {
  margin: 16rpx 0;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 24rpx;
}
.content_view .tab_content .content_bg .mg_bottom.data-v-cd1e00ca {
  margin-bottom: 12rpx;
}
.content_view .tab_content .title_flex.data-v-cd1e00ca {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.content_view .tab_content .title.data-v-cd1e00ca {
  font-family: AppleColorEmoji;
  font-size: 34rpx;
  font-weight: 500;
  color: #222222;
  text-align: left;
  font-style: normal;
}
.content_view .tab_content .label.data-v-cd1e00ca {
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 32rpx;
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
}
.content_view .tab_content .tag.data-v-cd1e00ca {
  width: auto;
  font-weight: 400;
  font-size: 24rpx;
  color: #FF9F18;
  background: rgba(255, 159, 24, 0.2);
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
}
.content_view .tab_content .content.data-v-cd1e00ca {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #CCCCCC;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
  color: #999999;
}
.content_view .tab_content .name.data-v-cd1e00ca {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #999999;
  line-height: 46rpx;
  text-align: left;
  font-style: normal;
}
.content_view .tab_content .remark.data-v-cd1e00ca {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #999999;
  line-height: 46rpx;
  text-align: left;
  font-style: normal;
  margin-left: 10rpx;
}