"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_market = require("../../../common/api/market.js");
const common_utils_share = require("../../../common/utils/share.js");
const stores_store = require("../../../stores/store.js");
const common_api_store = require("../../../common/api/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/utils/common.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_uv_parse2 = common_vendor.resolveComponent("uv-parse");
  (_easycom_uv_swiper2 + _easycom_a_audio2 + _easycom_uv_parse2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_uv_parse = () => "../../../uni_modules/uv-parse/components/uv-parse/uv-parse.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_a_audio + _easycom_uv_parse)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "details",
  setup(__props) {
    stores_store.useAreaStore();
    stores_store.useUserStore();
    const type = common_vendor.ref(0);
    const list = common_vendor.ref([]);
    const id = common_vendor.ref("");
    const title = common_vendor.ref("");
    const data = common_vendor.ref({});
    const { setShare } = common_utils_share.useShare();
    let share = {};
    const isCurrentUser = common_vendor.computed(() => {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const currentUserId = (userInfo == null ? void 0 : userInfo.id) || (userInfo == null ? void 0 : userInfo.userId);
      return currentUserId && data.value.userId && String(currentUserId) === String(data.value.userId);
    });
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    const toMessagePage = async () => {
      try {
        const sessionParams = {
          businessId: id.value,
          // 商品ID
          businessType: "1"
          // 业务类型：跳蚤市场
        };
        const sessionResult = await common_api_store.createSession(sessionParams);
        common_vendor.index.hideLoading();
        console.log(sessionResult, "sessionResult");
        if ((sessionResult == null ? void 0 : sessionResult.code) === 200) {
          const conversationId = sessionResult.message.id || sessionResult.message.conversationId || sessionResult.message;
          common_vendor.index.navigateTo({
            url: `/pages/subpackA/market/message?conversationId=${conversationId}&goodsId=${id.value}&goodsName=${encodeURIComponent(data.value.name || "")}&coverImage=${encodeURIComponent(data.value.coverImages || "")}`
          });
        } else {
          common_vendor.index.showToast({
            title: (sessionResult == null ? void 0 : sessionResult.message) || "创建会话失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("创建会话失败:", error);
        common_vendor.index.showToast({
          title: "创建会话失败，请重试",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad((query) => {
      console.log(query, "query");
      if (query && query.id) {
        if (query.type) {
          type.value = Number(query.type);
        }
        console.log(type.value, "type");
        id.value = decodeURIComponent(query.id);
        common_api_market.getMarketDetails({
          id: id.value
        }).then((res) => {
          if (res && res.data) {
            data.value = res.data;
            if (data.value.coverImages) {
              list.value = [data.value.coverImages];
            }
            if (data.value.type == 0) {
              title.value = "跳蚤市场详情";
            } else if (data.value.type == 1) {
              title.value = "共享物品详情";
            }
            common_vendor.index.setNavigationBarTitle({
              title: title.value
            });
            share = {
              title: data.value.name || "",
              desc: "",
              imageUrl: data.value.coverImages || ""
            };
            setShare({
              weapp: {
                ...share
              }
            });
          }
        }).catch((err) => {
          console.error("获取商品详情失败", err);
        });
      }
    });
    common_vendor.onShow(() => {
      setShare({
        weapp: {
          ...share
        }
      });
    });
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.p({
          height: "500rpx",
          width: "100%",
          list: list.value
        }),
        b: common_vendor.t(data.value.name),
        c: !common_vendor.unref(isCurrentUser) && type.value === 1
      }, !common_vendor.unref(isCurrentUser) && type.value === 1 ? {
        d: common_vendor.o(toMessagePage)
      } : {}, {
        e: common_vendor.t(data.value.address),
        f: common_vendor.o(($event) => openLocation(data.value)),
        g: common_vendor.t(data.value.contactPhone),
        h: type.value === 1
      }, type.value === 1 ? {
        i: common_vendor.t(data.value.price)
      } : {}, {
        j: common_vendor.t(data.value.brief),
        k: ((_a = data.value) == null ? void 0 : _a.voiceUrl) && type.value === 1
      }, ((_b = data.value) == null ? void 0 : _b.voiceUrl) && type.value === 1 ? {
        l: common_vendor.p({
          voiceUrl: (_c = data.value) == null ? void 0 : _c.voiceUrl
        })
      } : {}, {
        m: common_vendor.t(title.value),
        n: common_vendor.p({
          content: data.value.details
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8c78346e"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/market/details.vue"]]);
wx.createPage(MiniProgramPage);
