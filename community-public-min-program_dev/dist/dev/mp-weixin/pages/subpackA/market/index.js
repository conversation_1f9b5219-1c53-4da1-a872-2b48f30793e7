"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_market = require("../../../common/api/market.js");
const common_api_system = require("../../../common/api/system.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_swiper2 + _easycom_z_paging2 + _easycom_uv_picker2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_z_paging + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const current1 = common_vendor.ref(0);
    const list = common_vendor.ref([common_utils_common.img("https://qlzhsq.qlzhsq.cn:30400/images/static/04.png")]);
    const userStore = stores_store.useUserStore();
    function apply(type2) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            } else if (res.cancel)
              ;
          }
        });
      }
      if (type2 == 0) {
        const url = "/pages/subpackA/market/add";
        common_vendor.index.navigateTo({
          url
        });
      } else {
        const url = "/pages/subpackA/market/my-add";
        common_vendor.index.navigateTo({
          url
        });
      }
    }
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    function itemClick(item) {
      const url = "/pages/subpackA/market/details?id=" + item.id + "&type=1";
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const name = common_vendor.ref("");
    const type = common_vendor.ref(0);
    const selectedArea = common_vendor.ref("");
    const selectedAreaCode = common_vendor.ref("");
    const selectedSort = common_vendor.ref("");
    const selectedPrice = common_vendor.ref("");
    common_vendor.ref(false);
    const sortPickerRef = common_vendor.ref();
    const pricePickerRef = common_vendor.ref();
    const areaPickerRef = common_vendor.ref();
    const isShowingWelfare = common_vendor.ref(false);
    const sortActions = common_vendor.ref([
      { name: "无排序", value: "none", mindType: 0 },
      { name: "发布时间排序", value: "time", mindType: 1 },
      { name: "价格高低排序", value: "price", mindType: 2 }
    ]);
    const priceActions = common_vendor.ref([
      { name: "全部", value: "all", priceType: 0 },
      { name: "免费", value: "free", priceType: 1 },
      { name: "0-50", value: "0-50", priceType: 2 },
      { name: "50-100", value: "50-100", priceType: 3 },
      { name: "100-500", value: "100-500", priceType: 4 },
      { name: "500以上", value: "500+", priceType: 5 }
    ]);
    const areaActions = common_vendor.ref([]);
    common_vendor.watch(type, () => {
      if (type.value == 0) {
        list.value = [common_utils_common.img("/static/banner/ershou.png")];
      }
      if (type.value == 1) {
        list.value = [common_utils_common.img("/static/banner/gongxiang.png")];
      }
    });
    function search() {
      paging.value.reload();
    }
    function getDataList(offset, count) {
      let mindType = 0;
      if (selectedSort.value) {
        const selectedSortOption = sortActions.value.find((item) => item.name === selectedSort.value);
        if (selectedSortOption) {
          mindType = selectedSortOption.mindType;
        }
      }
      let priceType = 0;
      if (selectedPrice.value) {
        const selectedPriceOption = priceActions.value.find((item) => item.name === selectedPrice.value);
        if (selectedPriceOption) {
          priceType = selectedPriceOption.priceType;
        }
      }
      common_api_market.getList({
        pageNum: offset,
        pageSize: count,
        name: name.value,
        type: type.value,
        districtCode: selectedAreaCode.value || "",
        //全部
        mindType,
        priceType
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    const popularList = common_vendor.ref([]);
    function getPopularListData() {
      common_api_market.getPopularList({
        pageNum: 1,
        pageSize: 4,
        type: type.value
      }).then((res) => {
        popularList.value = res.rows;
      });
    }
    function showAreaPicker() {
      console.log("显示区域选择器");
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const defaultCode = common_vendor.index.getStorageSync("areaCode") || "";
      common_api_system.getAllDistrictStreet(defaultCode).then((res) => {
        common_vendor.index.hideLoading();
        console.log("获取到的辖区街道数据:", res);
        if (res && res.data && Array.isArray(res.data)) {
          areaActions.value = res.data.map((item) => ({
            name: item.name || item.streetName || item.districtName || "未知",
            value: item.code || item.streetCode || item.districtCode || "",
            code: item.code || item.streetCode || item.districtCode || ""
          }));
          if (areaActions.value.length > 0) {
            areaActions.value.unshift({ name: "全部", value: "", code: "" });
          }
          areaPickerRef.value.open();
        } else {
          common_vendor.index.showToast({
            title: "暂无数据",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.hideLoading();
        console.error("获取辖区街道失败:", error);
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      });
    }
    function showSortPicker() {
      console.log("显示排序选择器");
      sortPickerRef.value.open();
    }
    function onSortSelect(item) {
      selectedSort.value = item.value[0].name;
      console.log("选择的排序方式:", item.value[0]);
      paging.value.reload();
    }
    function showPricePicker() {
      console.log("显示价格选择器");
      pricePickerRef.value.open();
    }
    function onPriceSelect(item) {
      selectedPrice.value = item.value[0].name;
      console.log("选择的价格区间:", item.value[0]);
      isShowingWelfare.value = item.value[0].name === "免费";
      paging.value.reload();
    }
    function onAreaSelect(item) {
      selectedArea.value = item.value[0].name;
      selectedAreaCode.value = item.value[0].code;
      console.log("选择的辖区:", item.value[0]);
      paging.value.reload();
    }
    function showWelfareItems() {
      if (isShowingWelfare.value) {
        console.log("显示全部商品");
        selectedPrice.value = "";
        isShowingWelfare.value = false;
      } else {
        console.log("显示公益物品");
        selectedPrice.value = "免费";
        isShowingWelfare.value = true;
      }
      paging.value.reload();
    }
    common_vendor.onLoad(() => {
      getPopularListData();
      common_vendor.index.$on("refreshList", search);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("refreshList", search);
    });
    return (_ctx, _cache) => {
      return {
        a: name.value,
        b: common_vendor.o(($event) => name.value = $event.detail.value),
        c: common_vendor.o(($event) => search()),
        d: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.n(index === current1.value && "indicator__dot--active")
          };
        }),
        e: common_vendor.o((e) => current1.value = e.current),
        f: common_vendor.p({
          height: "300rpx",
          list: list.value,
          autoplay: false
        }),
        g: common_vendor.o(($event) => apply(1)),
        h: common_vendor.t(isShowingWelfare.value ? "全部商品" : "公益物品"),
        i: common_vendor.o(showWelfareItems),
        j: common_vendor.o(($event) => apply(0)),
        k: common_vendor.f(popularList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        l: common_vendor.t(selectedArea.value || "辖区"),
        m: common_vendor.o(showAreaPicker),
        n: common_vendor.t(selectedSort.value || "智能排序"),
        o: common_vendor.o(showSortPicker),
        p: common_vendor.t(selectedPrice.value || "价格"),
        q: common_vendor.o(showPricePicker),
        r: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: common_vendor.t(item.brief),
            e: common_vendor.t(item.address),
            f: common_vendor.o(($event) => openLocation(item), index),
            g: common_vendor.t(item.contactPhone),
            h: common_vendor.o(() => {
            }, index),
            i: item.price === "0"
          }, item.price === "0" ? {} : {}, {
            j: item.price === "0"
          }, item.price === "0" ? {} : {
            k: common_vendor.t(item.price)
          }, {
            l: index,
            m: common_vendor.o(($event) => itemClick(item), index)
          });
        }),
        s: common_vendor.sr(paging, "9c3e97fc-0", {
          "k": "paging"
        }),
        t: common_vendor.o(queryList),
        v: common_vendor.o(($event) => dataList.value = $event),
        w: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        x: common_vendor.sr(sortPickerRef, "9c3e97fc-2", {
          "k": "sortPickerRef"
        }),
        y: common_vendor.o(onSortSelect),
        z: common_vendor.p({
          columns: [sortActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        }),
        A: common_vendor.sr(pricePickerRef, "9c3e97fc-3", {
          "k": "pricePickerRef"
        }),
        B: common_vendor.o(onPriceSelect),
        C: common_vendor.p({
          columns: [priceActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        }),
        D: common_vendor.sr(areaPickerRef, "9c3e97fc-4", {
          "k": "areaPickerRef"
        }),
        E: common_vendor.o(onAreaSelect),
        F: common_vendor.p({
          columns: [areaActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9c3e97fc"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/market/index.vue"]]);
wx.createPage(MiniProgramPage);
