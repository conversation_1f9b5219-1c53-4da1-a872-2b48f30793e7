/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.top {
  padding: 8px 0 16px 16px;
  text-align: left;
  color: #ffffff;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.top .title {
  font-weight: 500;
  font-size: 48rpx;
  color: #FFFFFF;
  line-height: 64rpx;
  margin-bottom: 4rpx;
}
.top .top-location-box {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
}
.top .top-location-box image:first-of-type {
  width: 29rpx;
  height: 29rpx;
  margin-right: 4rpx;
}
.goods-info {
  width: 100%;
  padding: 12px 16px 16px 16px;
  box-sizing: border-box;
  margin-top: -10px;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
  background: #fff;
}
.goods-info .goods-box {
  display: flex;
  align-items: center;
}
.goods-info .goods-box .goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.goods-info .goods-box .goods-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.goods-info .goods-box .goods-details .goods-name {
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 40rpx;
  margin-bottom: 6rpx;
}
.goods-info .goods-box .goods-details .goods-id {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
}
.tips_msg {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
  padding: 16rpx 24rpx;
  background-color: #F5F5F5;
  border-radius: 16rpx;
}
.content_body {
  background-color: white;
}
.content_body scroll-view {
  background-color: #F5F5F5;
  padding: 32rpx 0 0 0;
}
.content_body .flex_row_end {
  display: flex;
  justify-content: flex-end;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin-bottom: 32rpx;
}
.content_body .flex_row_start {
  display: flex;
  justify-content: flex-start;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin-bottom: 32rpx;
}
.content_body .play_record {
  padding: 6px 6px;
  margin: 4px 4px;
}
.content_body .right_border {
  border-radius: 16rpx 16rpx 0rpx 16rpx;
  background: rgba(255, 159, 24, 0.2);
  align-self: flex-end;
}
.content_body .left_border {
  border-radius: 16rpx 16rpx 16rpx 0rpx;
  background: #FFFFFF;
  align-self: flex-start;
}
.content_body .self {
  max-width: 80%;
  padding: 16rpx 24rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  display: inline-block;
  word-break: break-all;
  white-space: normal;
  width: auto !important;
  box-sizing: border-box;
}
.search_input {
  margin: 0 auto;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #EEEEEE;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 100rpx;
}
.search_input .input-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search_input .icon_svg {
  width: 40rpx;
  height: 40rpx;
  color: #999;
}
.search_input .uni-input {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  background: #F5F5F5;
  padding: 0 24rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  line-height: 70rpx; /* 确保文字垂直居中 */
}
.search_input .voice-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  background: #F5F5F5;
  margin: 0 10rpx;
  font-size: 28rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}
.search_input .send-btn {
  width: 70rpx;
  height: 58rpx;
  line-height: 58rpx;
  border-radius: 29rpx;
  text-align: center;
  font-size: 24rpx;
  background: #2878FF;
  color: white;
}
.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 调整placeholder的样式 */
::-moz-placeholder {
  color: #999;
}
::placeholder {
  color: #999;
}
page {
  background-color: #F5F5F5;
}
.recording-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.recording-mask .recording-text {
  background: white;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: black;
}