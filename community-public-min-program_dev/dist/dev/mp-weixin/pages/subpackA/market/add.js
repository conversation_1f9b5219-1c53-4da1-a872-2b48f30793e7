"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../common/config.js");
const common_api_market = require("../../../common/api/market.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_upload_img2 = common_vendor.resolveComponent("upload-img");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_a_record2 = common_vendor.resolveComponent("a-record");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_upload_img2 + _easycom_uv_textarea2 + _easycom_a_audio2 + _easycom_a_record2 + _easycom_uv_button2 + _easycom_uv_form2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_upload_img = () => "../../../components/upload-img/upload-img.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_a_record = () => "../../../components/a-record/a-record.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_upload_img + _easycom_uv_textarea + _easycom_a_audio + _easycom_a_record + _easycom_uv_button + _easycom_uv_form)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "add",
  setup(__props) {
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    const rules = Object.freeze({
      name: {
        type: "string",
        required: true,
        message: "请填写名称",
        trigger: ["blur", "change"]
      },
      districtCode: {
        type: "string",
        required: true,
        message: "请选择社区",
        trigger: ["blur", "change"]
      },
      brief: {
        type: "string",
        required: true,
        message: "请填写描述",
        trigger: ["blur", "change"]
      },
      address: {
        type: "string",
        required: true,
        message: "请填写具体位置",
        trigger: ["blur", "change"]
      },
      coverImages: {
        type: "string",
        required: true,
        message: "请上传封面图",
        trigger: ["blur", "change"]
      },
      images: {
        type: "string",
        required: true,
        message: "请上传详情图",
        trigger: ["blur", "change"],
        validator: (rule, value, callback) => {
          if (!value || value.length == 0) {
            callback(new Error("请上传详情图！"));
          } else {
            callback();
          }
        }
      },
      contactPhone: {
        type: "string",
        required: true,
        message: "请输入正确的联系电话",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      },
      price: [
        { required: true, message: "价格不能为空", trigger: ["blur", "change"] },
        { required: true, pattern: /^(\d+.?)?\d{0,2}$/, message: "请输入数字或者2位的小数", trigger: ["blur", "change"] }
      ]
    });
    const fileList = common_vendor.ref([]);
    const coverImages = common_vendor.ref([]);
    const audioFile = common_vendor.ref({});
    const form = common_vendor.reactive({
      id: "",
      name: "",
      coverImages: "",
      districtCode: "",
      districtName: "",
      brief: "",
      images: [],
      type: 0,
      longitude: 0,
      latitude: 0,
      address: "",
      contactPhone: "",
      price: "",
      details: "",
      voiceId: null
    });
    const formRef = common_vendor.ref();
    const getCurrentLocation = () => {
      common_vendor.index.chooseLocation({
        success: function(res) {
          form.longitude = res.longitude;
          form.latitude = res.latitude;
          form.address = res.address;
        },
        fail(e) {
          console.log(e);
        }
      });
    };
    const getAudio = (res) => {
      form.voiceId = res.ossId;
      audioFile.value = res;
    };
    const submit = async () => {
      form.images = [];
      fileList.value.length && fileList.value.map((item) => {
        form.images.push(item.url);
      });
      coverImages.value.length && coverImages.value.map((item) => {
        form.coverImages = item.url;
      });
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        common_api_market.add(form).then((res) => {
          common_vendor.index.hideLoading();
          common_vendor.index.navigateBack({
            success: function() {
              common_vendor.index.$emit("refreshList", {});
            }
          });
        });
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    common_vendor.onLoad((options) => {
      if (options.id) {
        let id = decodeURIComponent(options.id);
        common_api_market.getMarketDetails({
          id
        }).then((res) => {
          Object.assign(form, res.data);
          form.name = res.data.name;
          let imgRegex = /<img\s+(?:[^>]*?\s+)?src\s*=\s*(['"])(.*?)\1/gi;
          let match;
          while ((match = imgRegex.exec(res.data.details)) !== null) {
            fileList.value.push({ url: match[2] });
          }
          coverImages.value.push({ url: form.coverImages });
          common_vendor.index.setNavigationBarTitle({
            title: "修改二手闲置"
          });
        });
      }
      common_vendor.index.$on("selectionRegion", areaSetting);
    });
    function areaSetting(data) {
      if (data.code) {
        form.districtCode = data.code;
      }
      if (data.name) {
        form.districtName = data.name;
      }
    }
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion", areaSetting);
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return {
        a: common_vendor.o(($event) => form.districtCode = $event),
        b: common_vendor.p({
          ["custom-style"]: "display:none",
          modelValue: form.districtCode
        }),
        c: common_vendor.o(($event) => form.districtName = $event),
        d: common_vendor.p({
          readonly: true,
          placeholder: "请选择社区",
          modelValue: form.districtName
        }),
        e: common_vendor.o(($event) => toPage("/pages/index/area/area?scene=auth")),
        f: common_vendor.p({
          label: "社区",
          prop: "districtCode",
          required: true
        }),
        g: common_vendor.o(($event) => form.name = $event),
        h: common_vendor.p({
          placeholder: "商品名称",
          modelValue: form.name
        }),
        i: common_vendor.p({
          label: "名称",
          prop: "name",
          required: true
        }),
        j: common_vendor.o(($event) => coverImages.value = $event),
        k: common_vendor.p({
          ["max-count"]: 1,
          modelValue: coverImages.value
        }),
        l: common_vendor.p({
          label: "封面",
          prop: "coverImages",
          required: true
        }),
        m: common_vendor.o(($event) => form.brief = $event),
        n: common_vendor.p({
          maxlength: "255",
          showConfirmBar: false,
          placeholder: "请输入商品的简要描述(最多200字)",
          modelValue: form.brief
        }),
        o: common_vendor.p({
          label: "描述",
          prop: "brief",
          required: true
        }),
        p: common_vendor.o(($event) => fileList.value = $event),
        q: common_vendor.p({
          ["max-count"]: 5,
          multiple: true,
          modelValue: fileList.value
        }),
        r: common_vendor.p({
          label: "详情图片",
          prop: "images",
          required: true
        }),
        s: common_vendor.p({
          voiceUrl: (_a = audioFile.value) == null ? void 0 : _a.url,
          duration: (_b = audioFile.value) == null ? void 0 : _b.duration
        }),
        t: common_vendor.o(getAudio),
        v: common_vendor.p({
          label: "语音介绍"
        }),
        w: common_vendor.o(getCurrentLocation),
        x: common_vendor.o(($event) => form.address = $event),
        y: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择具体位置",
          suffixIcon: "map-fill",
          suffixIconStyle: "color: #909399",
          modelValue: form.address
        }),
        z: common_vendor.p({
          label: "具体位置",
          prop: "address",
          required: true
        }),
        A: common_vendor.o(($event) => form.price = $event),
        B: common_vendor.p({
          type: "number",
          placeholder: "请输入价格",
          modelValue: form.price
        }),
        C: common_vendor.p({
          label: "价格",
          prop: "price",
          required: true
        }),
        D: common_vendor.o(($event) => form.contactPhone = $event),
        E: common_vendor.p({
          type: "number",
          placeholder: "请输入联系电话",
          modelValue: form.contactPhone
        }),
        F: common_vendor.p({
          label: "联系电话",
          prop: "contactPhone",
          required: true
        }),
        G: common_vendor.o(submit),
        H: common_vendor.p({
          type: "warning",
          text: "提交审核",
          customStyle: "margin-top: 10px"
        }),
        I: common_vendor.sr(formRef, "8b7a85aa-0", {
          "k": "formRef"
        }),
        J: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8b7a85aa"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/market/add.vue"]]);
wx.createPage(MiniProgramPage);
