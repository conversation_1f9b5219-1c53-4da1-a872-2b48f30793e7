<z-paging wx:if="{{f}}" class="r data-v-35f9f858" u-s="{{['top','d']}}" u-r="paging" bindquery="{{d}}" u-i="35f9f858-0" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><view class="data-v-35f9f858" style="background:linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);padding:0 1rem 0 1rem" slot="top"><view class="data-v-35f9f858" color="#2BBC4A" style="padding:20rpx 0"><view class="data-v-35f9f858" style="justify-content:left;display:flex;flex-wrap:wrap"><view class="btn normal data-v-35f9f858" bindtap="{{a}}">发布二手闲置</view></view></view></view><view wx:for="{{b}}" wx:for-item="item" wx:key="y" class="data-v-35f9f858" style="border-bottom:solid 1px #EEEEEE;padding:20rpx"><view class="list data-v-35f9f858" style="border-bottom:none;margin:0"><view class="data-v-35f9f858"><image class="data-v-35f9f858" mode="aspectFill" style="height:300rpx;width:28vw" src="{{item.a}}"></image></view><view class="info data-v-35f9f858"><view class="data-v-35f9f858" style="display:flex;align-items:center;padding:0;flex-wrap:wrap;column-gap:10rpx"><view class="title data-v-35f9f858" style="margin-right:28rpx">{{item.b}}</view><view class="data-v-35f9f858" style="color:#FF9F18;background:rgb(255 159 24 / 10%);padding:8rpx">{{item.c}}</view></view><view class="desc data-v-35f9f858"><uv-text wx:if="{{item.e}}" class="data-v-35f9f858" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></uv-text></view><view class="grey data-v-35f9f858" style="align-items:center;display:flex"><image class="icon data-v-35f9f858" src="/static/tourLine/location.png" mode=""></image><uv-text wx:if="{{item.g}}" class="data-v-35f9f858" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"></uv-text></view><view class="grey data-v-35f9f858" style="align-items:center;display:flex"><image class="icon data-v-35f9f858" src="/static/tourLine/person.png" mode=""></image><uv-text wx:if="{{item.i}}" class="data-v-35f9f858" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"></uv-text></view><view class="grey data-v-35f9f858" style="align-items:center;display:flex"><view class="data-v-35f9f858">状态：</view><uv-text wx:if="{{item.j}}" class="data-v-35f9f858" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"></uv-text><uv-text wx:if="{{item.m}}" class="data-v-35f9f858" u-i="{{item.n}}" bind:__l="__l" u-p="{{item.o}}"></uv-text><uv-text wx:if="{{item.p}}" class="data-v-35f9f858" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"></uv-text></view><view wx:if="{{item.s}}" class="grey data-v-35f9f858" style="align-items:center;display:flex"><view class="data-v-35f9f858">原因：</view><uv-text wx:if="{{item.v}}" class="data-v-35f9f858" u-i="{{item.t}}" bind:__l="__l" u-p="{{item.v}}"></uv-text></view></view></view><view class="data-v-35f9f858" style="justify-content:left;display:flex"><view class="btn normal data-v-35f9f858" style="background:#FF9F18" bindtap="{{item.w}}">修改</view><view class="btn normal data-v-35f9f858" style="background:#FFCD35" bindtap="{{item.x}}">删除</view></view></view></z-paging><a-popup wx:if="{{i}}" class="r data-v-35f9f858" u-r="deleteModal" bindemitEvent="{{h}}" u-i="35f9f858-8" bind:__l="__l" u-p="{{i}}"></a-popup>