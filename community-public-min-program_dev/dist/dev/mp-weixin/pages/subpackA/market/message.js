"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_api_store = require("../../../common/api/store.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/request.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "message",
  setup(__props) {
    const { proxy } = common_vendor.getCurrentInstance();
    stores_store.useAreaStore();
    const userStore = stores_store.useUserStore();
    let scrollHeight = common_vendor.ref(), textRef = common_vendor.ref(false);
    let recordOrText = common_vendor.ref(true), isRecord = common_vendor.ref(false);
    const conversationId = common_vendor.ref("");
    const goodsInfo = common_vendor.ref({});
    let searchObj = common_vendor.reactive({
      keyWord: "",
      list: [],
      pageNum: 1,
      total: 0
    });
    const scrolltoupper = () => {
      searchObj.pageNum += 1;
      let remainder = Math.floor(searchObj.total / 12);
      let template = searchObj.total % 12 > 0 ? 1 : 0;
      if (searchObj.pageNum > remainder + template) {
        return;
      }
      getMessageList();
    };
    const getMessageList = async () => {
      try {
        const result = await common_api_store.listMessage(conversationId.value);
        if ((result == null ? void 0 : result.code) === 200) {
          console.log("获取消息列表成功:", result);
          if (result.rows && Array.isArray(result.rows)) {
            const sentMessages = searchObj.list.filter((msg) => {
              var _a;
              return !msg.isTemp && msg.sendUserId === ((_a = userStore.userInfo) == null ? void 0 : _a.userId);
            });
            const sentMessageIds = sentMessages.map((msg) => msg.id);
            const serverMessages = result.rows.map((item) => {
              var _a;
              const isSentByMe = sentMessageIds.includes(item.id);
              console.log(`消息ID: ${item.id}, 服务器返回的sendUserId: ${item.sendUserId}, 是否为自己发送: ${isSentByMe}`);
              return {
                ...item,
                // 保留服务器返回的原始sendUserId，不要修改
                // 如果服务器没有返回sendUserId，则根据是否为自己发送来设置
                sendUserId: item.sendUserId || (isSentByMe ? (_a = userStore.userInfo) == null ? void 0 : _a.userId : "other-user"),
                isPlay: false,
                // 确保每条消息都有contentType，没有则默认为text
                contentType: item.contentType || "text"
              };
            });
            const uniqueServerMessages = serverMessages.filter((msg) => !sentMessageIds.includes(msg.id));
            const allMessages = [...sentMessages, ...uniqueServerMessages];
            searchObj.list = allMessages.sort((a, b) => {
              return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
            });
          }
        } else {
          console.error("获取消息列表失败:", result);
        }
      } catch (error) {
        console.error("获取消息列表出错:", error);
      }
    };
    const sendMsgPost = async (content, type) => {
      var _a, _b, _c;
      if (!conversationId.value) {
        console.log("没有会话ID，无法发送消息");
        common_vendor.index.showToast({
          title: "会话无效，请返回重试",
          icon: "none"
        });
        return;
      }
      try {
        const tempMsg = {
          id: "temp_" + Date.now(),
          // 临时ID
          content,
          contentType: type,
          sendUserId: (_a = userStore.userInfo) == null ? void 0 : _a.userId,
          createTime: new Date().toISOString(),
          isTemp: true,
          // 标记为临时消息
          isPlay: false
        };
        searchObj.list.push(tempMsg);
        const result = await common_api_store.sendMessage({
          conversationsId: conversationId.value,
          content,
          contentType: type
        });
        if ((result == null ? void 0 : result.code) === 200) {
          console.log("发送消息成功:", result);
          searchObj.keyWord = "";
          const index = searchObj.list.findIndex((item) => item.id === tempMsg.id);
          if (index !== -1) {
            searchObj.list[index] = {
              ...tempMsg,
              id: ((_b = result.data) == null ? void 0 : _b.id) || tempMsg.id,
              // 如果有返回id则使用，否则保留临时id
              isTemp: false,
              // 不再是临时消息
              createTime: ((_c = result.data) == null ? void 0 : _c.createTime) || tempMsg.createTime
            };
          }
        } else {
          console.error("发送消息失败:", result);
          searchObj.list = searchObj.list.filter((msg) => msg.id !== tempMsg.id);
          common_vendor.index.showToast({
            title: "发送失败",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("发送消息失败:", err);
        searchObj.list = searchObj.list.filter((msg) => msg.id !== "temp_" + Date.now());
        common_vendor.index.showToast({
          title: "发送失败",
          icon: "none"
        });
      }
    };
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 180;
    };
    const innerAudioContext = common_vendor.index.createInnerAudioContext();
    const playRecord = (item) => {
      searchObj.list.forEach((msg) => {
        msg.isPlay = false;
      });
      item.isPlay = true;
      innerAudioContext.src = item.content;
      innerAudioContext.play();
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            console.log(uploadFileRes);
            const responseData = JSON.parse(uploadFileRes.data);
            if (responseData && responseData.data) {
              resolve(responseData.data);
            } else {
              reject(new Error("上传响应格式错误"));
            }
          },
          fail: function(err) {
            reject(err);
          }
        });
      });
    };
    const hidden = () => {
      recordOrText.value = false;
      textRef.value = false;
    };
    const showKeyboard = () => {
      recordOrText.value = true;
      proxy.$nextTick(() => {
        textRef.value = true;
      });
    };
    const recorderManager = common_vendor.index.getRecorderManager();
    const startRecord = () => {
      console.log("开始录音");
      isRecord.value = true;
      recorderManager.start({
        duration: 6e4
      });
    };
    const endRecord = () => {
      console.log("录音结束");
      recorderManager.stop();
      isRecord.value = false;
    };
    common_vendor.onLoad((option) => {
      console.log("接收到的参数:", option);
      if (option.conversationId) {
        conversationId.value = option.conversationId;
      }
      goodsInfo.value = {
        goodsId: option.goodsId || "",
        goodsName: option.goodsName ? decodeURIComponent(option.goodsName) : "",
        coverImage: option.coverImage ? decodeURIComponent(option.coverImage) : ""
      };
      recorderManager.onStop(async (res) => {
        console.log("recorder stop" + JSON.stringify(res), res.duration);
        if (res.duration <= 1e3) {
          common_vendor.index.showLoading({
            title: "说话声音太短",
            mask: true
          });
          setTimeout(() => {
            common_vendor.index.hideLoading();
          }, 800);
        } else {
          try {
            const uploadRes = await uploadFile(res.tempFilePath);
            sendMsgPost(uploadRes.url, "audio");
          } catch (err) {
            console.error("上传录音失败:", err);
            common_vendor.index.showToast({
              title: "上传失败",
              icon: "none"
            });
          }
        }
      });
      getScreenHeight();
      getMessageList();
      innerAudioContext.onEnded(() => {
        searchObj.list.forEach((item) => {
          item.isPlay = false;
        });
      });
    });
    common_vendor.onUnload(() => {
      innerAudioContext.pause();
      innerAudioContext.destroy();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: goodsInfo.value.coverImage || "../../../static/my/avatar.png",
        b: common_vendor.t(goodsInfo.value.goodsName || "商品详情"),
        c: goodsInfo.value.goodsId
      }, goodsInfo.value.goodsId ? {
        d: common_vendor.t(goodsInfo.value.goodsId)
      } : {}, {
        e: common_vendor.f(common_vendor.unref(searchObj).list, (item, i, i0) => {
          var _a, _b, _c, _d;
          return {
            a: "../../../static/found/" + (item.isPlay ? "playing.png" : "sound.png"),
            b: item.sendUserId === ((_a = common_vendor.unref(userStore).userInfo) == null ? void 0 : _a.userId) ? "scaleX(-1)" : "none",
            c: common_vendor.o(($event) => playRecord(item), i),
            d: item.contentType === "audio",
            e: common_vendor.n(item.sendUserId === ((_b = common_vendor.unref(userStore).userInfo) == null ? void 0 : _b.userId) ? "right_border" : "left_border"),
            f: common_vendor.t(item.content),
            g: item.contentType === "text",
            h: common_vendor.n(item.sendUserId === ((_c = common_vendor.unref(userStore).userInfo) == null ? void 0 : _c.userId) ? "right_border" : "left_border"),
            i: common_vendor.n(item.sendUserId === ((_d = common_vendor.unref(userStore).userInfo) == null ? void 0 : _d.userId) ? "flex_row_end" : "flex_row_start"),
            j: i
          };
        }),
        f: common_vendor.o(scrolltoupper),
        g: common_vendor.unref(scrollHeight) + "px",
        h: common_vendor.unref(recordOrText)
      }, common_vendor.unref(recordOrText) ? {
        i: common_vendor.o(hidden)
      } : {}, {
        j: !common_vendor.unref(recordOrText)
      }, !common_vendor.unref(recordOrText) ? {
        k: common_vendor.o(showKeyboard)
      } : {}, {
        l: !common_vendor.unref(recordOrText)
      }, !common_vendor.unref(recordOrText) ? {
        m: common_vendor.o(startRecord),
        n: common_vendor.o(endRecord)
      } : {
        o: common_vendor.unref(textRef),
        p: common_vendor.o(($event) => sendMsgPost(common_vendor.unref(searchObj).keyWord, "text")),
        q: common_vendor.unref(searchObj).keyWord,
        r: common_vendor.o(($event) => common_vendor.unref(searchObj).keyWord = $event.detail.value)
      }, {
        s: !common_vendor.unref(searchObj).keyWord
      }, !common_vendor.unref(searchObj).keyWord ? {
        t: common_vendor.o(($event) => common_vendor.isRef(recordOrText) ? recordOrText.value = true : recordOrText = true)
      } : {
        v: common_vendor.o(($event) => sendMsgPost(common_vendor.unref(searchObj).keyWord, "text"))
      }, {
        w: common_vendor.unref(isRecord)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/market/message.vue"]]);
wx.createPage(MiniProgramPage);
