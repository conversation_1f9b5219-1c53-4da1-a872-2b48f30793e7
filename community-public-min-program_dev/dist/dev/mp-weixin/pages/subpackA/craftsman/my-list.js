"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_craftsman = require("../../../common/api/craftsman.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_button2 + _easycom_uv_text2 + _easycom_z_paging2 + _easycom_uv_picker2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_text + _easycom_z_paging + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "my-list",
  setup(__props) {
    function itemClick(item) {
      const url = "/pages/subpackA/craftsman/details?id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const name = common_vendor.ref("");
    const selectedSort = common_vendor.ref("");
    const selectedOrder = common_vendor.ref("");
    const sortPickerRef = common_vendor.ref();
    const orderPickerRef = common_vendor.ref();
    const sortActions = common_vendor.ref([
      { name: "默认", value: "", orderByColumn: "" },
      { name: "按名称", value: "name", orderByColumn: "name" },
      { name: "按创建时间", value: "createTime", orderByColumn: "create_time" },
      { name: "按排序号", value: "sort", orderByColumn: "sort" }
    ]);
    const orderActions = common_vendor.ref([
      { name: "升序", value: "asc", isAsc: "asc" },
      { name: "降序", value: "desc", isAsc: "desc" }
    ]);
    function search() {
      paging.value.reload();
    }
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    function getDataList(offset, count) {
      let orderByColumn = "";
      let isAsc = "";
      if (selectedSort.value) {
        const selectedSortOption = sortActions.value.find((item) => item.name === selectedSort.value);
        if (selectedSortOption) {
          orderByColumn = selectedSortOption.orderByColumn;
        }
      }
      if (selectedOrder.value) {
        const selectedOrderOption = orderActions.value.find((item) => item.name === selectedOrder.value);
        if (selectedOrderOption) {
          isAsc = selectedOrderOption.isAsc;
        }
      }
      const params = {
        pageNum: offset,
        pageSize: count
      };
      if (name.value.trim()) {
        params.name = name.value;
      }
      if (orderByColumn) {
        params.orderByColumn = orderByColumn;
      }
      if (isAsc) {
        params.isAsc = isAsc;
      }
      common_api_craftsman.getMyList(params).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    function onSortSelect(item) {
      selectedSort.value = item.value[0].name;
      console.log("选择的排序字段:", item.value[0]);
      paging.value.reload();
    }
    function onOrderSelect(item) {
      selectedOrder.value = item.value[0].name;
      console.log("选择的升降序:", item.value[0]);
      paging.value.reload();
    }
    common_vendor.onLoad(() => {
      common_vendor.index.$on("refreshList", () => {
        paging.value.reload();
      });
    });
    common_vendor.onShow(() => {
      if (paging.value) {
        paging.value.reload();
      }
    });
    return (_ctx, _cache) => {
      return {
        a: name.value,
        b: common_vendor.o(($event) => name.value = $event.detail.value),
        c: common_vendor.o(($event) => search()),
        d: common_vendor.p({
          type: "warning",
          customStyle: {
            height: "50rpx"
          }
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: "3da415c6-2-" + i0 + ",3da415c6-0",
            e: common_vendor.p({
              lines: 2,
              size: "14",
              text: item.brief
            }),
            f: common_vendor.t(item.address),
            g: common_vendor.o(($event) => openLocation(item), index),
            h: common_vendor.t(item.contactPhone),
            i: common_vendor.o(() => {
            }, index),
            j: common_vendor.t(item.status === "1" ? "启用" : "禁用"),
            k: common_vendor.n(item.status === "1" ? "status-active" : "status-inactive"),
            l: index,
            m: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        f: common_vendor.sr(paging, "3da415c6-0", {
          "k": "paging"
        }),
        g: common_vendor.o(queryList),
        h: common_vendor.o(($event) => dataList.value = $event),
        i: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        j: common_vendor.sr(sortPickerRef, "3da415c6-3", {
          "k": "sortPickerRef"
        }),
        k: common_vendor.o(onSortSelect),
        l: common_vendor.p({
          columns: [sortActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        }),
        m: common_vendor.sr(orderPickerRef, "3da415c6-4", {
          "k": "orderPickerRef"
        }),
        n: common_vendor.o(onOrderSelect),
        o: common_vendor.p({
          columns: [orderActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3da415c6"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/craftsman/my-list.vue"]]);
wx.createPage(MiniProgramPage);
