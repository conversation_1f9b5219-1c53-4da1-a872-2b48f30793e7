"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_craftsman = require("../../../common/api/craftsman.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_button2 + _easycom_uv_swiper2 + _easycom_uv_text2 + _easycom_z_paging2 + _easycom_uv_picker2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_swiper + _easycom_uv_text + _easycom_z_paging + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const current1 = common_vendor.ref(0);
    const list = common_vendor.ref([common_utils_common.img("https://qlzhsq.qlzhsq.cn:30400/images/static/gongjiang.png")]);
    function itemClick(item) {
      const url = "/pages/subpackA/craftsman/details?id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    function goToAdd() {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/craftsman/add"
      });
    }
    function goToMyList() {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/craftsman/my-list"
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const name = common_vendor.ref("");
    common_vendor.ref("");
    const selectedSort = common_vendor.ref("");
    const selectedCategory = common_vendor.ref("");
    const sortPickerRef = common_vendor.ref();
    const categoryPickerRef = common_vendor.ref();
    const sortActions = common_vendor.ref([
      { name: "无排序", value: "none", sortType: 0 },
      { name: "发布时间排序", value: "time", sortType: 1 },
      { name: "评分排序", value: "rating", sortType: 2 }
    ]);
    const categoryActions = common_vendor.ref([
      { name: "全部", value: "all", categoryType: 0 },
      { name: "维修服务", value: "repair", categoryType: 1 },
      { name: "家政服务", value: "housekeeping", categoryType: 2 },
      { name: "教育培训", value: "education", categoryType: 3 },
      { name: "健康医疗", value: "health", categoryType: 4 },
      { name: "其他服务", value: "other", categoryType: 5 }
    ]);
    function search() {
      paging.value.reload();
    }
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    function getDataList(offset, count) {
      let sortType = 0;
      if (selectedSort.value) {
        const selectedSortOption = sortActions.value.find((item) => item.name === selectedSort.value);
        if (selectedSortOption) {
          sortType = selectedSortOption.sortType;
        }
      }
      let categoryType = 0;
      if (selectedCategory.value) {
        const selectedCategoryOption = categoryActions.value.find((item) => item.name === selectedCategory.value);
        if (selectedCategoryOption) {
          categoryType = selectedCategoryOption.categoryType;
        }
      }
      common_api_craftsman.getList({
        pageNum: offset,
        pageSize: count,
        name: name.value,
        // districtCode:'',//全部
        sortType,
        categoryType
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    function onSortSelect(item) {
      selectedSort.value = item.value[0].name;
      console.log("选择的排序方式:", item.value[0]);
      paging.value.reload();
    }
    function onCategorySelect(item) {
      selectedCategory.value = item.value[0].name;
      console.log("选择的分类:", item.value[0]);
      paging.value.reload();
    }
    const popularList = common_vendor.ref([]);
    function getPopularListData() {
      common_api_craftsman.getPopularList({
        pageNum: 1,
        pageSize: 4
      }).then((res) => {
        popularList.value = res.rows;
      });
    }
    common_vendor.onLoad(() => {
      getPopularListData();
    });
    return (_ctx, _cache) => {
      return {
        a: name.value,
        b: common_vendor.o(($event) => name.value = $event.detail.value),
        c: common_vendor.o(($event) => search()),
        d: common_vendor.p({
          type: "warning",
          customStyle: {
            height: "50rpx"
          }
        }),
        e: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.n(index === current1.value && "indicator__dot--active")
          };
        }),
        f: common_vendor.o((e) => current1.value = e.current),
        g: common_vendor.p({
          height: "300rpx",
          list: list.value,
          autoplay: false
        }),
        h: common_vendor.o(goToMyList),
        i: common_vendor.o(goToAdd),
        j: common_vendor.f(popularList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        k: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: "62d57596-3-" + i0 + ",62d57596-0",
            e: common_vendor.p({
              lines: 2,
              size: "14",
              text: item.brief
            }),
            f: common_vendor.t(item.address),
            g: common_vendor.o(($event) => openLocation(item), index),
            h: common_vendor.t(item.contactPhone),
            i: common_vendor.o(() => {
            }, index),
            j: index,
            k: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        l: common_vendor.sr(paging, "62d57596-0", {
          "k": "paging"
        }),
        m: common_vendor.o(queryList),
        n: common_vendor.o(($event) => dataList.value = $event),
        o: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        p: common_vendor.sr(sortPickerRef, "62d57596-4", {
          "k": "sortPickerRef"
        }),
        q: common_vendor.o(onSortSelect),
        r: common_vendor.p({
          columns: [sortActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        }),
        s: common_vendor.sr(categoryPickerRef, "62d57596-5", {
          "k": "categoryPickerRef"
        }),
        t: common_vendor.o(onCategorySelect),
        v: common_vendor.p({
          columns: [categoryActions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-62d57596"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/craftsman/index.vue"]]);
wx.createPage(MiniProgramPage);
