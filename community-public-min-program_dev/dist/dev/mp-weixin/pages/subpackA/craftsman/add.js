"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_craftsman = require("../../../common/api/craftsman.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_upload_img2 = common_vendor.resolveComponent("upload-img");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_upload_img2 + _easycom_uv_textarea2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_uv_picker2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_upload_img = () => "../../../components/upload-img/upload-img.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_upload_img + _easycom_uv_textarea + _easycom_uv_button + _easycom_uv_form + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "add",
  setup(__props) {
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    const statusOptions = common_vendor.ref([
      { name: "启用", value: "0" },
      { name: "禁用", value: "1" }
    ]);
    const statusPickerRef = common_vendor.ref();
    function showStatusPicker() {
      statusPickerRef.value.open();
    }
    function onStatusSelect(item) {
      form.status = item.value[0].value;
      form.statusName = item.value[0].name;
    }
    const rules = Object.freeze({
      name: {
        type: "string",
        required: true,
        message: "请填写名称",
        trigger: ["blur", "change"]
      },
      districtCode: {
        type: "string",
        required: true,
        message: "请选择社区",
        trigger: ["blur", "change"]
      },
      address: {
        type: "string",
        required: true,
        message: "请填写地址",
        trigger: ["blur", "change"]
      },
      contactPhone: {
        type: "string",
        required: true,
        message: "请输入正确的联系电话",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      },
      coverImages: {
        type: "string",
        required: true,
        message: "请上传封面",
        trigger: ["blur", "change"]
      },
      status: {
        type: "string",
        required: true,
        message: "请选择状态",
        trigger: ["blur", "change"]
      },
      brief: {
        type: "string",
        required: true,
        message: "请填写简介",
        trigger: ["blur", "change"]
      },
      details: {
        type: "string",
        required: true,
        message: "请填写详情",
        trigger: ["blur", "change"]
      }
    });
    const coverImages = common_vendor.ref([]);
    const form = common_vendor.reactive({
      id: null,
      name: "",
      coverImages: "",
      brief: "",
      details: "",
      contactPhone: "",
      address: "",
      longitude: "",
      latitude: "",
      districtCode: "",
      districtName: "",
      browseNumber: 0,
      sort: null,
      status: "",
      statusName: ""
    });
    const formRef = common_vendor.ref();
    const getCurrentLocation = () => {
      common_vendor.index.chooseLocation({
        success: function(res) {
          form.longitude = res.longitude.toString();
          form.latitude = res.latitude.toString();
          form.address = res.address;
        },
        fail(e) {
          console.log(e);
        }
      });
    };
    const submit = async () => {
      coverImages.value.length && coverImages.value.map((item) => {
        form.coverImages = item.url;
      });
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        const submitData = {
          id: form.id,
          name: form.name,
          coverImages: form.coverImages,
          brief: form.brief,
          details: form.details,
          contactPhone: form.contactPhone,
          address: form.address,
          longitude: form.longitude,
          latitude: form.latitude,
          districtCode: form.districtCode,
          browseNumber: form.browseNumber,
          sort: form.sort,
          status: form.status
        };
        common_api_craftsman.add(submitData).then((res) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "提交成功，等待审核",
            icon: "success"
          });
          common_vendor.index.navigateBack({
            success: function() {
              common_vendor.index.$emit("refreshList", {});
            }
          });
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "提交失败，请重试",
            icon: "error"
          });
        });
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.id) {
        let id = decodeURIComponent(options.id);
        common_api_craftsman.getCraftsmanDetails({
          id
        }).then((res) => {
          Object.assign(form, res.data);
          coverImages.value.push({ url: form.coverImages });
          const status = statusOptions.value.find((item) => item.value === form.status);
          if (status) {
            form.statusName = status.name;
          }
          common_vendor.index.setNavigationBarTitle({
            title: "修改匠人服务"
          });
        });
      }
      common_vendor.index.$on("selectionRegion", areaSetting);
    });
    function areaSetting(data) {
      if (data.code) {
        form.districtCode = data.code;
      }
      if (data.name) {
        form.districtName = data.name;
      }
    }
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion", areaSetting);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => form.name = $event),
        b: common_vendor.p({
          placeholder: "请输入服务名称",
          modelValue: form.name
        }),
        c: common_vendor.p({
          label: "名称",
          prop: "name",
          required: true
        }),
        d: common_vendor.o(($event) => form.districtCode = $event),
        e: common_vendor.p({
          ["custom-style"]: "display:none",
          modelValue: form.districtCode
        }),
        f: common_vendor.o(($event) => form.districtName = $event),
        g: common_vendor.p({
          readonly: true,
          placeholder: "请选择社区",
          modelValue: form.districtName
        }),
        h: common_vendor.o(($event) => toPage("/pages/index/area/area?scene=auth")),
        i: common_vendor.p({
          label: "社区",
          prop: "districtCode",
          required: true
        }),
        j: common_vendor.o(getCurrentLocation),
        k: common_vendor.o(($event) => form.address = $event),
        l: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择地址",
          suffixIcon: "map-fill",
          suffixIconStyle: "color: #909399",
          modelValue: form.address
        }),
        m: common_vendor.p({
          label: "地址",
          prop: "address",
          required: true
        }),
        n: common_vendor.o(($event) => form.contactPhone = $event),
        o: common_vendor.p({
          type: "number",
          placeholder: "请输入联系电话",
          modelValue: form.contactPhone
        }),
        p: common_vendor.p({
          label: "联系电话",
          prop: "contactPhone",
          required: true
        }),
        q: common_vendor.o(($event) => form.sort = $event),
        r: common_vendor.p({
          type: "number",
          placeholder: "请输入排序号（数字越小越靠前）",
          modelValue: form.sort
        }),
        s: common_vendor.p({
          label: "排序",
          prop: "sort"
        }),
        t: common_vendor.o(($event) => coverImages.value = $event),
        v: common_vendor.p({
          ["max-count"]: 1,
          modelValue: coverImages.value
        }),
        w: common_vendor.p({
          label: "封面",
          prop: "coverImages",
          required: true
        }),
        x: common_vendor.o(($event) => form.statusName = $event),
        y: common_vendor.p({
          ["custom-style"]: "display:none",
          modelValue: form.statusName
        }),
        z: common_vendor.o(($event) => form.statusName = $event),
        A: common_vendor.p({
          readonly: true,
          placeholder: "请选择状态",
          suffixIcon: "arrow-down",
          modelValue: form.statusName
        }),
        B: common_vendor.o(showStatusPicker),
        C: common_vendor.p({
          label: "状态",
          prop: "status",
          required: true
        }),
        D: common_vendor.o(($event) => form.brief = $event),
        E: common_vendor.p({
          maxlength: "500",
          showConfirmBar: false,
          placeholder: "请输入简介(最多500字)",
          modelValue: form.brief
        }),
        F: common_vendor.p({
          label: "简介",
          prop: "brief",
          required: true
        }),
        G: common_vendor.o(($event) => form.details = $event),
        H: common_vendor.p({
          maxlength: "1000",
          showConfirmBar: false,
          placeholder: "请输入详情(最多1000字)",
          modelValue: form.details
        }),
        I: common_vendor.p({
          label: "详情",
          prop: "details",
          required: true
        }),
        J: common_vendor.o(submit),
        K: common_vendor.p({
          type: "warning",
          text: "提交审核",
          customStyle: "margin-top: 10px"
        }),
        L: common_vendor.sr(formRef, "6d577094-0", {
          "k": "formRef"
        }),
        M: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        }),
        N: common_vendor.sr(statusPickerRef, "6d577094-22", {
          "k": "statusPickerRef"
        }),
        O: common_vendor.o(onStatusSelect),
        P: common_vendor.p({
          columns: [statusOptions.value],
          keyName: "name",
          confirmColor: "#FF9F18"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6d577094"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/craftsman/add.vue"]]);
wx.createPage(MiniProgramPage);
