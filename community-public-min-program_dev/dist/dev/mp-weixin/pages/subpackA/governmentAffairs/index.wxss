/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.title.data-v-6670ef2a {
  font-weight: bold;
  color: #FFFFFF;
  text-align: left;
  font-size: 24px;
  padding-bottom: 1rem;
}
.search.data-v-6670ef2a {
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 15rpx;
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.2);
}
.custom-input.data-v-6670ef2a {
  width: 100%;
  height: 100%;
  color: #ffffff;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0 10rpx;
}
.u-wrap.data-v-6670ef2a {
  height: calc(100vh - 250rpx + 50rpx);
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 50rpx;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -50rpx;
  overflow: hidden;
}
.u-menu-wrap.data-v-6670ef2a {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.u-tab-view.data-v-6670ef2a {
  width: 300rpx;
  height: 100%;
}
.u-tab-item.data-v-6670ef2a {
  height: 110rpx;
  background: #f6f6f6;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  line-height: 1;
  padding: 0 10px;
}
.u-tab-item-active.data-v-6670ef2a {
  position: relative;
  color: #FF9F18;
  font-size: 28rpx;
  font-weight: 600;
  background: #fff;
}
.u-tab-view.data-v-6670ef2a {
  height: 100%;
}
.right-box.data-v-6670ef2a {
  background-color: rgb(250, 250, 250);
}
.page-view.data-v-6670ef2a {
  padding: 0;
}
.class-item.data-v-6670ef2a {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 16rpx;
  border-radius: 8rpx;
}
.item-title.data-v-6670ef2a {
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  padding: 10px 0;
}
.item-menu-name.data-v-6670ef2a {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  width: 100%;
}
.item-container.data-v-6670ef2a {
  display: flex;
  flex-wrap: wrap;
}
.thumb-box.data-v-6670ef2a {
  width: 33.333333%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 20rpx;
}
.item-menu-image.data-v-6670ef2a {
  width: 120rpx;
  height: 120rpx;
}

/* 全局样式，确保占位符文本为白色 */
.white-placeholder {
  color: #ffffff !important;
}
