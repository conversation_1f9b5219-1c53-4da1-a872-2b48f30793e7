"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_governmentAffairs = require("../../../common/api/governmentAffairs.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  _easycom_uv_button2();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
if (!Math) {
  _easycom_uv_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const dataList = common_vendor.ref([]);
    const scrollTop = common_vendor.ref(0);
    const current = common_vendor.ref(0);
    const menuHeight = common_vendor.ref(0);
    const menuItemHeight = common_vendor.ref(0);
    const name = common_vendor.ref("");
    common_vendor.onLoad((query) => {
      getDataList().then(() => {
        if (query && query.itemName) {
          name.value = decodeURIComponent(query.itemName);
          const index = findMenuIndex(name.value);
          if (index !== -1) {
            console.log(`找到匹配的菜单项，索引: ${index}, 名称: ${name.value}`);
            setTimeout(async () => {
              await initElementSize();
              swichMenu(index);
              onMenuItemVisible(index);
              setTimeout(() => {
                calculateScrollPosition(index);
              }, 500);
            }, 500);
          }
        }
      });
    });
    common_vendor.onReady(() => {
      if (current.value > 0) {
        console.log("onReady: 再次尝试滚动到指定位置", current.value);
        setTimeout(async () => {
          await initElementSize();
          calculateScrollPosition(current.value);
        }, 300);
      }
    });
    function findMenuIndex(searchName) {
      if (!searchName || !dataList.value || !dataList.value.length)
        return -1;
      const exactIndex = dataList.value.findIndex(
        (item) => item.itemName === searchName
      );
      if (exactIndex !== -1)
        return exactIndex;
      const fuzzyIndex = dataList.value.findIndex(
        (item) => item.itemName && item.itemName.includes(searchName)
      );
      return fuzzyIndex;
    }
    function getDataList() {
      return common_api_governmentAffairs.getList({}).then((res) => {
        if (res && res.data) {
          dataList.value = res.data;
        }
        return res;
      });
    }
    const callPhone = (phone) => {
      common_vendor.index.makePhoneCall({
        phoneNumber: phone
      });
    };
    const swichMenu = async (index) => {
      if (index == current.value)
        return;
      current.value = index;
      if (menuHeight.value == 0 || menuItemHeight.value == 0) {
        try {
          await initElementSize();
        } catch (e) {
          console.error("初始化元素尺寸失败:", e);
        }
      }
      calculateScrollPosition(index);
    };
    const initElementSize = async () => {
      await getElRect("menu-scroll-view", "menuHeight");
      await getElRect("u-tab-item", "menuItemHeight");
      return true;
    };
    const calculateScrollPosition = (index) => {
      if (menuHeight.value > 0 && menuItemHeight.value > 0) {
        scrollTop.value = index * menuItemHeight.value + menuItemHeight.value / 2 - menuHeight.value / 2;
        if (scrollTop.value < 0)
          scrollTop.value = 0;
        console.log(`滚动到位置: ${scrollTop.value}, 索引: ${index}, 菜单高度: ${menuHeight.value}, 项目高度: ${menuItemHeight.value}`);
      } else {
        console.log("无法计算滚动位置，元素尺寸未初始化");
        scrollTop.value = Math.max(index * 110 - 150, 0);
      }
    };
    const getElRect = (elClass, dataVal) => {
      return new Promise((resolve, reject) => {
        const query = common_vendor.index.createSelectorQuery();
        query.select("." + elClass).boundingClientRect((res) => {
          if (!res) {
            setTimeout(() => {
              getElRect(elClass, dataVal).then(resolve).catch(reject);
            }, 100);
            return;
          }
          if (dataVal === "menuHeight") {
            menuHeight.value = res.height;
            resolve(res.height);
          } else if (dataVal === "menuItemHeight") {
            menuItemHeight.value = res.height;
            resolve(res.height);
          } else {
            resolve(0);
          }
        }).exec();
      });
    };
    const onMenuItemVisible = (index) => {
      console.log(`菜单项${index}可见，确保内容也可见`);
      common_vendor.nextTick$1(() => {
        try {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".right-box").boundingClientRect().exec((res) => {
            if (res && res[0]) {
              const rightBox = res[0];
              console.log("右侧内容区域信息:", rightBox);
            }
          });
        } catch (e) {
          console.error("尝试滚动右侧内容失败:", e);
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: name.value,
        b: common_vendor.o(($event) => name.value = $event.detail.value),
        c: common_vendor.o(($event) => getDataList()),
        d: common_vendor.p({
          type: "warning",
          customStyle: {
            height: "50rpx"
          }
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.itemName),
            b: index,
            c: common_vendor.n(current.value == index ? "u-tab-item-active" : ""),
            d: index,
            e: common_vendor.o(($event) => swichMenu(index), index)
          };
        }),
        f: scrollTop.value,
        g: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: current.value == index
          }, current.value == index ? common_vendor.e({
            b: common_vendor.t(item.itemName),
            c: common_vendor.t(item.timeLimit),
            d: common_vendor.t(item.contactPhone),
            e: common_vendor.o(($event) => callPhone(item.contactPhone), index),
            f: common_vendor.t(item.materials),
            g: item.remarks
          }, item.remarks ? {
            h: common_vendor.t(item.remarks)
          } : {}) : {}, {
            i: index
          });
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6670ef2a"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/governmentAffairs/index.vue"]]);
wx.createPage(MiniProgramPage);
