/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.community-circle.data-v-b22812c5 {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}
.status-bar.data-v-b22812c5 {
  background-color: #fff;
}
.section-title-text.data-v-b22812c5 {
  padding: 0 32rpx 32rpx;
  display: none;
}
.section-title-t2.data-v-b22812c5 {
  font-size: 24rpx;
  color: #999;
  display: block;
}
.more-text-have-active.data-v-b22812c5 {
  border: 2rpx solid #FE9A24;
  background: #fff !important;
  color: #FE9A24 !important;
}
.navbar.data-v-b22812c5 {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.navbar .navbar-content.data-v-b22812c5 {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}
.navbar .navbar-content .navbar-left.data-v-b22812c5 {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar .navbar-content .navbar-title.data-v-b22812c5 {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}
.navbar .navbar-content .navbar-right.data-v-b22812c5 {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.content.data-v-b22812c5 {
  flex: 1;
  box-sizing: border-box;
}
.banner.data-v-b22812c5 {
  position: relative;
  height: 240px;
  padding: 32rpx;
  overflow: hidden;
  background: #FBA62D;
}
.banner .banner-bg.data-v-b22812c5 {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}
.banner .banner-content.data-v-b22812c5 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.banner .banner-content .banner-title.data-v-b22812c5 {
  display: block;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
}
.banner .banner-content .banner-subtitle.data-v-b22812c5 {
  font-size: 16px;
  color: #fff;
  opacity: 0.9;
}
.search-section.data-v-b22812c5 {
  padding: 30rpx 32rpx;
  margin-bottom: 16px;
  background: #fff;
}
.search-section .search-container.data-v-b22812c5 {
  display: flex;
  align-items: center;
  gap: 12px;
}
.search-section .search-container .search-box.data-v-b22812c5 {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
}
.search-section .search-container .search-box .search-input.data-v-b22812c5 {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}
.search-section .search-container .switch-community.data-v-b22812c5 {
  display: flex;
  align-items: center;
}
.search-section .search-container .switch-community .switch-icon.data-v-b22812c5 {
  width: 30rpx;
  height: 30rpx;
}
.search-section .search-container .switch-community .switch-text.data-v-b22812c5 {
  font-size: 14px;
  color: #FF9F18;
}
.section.data-v-b22812c5 {
  margin-bottom: 24px;
}
.section .section-header.data-v-b22812c5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 16px;
}
.section .section-header .section-title.data-v-b22812c5 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.section .section-header .section-more.data-v-b22812c5 {
  display: flex;
  align-items: center;
  gap: 4px;
}
.section .section-header .section-more .more-text.data-v-b22812c5 {
  font-size: 24rpx;
  color: #333;
}
.section .section-header .section-more .circle-text.data-v-b22812c5 {
  font-size: 28rpx;
  font-weight: 500;
  padding: 10rpx 20rpx;
  background: #FE9A24;
  border-radius: 50rpx;
  color: #fff;
}
.circle-list.data-v-b22812c5 {
  padding: 0 16px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.circle-list .circle-item.data-v-b22812c5 {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 12px;
}
.circle-list .circle-item .circle-avatar.data-v-b22812c5 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
}
.circle-list .circle-item .circle-info.data-v-b22812c5 {
  flex: 1;
}
.circle-list .circle-item .circle-info .circle-name.data-v-b22812c5 {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.circle-list .circle-item .circle-info .circle-count.data-v-b22812c5 {
  font-size: 12px;
  color: #999;
}
.topic-list.data-v-b22812c5 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 16px;
}
.topic-list .topic-item.data-v-b22812c5 {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20px;
  padding: 8px 16px;
}
.topic-list .topic-item .topic-icon.data-v-b22812c5 {
  width: 20px;
  height: 20px;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.topic-list .topic-item .topic-icon .topic-icon-text.data-v-b22812c5 {
  font-size: 28rpx;
  color: #fff;
}
.topic-list .topic-item .topic-name.data-v-b22812c5 {
  font-size: 28rpx;
  color: #333;
}
.content-grid.data-v-b22812c5 {
  display: flex;
  justify-content: space-between;
  gap: 8rpx;
  padding: 0 32rpx;
  background: #f5f5f5;
}
.content-grid .content-item.data-v-b22812c5 {
  position: relative;
  width: 220rpx;
  height: 220rpx;
  border-radius: 8px;
  overflow: hidden;
}
.content-grid .content-item .content-image.data-v-b22812c5 {
  width: 100%;
  height: 100%;
}
.content-grid .content-item .content-overlay.data-v-b22812c5 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 8px;
}
.content-grid .content-item .content-overlay .content-user.data-v-b22812c5 {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.content-grid .content-item .content-overlay .content-user .user-avatar.data-v-b22812c5 {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  margin-right: 4px;
}
.content-grid .content-item .content-overlay .content-user .user-name.data-v-b22812c5 {
  font-size: 10px;
  color: #fff;
}
.content-grid .content-item .content-overlay .content-text.data-v-b22812c5 {
  font-size: 10px;
  color: #fff;
  line-height: 1.2;
}
.safe-bottom.data-v-b22812c5 {
  height: 34px;
}