/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.publish-page.data-v-9d31c241 {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.publish-page .content.data-v-9d31c241 {
  flex: 1;
}
.publish-page .content .form-container.data-v-9d31c241 {
  padding: 32rpx;
}
.publish-page .content .form-container .page-title.data-v-9d31c241 {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 40rpx;
}
.publish-page .content .form-container .form-item.data-v-9d31c241 {
  margin-bottom: 40rpx;
}
.publish-page .content .form-container .form-item .form-label.data-v-9d31c241 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.publish-page .content .form-container .form-item .form-label .label-text.data-v-9d31c241 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.publish-page .content .form-container .form-item .form-label .required.data-v-9d31c241 {
  color: #ff4757;
  margin-left: 8rpx;
  font-size: 28rpx;
}
.publish-page .content .form-container .form-item .input-wrapper.data-v-9d31c241 {
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
}
.publish-page .content .form-container .form-item .input-wrapper .form-input.data-v-9d31c241 {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}
.publish-page .content .form-container .form-item .input-wrapper .form-input.data-v-9d31c241::-moz-placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .input-wrapper .form-input.data-v-9d31c241::placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .select-wrapper.data-v-9d31c241 {
  display: flex;
  gap: 16rpx;
}
.publish-page .content .form-container .form-item .select-wrapper .select-item.data-v-9d31c241 {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
}
.publish-page .content .form-container .form-item .select-wrapper .select-item .form-input.data-v-9d31c241 {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}
.publish-page .content .form-container .form-item .select-wrapper .select-item .form-input.data-v-9d31c241::-moz-placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .select-wrapper .select-item .form-input.data-v-9d31c241::placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .select-wrapper .search-btn.data-v-9d31c241 {
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 140rpx;
  justify-content: center;
}
.publish-page .content .form-container .form-item .select-wrapper .search-btn .search-text.data-v-9d31c241 {
  font-size: 24rpx;
  color: #999;
}
.publish-page .content .form-container .form-item .add-tag-btn.data-v-9d31c241 {
  margin-top: 16rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  align-self: flex-start;
}
.publish-page .content .form-container .form-item .add-tag-btn .add-text.data-v-9d31c241 {
  font-size: 24rpx;
  color: #999;
}
.publish-page .content .form-container .form-item .textarea-wrapper.data-v-9d31c241 {
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
}
.publish-page .content .form-container .form-item .textarea-wrapper .form-textarea.data-v-9d31c241 {
  width: 100%;
  min-height: 200rpx;
  font-size: 28rpx;
  color: #333;
}
.publish-page .content .form-container .form-item .textarea-wrapper .form-textarea.data-v-9d31c241::-moz-placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .textarea-wrapper .form-textarea.data-v-9d31c241::placeholder {
  color: #999;
}
.publish-page .content .form-container .form-item .textarea-wrapper .char-count.data-v-9d31c241 {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
}
.publish-page .content .form-container .form-item .textarea-wrapper .char-count .count-text.data-v-9d31c241 {
  font-size: 24rpx;
  color: #666;
}
.publish-page .content .form-container .form-item .textarea-wrapper .char-count .tip-text.data-v-9d31c241 {
  font-size: 24rpx;
  color: #999;
}
.publish-page .content .media-section.data-v-9d31c241 {
  margin-bottom: 40rpx;
}
.publish-page .content .media-section .media-btns.data-v-9d31c241 {
  display: flex;
  gap: 32rpx;
  margin-bottom: 24rpx;
}
.publish-page .content .media-section .media-btns .media-btn.data-v-9d31c241 {
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}
.publish-page .content .media-section .media-btns .media-btn .media-text.data-v-9d31c241 {
  font-size: 26rpx;
  color: #666;
}
.publish-page .content .media-section .image-list.data-v-9d31c241 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.publish-page .content .media-section .image-list .image-item.data-v-9d31c241 {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}
.publish-page .content .media-section .image-list .image-item .preview-image.data-v-9d31c241 {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.publish-page .content .media-section .image-list .image-item .delete-btn.data-v-9d31c241 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.publish-page .content .privacy-section.data-v-9d31c241 {
  padding: 24rpx;
}
.publish-page .content .privacy-section .privacy-text.data-v-9d31c241 {
  font-size: 26rpx;
  color: #666;
}
.publish-page .publish-footer.data-v-9d31c241 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
}
.publish-page .publish-footer .publish-btn.data-v-9d31c241 {
  background-color: #FBA62D;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}
.publish-page .publish-footer .publish-btn .publish-text.data-v-9d31c241 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}