"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_icon2 + _easycom_uv_picker2)();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "publish",
  setup(__props) {
    const form = common_vendor.ref({
      title: "",
      circleId: "",
      topicId: "",
      content: "",
      images: [],
      videos: []
    });
    const circlePickerRef = common_vendor.ref();
    const topicPickerRef = common_vendor.ref();
    const selectedCircleName = common_vendor.ref("");
    const selectedTopicName = common_vendor.ref("");
    const circleOptions = common_vendor.ref([
      { id: 1, name: "社区骑游" },
      { id: 2, name: "社区钓鱼" },
      { id: 3, name: "社区舞蹈" },
      { id: 4, name: "邻里互助" },
      { id: 5, name: "美食分享" }
    ]);
    const topicOptions = common_vendor.ref([
      { id: 1, name: "邛崃", color: "#00C4DF" },
      { id: 2, name: "骑行邀约", color: "#FF519A" },
      { id: 3, name: "钓鱼组团", color: "#FE9A24" },
      { id: 4, name: "奇舞团舞蹈队日常", color: "#1990FF" },
      { id: 5, name: "环保", color: "#38A86B" },
      { id: 6, name: "美食", color: "#9A5DFF" }
    ]);
    common_vendor.onLoad(() => {
      console.log("发布页面加载");
    });
    const showCirclePicker = () => {
      circlePickerRef.value.open();
    };
    const showTopicPicker = () => {
      topicPickerRef.value.open();
    };
    const onCircleSelect = (item) => {
      selectedCircleName.value = item.value[0].name;
      form.value.circleId = item.value[0].id;
      console.log("选择的圈子:", item.value[0]);
    };
    const onTopicSelect = (item) => {
      selectedTopicName.value = item.value[0].name;
      form.value.topicId = item.value[0].id;
      console.log("选择的话题:", item.value[0]);
    };
    const searchCircle = () => {
      common_vendor.index.showToast({
        title: "搜索圈子功能",
        icon: "none"
      });
    };
    const searchTopic = () => {
      common_vendor.index.showToast({
        title: "搜索话题功能",
        icon: "none"
      });
    };
    const addNewTag = () => {
      common_vendor.index.showModal({
        title: "添加新标签",
        content: "是否添加新的话题标签？",
        success: (res) => {
          if (res.confirm) {
            console.log("添加新标签");
          }
        }
      });
    };
    const addImage = () => {
      common_vendor.index.chooseImage({
        count: 9 - form.value.images.length,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          form.value.images.push(...res.tempFilePaths);
        }
      });
    };
    const addVideo = () => {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 60,
        success: (res) => {
          form.value.videos.push(res.tempFilePath);
          common_vendor.index.showToast({
            title: "视频添加成功",
            icon: "success"
          });
        }
      });
    };
    const deleteImage = (index) => {
      form.value.images.splice(index, 1);
    };
    const submitPublish = () => {
      if (!form.value.title.trim()) {
        common_vendor.index.showToast({
          title: "请填写动态标题",
          icon: "none"
        });
        return;
      }
      if (!form.value.circleId) {
        common_vendor.index.showToast({
          title: "请选择所属圈子",
          icon: "none"
        });
        return;
      }
      if (!form.value.topicId) {
        common_vendor.index.showToast({
          title: "请选择话题标签",
          icon: "none"
        });
        return;
      }
      if (!form.value.content.trim()) {
        common_vendor.index.showToast({
          title: "请填写动态内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "发布中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1e3);
      }, 2e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: form.value.title,
        b: common_vendor.o(($event) => form.value.title = $event.detail.value),
        c: selectedCircleName.value,
        d: common_vendor.o(($event) => selectedCircleName.value = $event.detail.value),
        e: common_vendor.o(showCirclePicker),
        f: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        g: common_vendor.o(searchCircle),
        h: selectedTopicName.value,
        i: common_vendor.o(($event) => selectedTopicName.value = $event.detail.value),
        j: common_vendor.o(showTopicPicker),
        k: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        l: common_vendor.o(searchTopic),
        m: common_vendor.p({
          name: "plus",
          color: "#999",
          size: "16"
        }),
        n: common_vendor.o(addNewTag),
        o: form.value.content,
        p: common_vendor.o(($event) => form.value.content = $event.detail.value),
        q: common_vendor.t(form.value.content.length),
        r: common_vendor.p({
          name: "camera",
          color: "#999",
          size: "24"
        }),
        s: common_vendor.o(addImage),
        t: common_vendor.p({
          name: "play-circle",
          color: "#999",
          size: "24"
        }),
        v: common_vendor.o(addVideo),
        w: form.value.images.length > 0
      }, form.value.images.length > 0 ? {
        x: common_vendor.f(form.value.images, (img, index, i0) => {
          return {
            a: img,
            b: "9d31c241-5-" + i0,
            c: common_vendor.o(($event) => deleteImage(index), index),
            d: index
          };
        }),
        y: common_vendor.p({
          name: "close",
          color: "#fff",
          size: "16"
        })
      } : {}, {
        z: common_vendor.o(submitPublish),
        A: common_vendor.sr(circlePickerRef, "9d31c241-6", {
          "k": "circlePickerRef"
        }),
        B: common_vendor.o(onCircleSelect),
        C: common_vendor.p({
          columns: [circleOptions.value],
          keyName: "name",
          confirmColor: "#FBA62D"
        }),
        D: common_vendor.sr(topicPickerRef, "9d31c241-7", {
          "k": "topicPickerRef"
        }),
        E: common_vendor.o(onTopicSelect),
        F: common_vendor.p({
          columns: [topicOptions.value],
          keyName: "name",
          confirmColor: "#FBA62D"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9d31c241"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/community-circle/publish.vue"]]);
wx.createPage(MiniProgramPage);
