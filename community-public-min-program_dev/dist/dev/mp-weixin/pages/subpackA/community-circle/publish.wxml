<view class="publish-page data-v-9d31c241"><scroll-view class="content data-v-9d31c241" scroll-y="true"><view class="form-container data-v-9d31c241"><text class="page-title data-v-9d31c241">发布社区圈子动态</text><view class="form-item data-v-9d31c241"><view class="form-label data-v-9d31c241"><text class="label-text data-v-9d31c241">圈子动态标题</text><text class="required data-v-9d31c241">*</text></view><view class="input-wrapper data-v-9d31c241"><input class="form-input data-v-9d31c241" placeholder="填写圈子动态标题" maxlength="50" value="{{a}}" bindinput="{{b}}"/></view></view><view class="form-item data-v-9d31c241"><view class="form-label data-v-9d31c241"><text class="label-text data-v-9d31c241">所属圈子</text><text class="required data-v-9d31c241">*</text></view><view class="select-wrapper data-v-9d31c241"><view class="select-item data-v-9d31c241" bindtap="{{e}}"><input class="form-input data-v-9d31c241" placeholder="选择所属圈子" disabled value="{{c}}" bindinput="{{d}}"/></view><view class="search-btn data-v-9d31c241" bindtap="{{g}}"><uv-icon wx:if="{{f}}" class="data-v-9d31c241" u-i="9d31c241-0" bind:__l="__l" u-p="{{f}}"></uv-icon><text class="search-text data-v-9d31c241">搜索圈子</text></view></view></view><view class="form-item data-v-9d31c241"><view class="form-label data-v-9d31c241"><text class="label-text data-v-9d31c241">话题标签</text><text class="required data-v-9d31c241">*</text></view><view class="select-wrapper data-v-9d31c241"><view class="select-item data-v-9d31c241" bindtap="{{j}}"><input class="form-input data-v-9d31c241" placeholder="选择话题标签" disabled value="{{h}}" bindinput="{{i}}"/></view><view class="search-btn data-v-9d31c241" bindtap="{{l}}"><uv-icon wx:if="{{k}}" class="data-v-9d31c241" u-i="9d31c241-1" bind:__l="__l" u-p="{{k}}"></uv-icon><text class="search-text data-v-9d31c241">搜索话题</text></view></view><view class="add-tag-btn data-v-9d31c241" bindtap="{{n}}"><uv-icon wx:if="{{m}}" class="data-v-9d31c241" u-i="9d31c241-2" bind:__l="__l" u-p="{{m}}"></uv-icon><text class="add-text data-v-9d31c241">添加新标签</text></view></view><view class="form-item data-v-9d31c241"><view class="form-label data-v-9d31c241"><text class="label-text data-v-9d31c241">圈子动态</text><text class="required data-v-9d31c241">*</text></view><view class="textarea-wrapper data-v-9d31c241"><block wx:if="{{r0}}"><textarea class="form-textarea data-v-9d31c241" placeholder="填写圈子动态内容" maxlength="100" show-confirm-bar="{{false}}" value="{{o}}" bindinput="{{p}}"></textarea></block><view class="char-count data-v-9d31c241"><text class="count-text data-v-9d31c241">{{q}}/100</text><text class="tip-text data-v-9d31c241">不高于100字</text></view></view></view><view class="media-section data-v-9d31c241"><view class="media-btns data-v-9d31c241"><view class="media-btn data-v-9d31c241" bindtap="{{s}}"><uv-icon wx:if="{{r}}" class="data-v-9d31c241" u-i="9d31c241-3" bind:__l="__l" u-p="{{r}}"></uv-icon><text class="media-text data-v-9d31c241">添加图片</text></view><view class="media-btn data-v-9d31c241" bindtap="{{v}}"><uv-icon wx:if="{{t}}" class="data-v-9d31c241" u-i="9d31c241-4" bind:__l="__l" u-p="{{t}}"></uv-icon><text class="media-text data-v-9d31c241">添加视频</text></view></view><view wx:if="{{w}}" class="image-list data-v-9d31c241"><view wx:for="{{x}}" wx:for-item="img" wx:key="d" class="image-item data-v-9d31c241"><image src="{{img.a}}" mode="aspectFill" class="preview-image data-v-9d31c241"></image><view class="delete-btn data-v-9d31c241" bindtap="{{img.c}}"><uv-icon wx:if="{{y}}" class="data-v-9d31c241" u-i="{{img.b}}" bind:__l="__l" u-p="{{y}}"></uv-icon></view></view></view></view><view class="privacy-section data-v-9d31c241"><text class="privacy-text data-v-9d31c241">公开范围：所有人可见</text></view></view></scroll-view><view class="publish-footer data-v-9d31c241"><view class="publish-btn data-v-9d31c241" bindtap="{{z}}"><text class="publish-text data-v-9d31c241">确认发布</text></view></view><uv-picker wx:if="{{C}}" class="r data-v-9d31c241" u-r="circlePickerRef" bindconfirm="{{B}}" u-i="9d31c241-6" bind:__l="__l" u-p="{{C}}"></uv-picker><uv-picker wx:if="{{F}}" class="r data-v-9d31c241" u-r="topicPickerRef" bindconfirm="{{E}}" u-i="9d31c241-7" bind:__l="__l" u-p="{{F}}"></uv-picker></view>