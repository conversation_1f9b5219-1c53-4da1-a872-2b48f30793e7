"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "circle-detail",
  setup(__props) {
    const activeTab = common_vendor.ref("dynamic");
    const currentCommunityName = common_vendor.ref("邛崃社区");
    const postList = common_vendor.ref([
      {
        id: 1,
        avatar: "https://picsum.photos/200/200?random=10",
        userName: "张鸿飞",
        community: "邛崃社区",
        content: "7号4号山间露营游泳大队!出发，要去私信我哦留言，一起郊游村区美好风光",
        images: [
          "https://picsum.photos/200/200?random=1",
          "https://picsum.photos/200/200?random=2",
          "https://picsum.photos/200/200?random=3"
        ],
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "骑行漫约", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        commentCount: 8,
        isLiked: false
      },
      {
        id: 2,
        avatar: "https://picsum.photos/200/200?random=11",
        userName: "叶国庆",
        community: "邛崃社区",
        content: "7号4号山间露营游泳大队!出发，要去私信我哦留言，一起郊游村区美好风光",
        images: [
          "https://picsum.photos/200/200?random=4",
          "https://picsum.photos/200/200?random=5",
          "https://picsum.photos/200/200?random=6"
        ],
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "骑行漫约", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        commentCount: 8,
        isLiked: true
      },
      {
        id: 3,
        avatar: "https://picsum.photos/200/200?random=12",
        userName: "李楠",
        community: "邛崃社区",
        content: "7号4号山间露营游泳大队!出发，要去私信我哦留言，一起郊游村区美好风光",
        images: [
          "https://picsum.photos/200/200?random=7",
          "https://picsum.photos/200/200?random=8",
          "https://picsum.photos/200/200?random=9"
        ],
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "骑行漫约", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        commentCount: 8,
        isLiked: false
      }
    ]);
    common_vendor.onLoad((options) => {
      console.log("圈子详情页面加载", options);
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data && data.name) {
          currentCommunityName.value = data.name;
          console.log("切换社区到:", data.name);
        }
      });
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    const switchTab = (tab) => {
      activeTab.value = tab;
    };
    const toggleLike = (item) => {
      item.isLiked = !item.isLiked;
      if (item.isLiked) {
        item.likeCount++;
      } else {
        item.likeCount--;
      }
    };
    const goToComment = (item) => {
      console.log("跳转到评论", item);
    };
    const goToDetail = (item) => {
      console.log("跳转到详情", item);
    };
    const goToPublish = () => {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/community-circle/publish"
      });
    };
    const switchCommunity = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/area/area?scene=auth"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: activeTab.value === "dynamic" ? 1 : "",
        b: common_vendor.o(($event) => switchTab("dynamic")),
        c: activeTab.value === "my" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("my")),
        e: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        f: common_vendor.t(currentCommunityName.value || "切换社区"),
        g: common_vendor.o(switchCommunity),
        h: common_vendor.f(postList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.avatar,
            b: common_vendor.t(item.userName),
            c: "7af32040-1-" + i0,
            d: common_vendor.t(item.community),
            e: "7af32040-2-" + i0,
            f: common_vendor.t(item.content),
            g: item.images && item.images.length > 0
          }, item.images && item.images.length > 0 ? {
            h: common_vendor.f(item.images, (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img
              };
            })
          } : {}, {
            i: item.tags && item.tags.length > 0
          }, item.tags && item.tags.length > 0 ? {
            j: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
              return {
                a: tag.color,
                b: common_vendor.t(tag.name),
                c: tag.color,
                d: tagIndex
              };
            })
          } : {}, {
            k: common_vendor.t(item.publishTime),
            l: "7af32040-3-" + i0,
            m: common_vendor.p({
              name: item.isLiked ? "star-fill" : "star",
              color: item.isLiked ? "#ff4757" : "#999",
              size: "20"
            }),
            n: common_vendor.t(item.likeCount),
            o: common_vendor.o(($event) => toggleLike(item), index),
            p: "7af32040-4-" + i0,
            q: common_vendor.t(item.commentCount),
            r: common_vendor.o(($event) => goToComment(item), index),
            s: index,
            t: common_vendor.o(($event) => goToDetail(item), index)
          });
        }),
        i: common_vendor.p({
          name: "map-fill",
          color: "#999",
          size: "12"
        }),
        j: common_vendor.p({
          name: "chat",
          color: "#999",
          size: "20"
        }),
        k: common_vendor.p({
          name: "chat",
          color: "#999",
          size: "20"
        }),
        l: common_vendor.o(goToPublish)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7af32040"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/community-circle/circle-detail.vue"]]);
wx.createPage(MiniProgramPage);
