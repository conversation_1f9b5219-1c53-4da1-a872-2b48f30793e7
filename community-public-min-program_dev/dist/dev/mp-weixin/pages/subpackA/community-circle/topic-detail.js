"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "topic-detail",
  setup(__props) {
    const activeTab = common_vendor.ref("dynamic");
    const currentCommunityName = common_vendor.ref("邛崃社区");
    const topicInfo = common_vendor.ref({
      id: "",
      name: "奇舞团舞蹈队日常",
      postCount: 1005
    });
    const contentList = common_vendor.ref([
      {
        id: 1,
        userName: "冯娜",
        avatar: "/static/circle/avatar.png",
        community: "邛崃社区",
        isOfficial: false,
        content: "7894日奇舞团舞蹈队在享受之中，遇陌大家一起一起参与接下来的比赛",
        image: "https://picsum.photos/400/300?random=1",
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "奇舞团舞蹈队日常", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        isLiked: false
      },
      {
        id: 2,
        userName: "李飞",
        avatar: "/static/circle/avatar.png",
        community: "邛崃社区",
        isOfficial: false,
        content: "7894日奇舞团舞蹈队在享受之中，遇陌大家一起一起参与接下来的比赛",
        image: "https://picsum.photos/400/300?random=2",
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "奇舞团舞蹈队日常", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        isLiked: false
      },
      {
        id: 3,
        userName: "经典飞扬",
        avatar: "/static/circle/avatar.png",
        community: "邛崃社区",
        isOfficial: false,
        content: "7894日奇舞团舞蹈队在享受之中，遇陌大家一起一起参与接下来的比赛",
        image: "https://picsum.photos/400/300?random=3",
        tags: [
          { name: "环保", color: "#00C4DF" },
          { name: "奇舞团舞蹈队日常", color: "#FF519A" }
        ],
        publishTime: "2025-07-08",
        likeCount: 115,
        isLiked: false
      }
    ]);
    common_vendor.onLoad((options) => {
      if (options.id) {
        topicInfo.value.id = options.id;
      }
      if (options.name) {
        topicInfo.value.name = decodeURIComponent(options.name);
      }
      if (options.color) {
        console.log("话题颜色:", decodeURIComponent(options.color));
      }
      updateContentByTopic(topicInfo.value.name);
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data && data.name) {
          currentCommunityName.value = data.name;
          console.log("切换社区到:", data.name);
        }
      });
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    const updateContentByTopic = (topicName) => {
      const topicTags = {
        "邛崃": [
          { name: "邛崃", color: "#00C4DF" },
          { name: "奇舞团舞蹈队日常", color: "#FF519A" }
        ],
        "骑行邀约": [
          { name: "骑行邀约", color: "#FF519A" },
          { name: "户外运动", color: "#9A5DFF" }
        ],
        "社区骑游": [
          { name: "社区骑游", color: "#9A5DFF" },
          { name: "骑行", color: "#38A86B" }
        ],
        "登山组团": [
          { name: "登山组团", color: "#38A86B" },
          { name: "户外运动", color: "#FE9A24" }
        ],
        "奇舞团舞蹈队日常": [
          { name: "奇舞团舞蹈队日常", color: "#1990FF" },
          { name: "舞蹈", color: "#FF519A" }
        ]
      };
      contentList.value.forEach((item) => {
        item.tags = topicTags[topicName] || [
          { name: topicName, color: "#00C4DF" },
          { name: "社区生活", color: "#FF519A" }
        ];
      });
    };
    const switchTab = (tab) => {
      activeTab.value = tab;
    };
    const toggleLike = (item) => {
      item.isLiked = !item.isLiked;
      if (item.isLiked) {
        item.likeCount++;
      } else {
        item.likeCount--;
      }
    };
    const goToContentDetail = (content) => {
      console.log("跳转到内容详情:", content);
    };
    const goToPublish = () => {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/community-circle/publish"
      });
    };
    const switchCommunity = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/area/area?scene=auth"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(topicInfo.value.name),
        b: common_vendor.t(topicInfo.value.postCount),
        c: activeTab.value === "dynamic" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("dynamic")),
        e: activeTab.value === "favorite" ? 1 : "",
        f: common_vendor.o(($event) => switchTab("favorite")),
        g: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        h: common_vendor.t(currentCommunityName.value || "切换社区"),
        i: common_vendor.o(switchCommunity),
        j: activeTab.value === "dynamic"
      }, activeTab.value === "dynamic" ? {
        k: common_vendor.f(contentList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.avatar,
            b: common_vendor.t(item.userName),
            c: "7f2ed169-1-" + i0,
            d: common_vendor.t(item.community),
            e: "7f2ed169-2-" + i0,
            f: common_vendor.t(item.content),
            g: item.image
          }, item.image ? {
            h: item.image
          } : {}, {
            i: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
              return {
                a: tag.color,
                b: common_vendor.t(tag.name),
                c: tag.color,
                d: tagIndex
              };
            }),
            j: common_vendor.t(item.publishTime),
            k: "7f2ed169-3-" + i0,
            l: common_vendor.p({
              name: item.isLiked ? "star-fill" : "star",
              color: item.isLiked ? "#ff4757" : "#999",
              size: "20"
            }),
            m: common_vendor.t(item.likeCount),
            n: common_vendor.o(($event) => toggleLike(item), index),
            o: index,
            p: common_vendor.o(($event) => goToContentDetail(item), index)
          });
        }),
        l: common_vendor.p({
          name: "map-fill"
        }),
        m: common_vendor.p({
          name: "chat"
        })
      } : {}, {
        n: common_vendor.p({
          name: "plus",
          color: "#fff",
          size: "20"
        }),
        o: common_vendor.o(goToPublish)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7f2ed169"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/community-circle/topic-detail.vue"]]);
wx.createPage(MiniProgramPage);
