/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.topics-page.data-v-c6747c86 {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.content.data-v-c6747c86 {
  flex: 1;
  box-sizing: border-box;
}
.banner.data-v-c6747c86 {
  position: relative;
  height: 240px;
  padding: 32rpx;
  overflow: hidden;
  background: #FBA62D;
}
.banner .banner-bg.data-v-c6747c86 {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}
.banner .banner-content.data-v-c6747c86 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.banner .banner-content .banner-title.data-v-c6747c86 {
  display: block;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
}
.banner .banner-content .banner-subtitle.data-v-c6747c86 {
  font-size: 16px;
  color: #fff;
  opacity: 0.9;
}
.search-section.data-v-c6747c86 {
  padding: 30rpx 32rpx;
  margin-bottom: 16px;
  background: #fff;
}
.search-section .search-container.data-v-c6747c86 {
  display: flex;
  align-items: center;
  gap: 12px;
}
.search-section .search-container .search-box.data-v-c6747c86 {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
}
.search-section .search-container .search-box .search-input.data-v-c6747c86 {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}
.search-section .search-container .switch-community.data-v-c6747c86 {
  display: flex;
  align-items: center;
}
.search-section .search-container .switch-community .switch-icon.data-v-c6747c86 {
  width: 40rpx;
  height: 40rpx;
}
.search-section .search-container .switch-community .switch-text.data-v-c6747c86 {
  font-size: 14px;
  color: #FF9F18;
}
.tab-section.data-v-c6747c86 {
  background: #fff;
  border-bottom: 1px solid #eee;
}
.tab-section .tab-container.data-v-c6747c86 {
  display: flex;
  padding: 0 32rpx;
}
.tab-section .tab-container .tab-item.data-v-c6747c86 {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}
.tab-section .tab-container .tab-item .tab-text.data-v-c6747c86 {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}
.tab-section .tab-container .tab-item.active .tab-text.data-v-c6747c86 {
  color: #FBA62D;
}
.tab-section .tab-container .tab-item.active.data-v-c6747c86::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #FBA62D;
  border-radius: 3rpx;
}
.topics-section.data-v-c6747c86 {
  background: #fff;
  margin-top: 20rpx;
  padding: 32rpx;
}
.topics-section .topics-list .topic-item.data-v-c6747c86 {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
}
.topics-section .topics-list .topic-item.data-v-c6747c86:last-child {
  border-bottom: none;
}
.topics-section .topics-list .topic-item .topic-number.data-v-c6747c86 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.topics-section .topics-list .topic-item .topic-number .number-text.data-v-c6747c86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
}
.topics-section .topics-list .topic-item .topic-icon.data-v-c6747c86 {
  width: 30rpx;
  height: 30rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.topics-section .topics-list .topic-item .topic-icon .topic-icon-text.data-v-c6747c86 {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.topics-section .topics-list .topic-item .topic-info.data-v-c6747c86 {
  flex: 1;
}
.topics-section .topics-list .topic-item .topic-info .topic-name.data-v-c6747c86 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.topics-section .empty-state.data-v-c6747c86 {
  text-align: center;
  padding: 100rpx 0;
}
.topics-section .empty-state .empty-text.data-v-c6747c86 {
  font-size: 28rpx;
  color: #999;
}
.safe-bottom.data-v-c6747c86 {
  height: 34px;
}