"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "topics",
  setup(__props) {
    const activeTab = common_vendor.ref("hot");
    const currentCommunityName = common_vendor.ref("邛崃社区");
    const hotTopicsList = common_vendor.ref([
      {
        id: 1,
        name: "邛崃",
        icon: "#",
        color: "#00C4DF"
      },
      {
        id: 2,
        name: "骑行邀约",
        icon: "#",
        color: "#FF519A"
      },
      {
        id: 3,
        name: "社区骑游",
        icon: "#",
        color: "#9A5DFF"
      },
      {
        id: 4,
        name: "登山组团",
        icon: "#",
        color: "#38A86B"
      },
      {
        id: 5,
        name: "奇舞团舞蹈队日常",
        icon: "#",
        color: "#1990FF"
      }
    ]);
    common_vendor.onLoad(() => {
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data && data.name) {
          currentCommunityName.value = data.name;
          console.log("切换社区到:", data.name);
        }
      });
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    const switchTab = (tab) => {
      activeTab.value = tab;
    };
    const goToTopicDetail = (topic) => {
      console.log("跳转到话题详情:", topic);
      common_vendor.index.navigateTo({
        url: `/pages/subpackA/community-circle/topic-detail?id=${topic.id}&name=${encodeURIComponent(topic.name)}&color=${encodeURIComponent(topic.color)}`
      });
    };
    const switchCommunity = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/area/area?scene=auth"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: activeTab.value === "hot" ? 1 : "",
        b: common_vendor.o(($event) => switchTab("hot")),
        c: activeTab.value === "favorite" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("favorite")),
        e: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        f: common_vendor.t(currentCommunityName.value || "切换社区"),
        g: common_vendor.o(switchCommunity),
        h: activeTab.value === "hot"
      }, activeTab.value === "hot" ? {
        i: common_vendor.f(hotTopicsList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: item.color,
            c: common_vendor.t(item.icon),
            d: item.color,
            e: common_vendor.t(item.name),
            f: item.color,
            g: index,
            h: common_vendor.o(($event) => goToTopicDetail(item), index)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c6747c86"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/community-circle/topics.vue"]]);
wx.createPage(MiniProgramPage);
