/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.topic-detail.data-v-7f2ed169 {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.content.data-v-7f2ed169 {
  flex: 1;
  box-sizing: border-box;
}
.banner.data-v-7f2ed169 {
  background: #FBA62D;
  padding: 40rpx 32rpx;
}
.banner .banner-content .banner-title.data-v-7f2ed169 {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
}
.banner .banner-content .banner-subtitle.data-v-7f2ed169 {
  font-size: 28rpx;
  color: #fff;
  opacity: 0.9;
}
.tab-section.data-v-7f2ed169 {
  background: #fff;
  border-bottom: 1px solid #eee;
}
.tab-section .tab-container.data-v-7f2ed169 {
  display: flex;
  padding: 0 32rpx;
}
.tab-section .tab-container .tab-item.data-v-7f2ed169 {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}
.tab-section .tab-container .tab-item .tab-text.data-v-7f2ed169 {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}
.tab-section .tab-container .tab-item.active .tab-text.data-v-7f2ed169 {
  color: #FBA62D;
}
.tab-section .tab-container .tab-item.active.data-v-7f2ed169::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #FBA62D;
  border-radius: 3rpx;
}
.search-section.data-v-7f2ed169 {
  padding: 30rpx 32rpx;
  background: #fff;
}
.search-section .search-container.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  gap: 12px;
}
.search-section .search-container .search-box.data-v-7f2ed169 {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
}
.search-section .search-container .search-box .search-input.data-v-7f2ed169 {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}
.search-section .search-container .switch-community.data-v-7f2ed169 {
  display: flex;
  align-items: center;
}
.search-section .search-container .switch-community .switch-icon.data-v-7f2ed169 {
  width: 40rpx;
  height: 40rpx;
}
.search-section .search-container .switch-community .switch-text.data-v-7f2ed169 {
  font-size: 14px;
  color: #FF9F18;
}
.content-section.data-v-7f2ed169 {
  margin-top: 20rpx;
}
.content-section .content-list .content-item.data-v-7f2ed169 {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 32rpx;
}
.content-section .content-list .content-item .user-info.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.content-section .content-list .content-item .user-info .user-avatar-wrapper.data-v-7f2ed169 {
  margin-right: 24rpx;
}
.content-section .content-list .content-item .user-info .user-avatar-wrapper .user-avatar.data-v-7f2ed169 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.content-section .content-list .content-item .user-info .user-details.data-v-7f2ed169 {
  flex: 1;
}
.content-section .content-list .content-item .user-info .user-details .user-name-row.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.content-section .content-list .content-item .user-info .user-details .user-name-row .user-name.data-v-7f2ed169 {
  font-size: 30rpx;
  color: #333;
  margin-right: 16rpx;
}
.content-section .content-list .content-item .user-info .user-details .user-name-row .user-badge.data-v-7f2ed169 {
  width: 40rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.content-section .content-list .content-item .user-info .user-details .user-name-row .user-badge .badge-text.data-v-7f2ed169 {
  font-size: 20rpx;
  color: #fff;
  font-weight: bold;
}
.content-section .content-list .content-item .user-info .user-details .user-community.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.content-section .content-list .content-item .user-info .user-details .user-community .uv-icon.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #999;
}
.content-section .content-list .content-item .content-text.data-v-7f2ed169 {
  margin-bottom: 24rpx;
}
.content-section .content-list .content-item .content-text text.data-v-7f2ed169 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
.content-section .content-list .content-item .content-image-wrapper.data-v-7f2ed169 {
  margin-bottom: 24rpx;
}
.content-section .content-list .content-item .content-image-wrapper .content-image.data-v-7f2ed169 {
  width: 230rpx;
  height: 230rpx;
  border-radius: 12rpx;
}
.content-section .content-list .content-item .content-tags.data-v-7f2ed169 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.content-section .content-list .content-item .content-tags .tag.data-v-7f2ed169 {
  padding: 8rpx 0;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
}
.content-section .content-list .content-item .content-tags .tag .tag-g.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #fff;
  width: 30rpx;
  height: 30rpx;
  text-align: center;
  line-height: 30rpx;
  border-radius: 10rpx;
  display: block;
  margin-right: 8rpx;
}
.content-section .content-list .content-item .content-tags .tag .tag-text.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #fff;
}
.content-section .content-list .content-item .publish-time.data-v-7f2ed169 {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content-section .content-list .content-item .publish-time .time-text.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #999;
}
.content-section .content-list .content-item .interaction-area.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content-section .content-list .content-item .interaction-area .interaction-item.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.content-section .content-list .content-item .interaction-area .interaction-item .interaction-count.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #999;
}
.content-section .content-list .content-item .interaction-area .interaction-item .interaction-text.data-v-7f2ed169 {
  font-size: 24rpx;
  color: #999;
}
.content-section .empty-state.data-v-7f2ed169 {
  text-align: center;
  padding: 100rpx 0;
  background: #fff;
}
.content-section .empty-state .empty-text.data-v-7f2ed169 {
  font-size: 28rpx;
  color: #999;
}
.publish-btn-container.data-v-7f2ed169 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: #fff;
  border-top: 1px solid #eee;
}
.publish-btn-container .publish-btn.data-v-7f2ed169 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  background: #FBA62D;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
}
.publish-btn-container .publish-btn .publish-text.data-v-7f2ed169 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.safe-bottom.data-v-7f2ed169 {
  height: 34px;
}