<view class="circle-detail data-v-7af32040"><view class="circle-info data-v-7af32040"><image src="/static/circle/trumpet.png" mode="aspectFill" class="circle-avatar data-v-7af32040"></image><view class="circle-details data-v-7af32040"><text class="circle-name data-v-7af32040">圈子名称：社区喇叭</text><text class="circle-stats data-v-7af32040">105 查看 | 128圈友</text></view><view class="join-btn data-v-7af32040"><text class="join-text-in data-v-7af32040">进圈</text><text class="join-text-tips data-v-7af32040">圈子公告</text></view></view><view class="nav-tabs data-v-7af32040"><view class="{{['tab-item', 'data-v-7af32040', a && 'active']}}" bindtap="{{b}}"><text class="tab-text data-v-7af32040">圈子动态</text></view><view class="{{['tab-item', 'data-v-7af32040', c && 'active']}}" bindtap="{{d}}"><text class="tab-text data-v-7af32040">我收藏的</text></view></view><view class="search-section data-v-7af32040"><view class="search-box data-v-7af32040"><uv-icon wx:if="{{e}}" class="data-v-7af32040" u-i="7af32040-0" bind:__l="__l" u-p="{{e}}"></uv-icon><input class="search-input data-v-7af32040" placeholder="搜索圈子或话题"/></view><view class="switch-community data-v-7af32040" bindtap="{{g}}"><image src="/static/circle/switch.png" mode="" class="switch-icon data-v-7af32040"></image><text class="switch-text data-v-7af32040">{{f}}</text></view></view><scroll-view class="content data-v-7af32040" scroll-y="true"><view class="post-list data-v-7af32040"><view wx:for="{{h}}" wx:for-item="item" wx:key="s" class="post-item data-v-7af32040" bindtap="{{item.t}}"><view class="post-header data-v-7af32040"><image src="{{item.a}}" mode="aspectFill" class="user-avatar data-v-7af32040"></image><view class="user-info data-v-7af32040"><view class="user-name-row data-v-7af32040"><text class="user-name data-v-7af32040">{{item.b}}</text></view><view class="user-community data-v-7af32040"><uv-icon wx:if="{{i}}" class="data-v-7af32040" u-i="{{item.c}}" bind:__l="__l" u-p="{{i}}"></uv-icon><text class="community-text data-v-7af32040">{{item.d}}</text></view></view><view class="message-btn data-v-7af32040"><uv-icon wx:if="{{j}}" class="data-v-7af32040" u-i="{{item.e}}" bind:__l="__l" u-p="{{j}}"></uv-icon></view></view><view class="post-content data-v-7af32040"><text class="content-text data-v-7af32040">{{item.f}}</text><view wx:if="{{item.g}}" class="content-images data-v-7af32040"><image wx:for="{{item.h}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="" class="content-image data-v-7af32040"></image></view></view><view wx:if="{{item.i}}" class="post-tags data-v-7af32040"><view wx:for="{{item.j}}" wx:for-item="tag" wx:key="d" class="tag data-v-7af32040"><text class="tag-hash data-v-7af32040" style="{{'background-color:' + tag.a}}">#</text><text class="tag-text data-v-7af32040" style="{{'color:' + tag.c}}">{{tag.b}}</text></view></view><view class="post-bottom data-v-7af32040"><text class="publish-time data-v-7af32040">{{item.k}}</text><view class="interaction-area data-v-7af32040"><view class="interaction-item data-v-7af32040" catchtap="{{item.o}}"><uv-icon wx:if="{{item.m}}" class="data-v-7af32040" u-i="{{item.l}}" bind:__l="__l" u-p="{{item.m}}"></uv-icon><text class="interaction-count data-v-7af32040">{{item.n}}</text></view><view class="interaction-item data-v-7af32040" catchtap="{{item.r}}"><uv-icon wx:if="{{k}}" class="data-v-7af32040" u-i="{{item.p}}" bind:__l="__l" u-p="{{k}}"></uv-icon><text class="interaction-count data-v-7af32040">{{item.q}}</text></view></view></view></view></view><view class="publish-btn data-v-7af32040" bindtap="{{l}}"><text class="publish-text data-v-7af32040">+ 发布圈子动态</text></view><view class="safe-bottom data-v-7af32040"></view></scroll-view></view>