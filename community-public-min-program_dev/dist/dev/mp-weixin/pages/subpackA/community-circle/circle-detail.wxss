/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.switch-icon.data-v-7af32040 {
  width: 30rpx;
  height: 30rpx;
}
.circle-detail.data-v-7af32040 {
  background-color: #f8f9fa;
  height: 100vh;
}
.circle-detail .circle-info.data-v-7af32040 {
  background-color: #FBA62D;
  padding: 32rpx 32rpx 52rpx 32rpx;
  display: flex;
  align-items: center;
}
.circle-detail .circle-info .circle-avatar.data-v-7af32040 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
}
.circle-detail .circle-info .circle-details.data-v-7af32040 {
  flex: 1;
}
.circle-detail .circle-info .circle-details text.data-v-7af32040 {
  display: block;
}
.circle-detail .circle-info .circle-details .circle-name.data-v-7af32040 {
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 8rpx;
}
.circle-detail .circle-info .circle-details .circle-stats.data-v-7af32040 {
  font-size: 26rpx;
  color: #fff;
  opacity: 0.9;
}
.circle-detail .circle-info .join-btn.data-v-7af32040 {
  border-radius: 36rpx;
  padding: 16rpx 32rpx;
}
.circle-detail .circle-info .join-btn text.data-v-7af32040 {
  display: block;
}
.circle-detail .circle-info .join-btn .join-text-in.data-v-7af32040 {
  font-size: 28rpx;
  color: #fff;
  border: 2rpx solid #fff;
  font-weight: 500;
  padding: 5rpx 20rpx;
  border-radius: 50rpx;
}
.circle-detail .circle-info .join-btn .join-text-tips.data-v-7af32040 {
  font-size: 24rpx;
  color: #fff;
  margin-top: 10rpx;
  text-decoration: underline;
}
.circle-detail .nav-tabs.data-v-7af32040 {
  background-color: #fff;
  display: flex;
  padding: 0 32rpx;
  margin-top: -32rpx;
  border-radius: 32rpx 32rpx 0 0;
}
.circle-detail .nav-tabs .tab-item.data-v-7af32040 {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}
.circle-detail .nav-tabs .tab-item.active .tab-text.data-v-7af32040 {
  color: #ff9500;
}
.circle-detail .nav-tabs .tab-item.active.data-v-7af32040::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff9500;
  border-radius: 2rpx;
}
.circle-detail .nav-tabs .tab-item .tab-text.data-v-7af32040 {
  font-size: 28rpx;
  color: #333;
}
.circle-detail .search-section.data-v-7af32040 {
  background-color: #fff;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.circle-detail .search-section .search-box.data-v-7af32040 {
  flex: 1;
  background-color: #f8f9fa;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.circle-detail .search-section .search-box .search-input.data-v-7af32040 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.circle-detail .search-section .search-box .search-input.data-v-7af32040::-moz-placeholder {
  color: #999;
}
.circle-detail .search-section .search-box .search-input.data-v-7af32040::placeholder {
  color: #999;
}
.circle-detail .search-section .switch-community.data-v-7af32040 {
  display: flex;
  align-items: center;
}
.circle-detail .search-section .switch-community .switch-text.data-v-7af32040 {
  font-size: 28rpx;
  color: #ff9500;
}
.circle-detail .content.data-v-7af32040 {
  flex: 1;
  background-color: #f8f9fa;
}
.circle-detail .content .post-list.data-v-7af32040 {
  padding: 24rpx 32rpx;
}
.circle-detail .content .post-list .post-item.data-v-7af32040 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.circle-detail .content .post-list .post-item .post-header.data-v-7af32040 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.circle-detail .content .post-list .post-item .post-header .user-avatar.data-v-7af32040 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 16rpx;
}
.circle-detail .content .post-list .post-item .post-header .user-info.data-v-7af32040 {
  flex: 1;
}
.circle-detail .content .post-list .post-item .post-header .user-info .user-name-row.data-v-7af32040 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.circle-detail .content .post-list .post-item .post-header .user-info .user-name-row .user-name.data-v-7af32040 {
  font-size: 30rpx;
  color: #333;
}
.circle-detail .content .post-list .post-item .post-header .user-info .user-community.data-v-7af32040 {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.circle-detail .content .post-list .post-item .post-header .user-info .user-community .community-text.data-v-7af32040 {
  font-size: 24rpx;
  color: #999;
}
.circle-detail .content .post-list .post-item .post-header .message-btn.data-v-7af32040 {
  padding: 8rpx;
}
.circle-detail .content .post-list .post-item .post-content.data-v-7af32040 {
  margin-bottom: 24rpx;
}
.circle-detail .content .post-list .post-item .post-content .content-text.data-v-7af32040 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.circle-detail .content .post-list .post-item .post-content .content-images.data-v-7af32040 {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin-top: 10rpx;
}
.circle-detail .content .post-list .post-item .post-content .content-images .content-image.data-v-7af32040 {
  width: 220rpx;
  height: 200rpx;
  display: block;
  border-radius: 12rpx;
}
.circle-detail .content .post-list .post-item .post-tags.data-v-7af32040 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.circle-detail .content .post-list .post-item .post-tags .tag.data-v-7af32040 {
  display: flex;
  align-items: center;
}
.circle-detail .content .post-list .post-item .post-tags .tag .tag-hash.data-v-7af32040 {
  font-size: 24rpx;
  color: #fff;
  width: 30rpx;
  height: 30rpx;
  text-align: center;
  line-height: 30rpx;
  border-radius: 10rpx;
  margin-right: 8rpx;
}
.circle-detail .content .post-list .post-item .post-tags .tag .tag-text.data-v-7af32040 {
  font-size: 24rpx;
}
.circle-detail .content .post-list .post-item .post-bottom.data-v-7af32040 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.circle-detail .content .post-list .post-item .post-bottom .publish-time.data-v-7af32040 {
  font-size: 24rpx;
  color: #999;
}
.circle-detail .content .post-list .post-item .post-bottom .interaction-area.data-v-7af32040 {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.circle-detail .content .post-list .post-item .post-bottom .interaction-area .interaction-item.data-v-7af32040 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.circle-detail .content .post-list .post-item .post-bottom .interaction-area .interaction-item .interaction-count.data-v-7af32040 {
  font-size: 24rpx;
  color: #999;
}
.circle-detail .content .publish-btn.data-v-7af32040 {
  background-color: #ff9500;
  border-radius: 32rpx;
  padding: 24rpx;
  margin: 24rpx 32rpx;
  text-align: center;
}
.circle-detail .content .publish-btn .publish-text.data-v-7af32040 {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
.circle-detail .safe-bottom.data-v-7af32040 {
  height: 60rpx;
}