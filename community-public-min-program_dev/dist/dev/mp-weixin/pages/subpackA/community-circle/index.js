"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(0);
    const currentCommunityName = common_vendor.ref("邛崃社区");
    const hotCircles = common_vendor.ref([
      {
        id: 1,
        name: "社区骑游",
        avatar: "/static/circle/icon1.png",
        memberCount: 95,
        postCount: 95
      },
      {
        id: 2,
        name: "社区舞蹈",
        avatar: "/static/circle/icon2.png",
        memberCount: 95,
        postCount: 95
      },
      {
        id: 3,
        name: "社区读书",
        avatar: "/static/circle/icon3.png",
        memberCount: 95,
        postCount: 95
      },
      {
        id: 4,
        name: "社区瑜伽",
        avatar: "/static/circle/icon4.png",
        memberCount: 95,
        postCount: 95
      }
    ]);
    const hotTopics = common_vendor.ref([
      {
        id: 1,
        name: "邛崃",
        icon: "#",
        color: "#00C4DF"
      },
      {
        id: 2,
        name: "骑行邀约",
        icon: "#",
        color: "#CD00DF"
      },
      {
        id: 3,
        name: "钓鱼组团",
        icon: "#",
        color: "#FE9A24"
      },
      {
        id: 4,
        name: "奇舞团舞蹈队日常",
        icon: "#",
        color: "#FF519A"
      },
      {
        id: 5,
        name: "滑板训练",
        icon: "#",
        color: "#9351FF"
      },
      {
        id: 6,
        name: "登山组团",
        icon: "#",
        color: "#38A86B"
      }
    ]);
    const browseCircles = common_vendor.ref([
      {
        id: 1,
        image: "https://picsum.photos/200/200?random=1",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 2,
        image: "https://picsum.photos/200/200?random=2",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 3,
        image: "https://picsum.photos/200/200?random=3",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      }
    ]);
    const fishingContent = common_vendor.ref([
      {
        id: 1,
        image: "https://picsum.photos/200/200?random=4",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 2,
        image: "https://picsum.photos/200/200?random=5",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 3,
        image: "https://picsum.photos/200/200?random=6",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      }
    ]);
    const danceContent = common_vendor.ref([
      {
        id: 1,
        image: "https://picsum.photos/200/200?random=7",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 2,
        image: "https://picsum.photos/200/200?random=8",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      },
      {
        id: 3,
        image: "https://picsum.photos/200/200?random=9",
        avatar: "/static/circle/avatar.png",
        userName: "用户6891502"
      }
    ]);
    common_vendor.onLoad(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 0;
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data && data.name) {
          currentCommunityName.value = data.name;
          console.log("切换社区到:", data.name);
        }
      });
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    const goToCircle = (circle) => {
      console.log("跳转到圈子详情:", circle);
    };
    const goToTopic = (topic) => {
      console.log("跳转到话题详情:", topic);
      common_vendor.index.navigateTo({
        url: `/pages/subpackA/community-circle/topic-detail?id=${topic.id}&name=${encodeURIComponent(topic.name)}&color=${encodeURIComponent(topic.color)}`
      });
    };
    const goToMoreTopics = () => {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/community-circle/topics"
      });
    };
    const goToMoreCircles = () => {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/community-circle/circle-detail"
      });
    };
    const switchCommunity = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/area/area?scene=auth"
      });
    };
    const goToContent = (content) => {
      console.log("跳转到内容详情:", content);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "search",
          color: "#999",
          size: "16"
        }),
        b: common_vendor.t(currentCommunityName.value || "切换社区"),
        c: common_vendor.o(switchCommunity),
        d: common_vendor.p({
          name: "arrow-right",
          color: "#999",
          size: "12"
        }),
        e: common_vendor.o(goToMoreCircles),
        f: common_vendor.f(hotCircles.value, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.memberCount),
            d: common_vendor.t(item.postCount),
            e: index,
            f: common_vendor.o(($event) => goToCircle(item), index)
          };
        }),
        g: common_vendor.p({
          name: "arrow-right",
          color: "#999",
          size: "12"
        }),
        h: common_vendor.o(goToMoreTopics),
        i: common_vendor.f(hotTopics.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.icon),
            b: item.color,
            c: common_vendor.t(item.name),
            d: item.color,
            e: index,
            f: common_vendor.o(($event) => goToTopic(item), index)
          };
        }),
        j: common_vendor.f(browseCircles.value, (item, index, i0) => {
          return {
            a: item.image,
            b: item.avatar,
            c: common_vendor.t(item.userName),
            d: index,
            e: common_vendor.o(($event) => goToContent(item), index)
          };
        }),
        k: common_vendor.f(fishingContent.value, (item, index, i0) => {
          return {
            a: item.image,
            b: item.avatar,
            c: common_vendor.t(item.userName),
            d: index,
            e: common_vendor.o(($event) => goToContent(item), index)
          };
        }),
        l: common_vendor.f(danceContent.value, (item, index, i0) => {
          return {
            a: item.image,
            b: item.avatar,
            c: common_vendor.t(item.userName),
            d: index,
            e: common_vendor.o(($event) => goToContent(item), index)
          };
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b22812c5"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/community-circle/index.vue"]]);
wx.createPage(MiniProgramPage);
