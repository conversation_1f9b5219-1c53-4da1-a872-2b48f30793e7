"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_space = require("../../../common/api/space.js");
const common_utils_share = require("../../../common/utils/share.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/utils/common.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_parse2 = common_vendor.resolveComponent("uv-parse");
  (_easycom_uv_swiper2 + _easycom_uv_parse2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_parse = () => "../../../uni_modules/uv-parse/components/uv-parse/uv-parse.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_parse)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "details",
  setup(__props) {
    const list = common_vendor.ref([]);
    const id = common_vendor.ref("");
    const data = common_vendor.ref({});
    const { setShare } = common_utils_share.useShare();
    let share = {};
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    common_vendor.onLoad((options) => {
      id.value = decodeURIComponent(options.id);
      common_api_space.getDetails({
        id: id.value
      }).then((res) => {
        data.value = res.data;
        list.value.push(data.value.coverImages);
        share = {
          title: data.value.name,
          desc: "",
          imageUrl: data.value.coverImages
        };
        setShare({
          weapp: {
            ...share
          }
        });
      });
      common_vendor.onShow(() => {
        setShare({
          weapp: {
            ...share
          }
        });
      });
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          height: "500rpx",
          width: "100%",
          list: list.value,
          radius: "0"
        }),
        b: common_vendor.t(data.value.name),
        c: common_vendor.t(data.value.address),
        d: common_vendor.o(($event) => openLocation(data.value)),
        e: common_vendor.t(data.value.contactPhone),
        f: common_vendor.t(data.value.brief),
        g: common_vendor.p({
          content: data.value.details
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6a1a904d"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/space/details.vue"]]);
wx.createPage(MiniProgramPage);
