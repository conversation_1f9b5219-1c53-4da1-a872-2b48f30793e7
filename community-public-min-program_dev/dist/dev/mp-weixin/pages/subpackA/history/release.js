"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_api_history = require("../../../common/api/history.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_upload2 = common_vendor.resolveComponent("uv-upload");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_uv_icon2 + _easycom_uv_upload2 + _easycom_uv_textarea2 + _easycom_uv_button2 + _easycom_uv_form2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_upload = () => "../../../uni_modules/uv-upload/components/uv-upload/uv-upload.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_uv_icon + _easycom_uv_upload + _easycom_uv_textarea + _easycom_uv_button + _easycom_uv_form)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "release",
  setup(__props) {
    const rules = Object.freeze({
      title: {
        type: "string",
        required: true,
        message: "请输入动态标题",
        trigger: ["blur", "change"]
      },
      content: {
        type: "string",
        required: true,
        message: "请输入动态详情",
        trigger: ["blur", "change"]
      }
    });
    const fileList = common_vendor.ref([]), videoList = common_vendor.ref([]);
    let form = common_vendor.reactive({
      title: "",
      content: "",
      ossIds: null
    });
    const formRef = common_vendor.ref();
    const deletePic = (e) => {
      fileList.value.splice(e.index, 1);
    };
    const uploadImgOrVideo = () => {
      common_vendor.index.chooseMedia({
        count: 5,
        //默认9
        mediaType: ["mix"],
        sizeType: ["original", "compressed"],
        //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album"],
        //从相册选择
        success: function(res) {
          console.log(res);
          const listReq = [];
          res.tempFiles.map((item) => {
            listReq.push(uploadFile(item.tempFilePath));
          });
          Promise.all(listReq).then((result) => {
            let resFile = [];
            result.map((item) => {
              resFile.push({ url: item.url, message: item.ossId });
            });
            fileList.value = fileList.value.concat(resFile);
          });
        }
      });
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    const submit = async () => {
      form.ossIds = [];
      fileList.value.length && fileList.value.map((item) => {
        form.ossIds.push(item.message);
      });
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        const res = await common_api_history.pushActivity(form);
        if ((res == null ? void 0 : res.code) === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.setStorageSync("isBack", true);
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.hideLoading();
        }
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => common_vendor.unref(form).title = $event),
        b: common_vendor.p({
          placeholder: "请输入动态标题",
          modelValue: common_vendor.unref(form).title
        }),
        c: common_vendor.p({
          label: "动态标题",
          prop: "title",
          required: true
        }),
        d: fileList.value.length != 5
      }, fileList.value.length != 5 ? {
        e: common_vendor.p({
          name: "camera-fill",
          label: "附件",
          labelPos: "bottom",
          color: "#999999",
          size: "30"
        }),
        f: common_vendor.o(uploadImgOrVideo)
      } : {}, {
        g: fileList.value.length
      }, fileList.value.length ? {
        h: common_vendor.o(deletePic),
        i: common_vendor.p({
          fileList: fileList.value,
          name: "1",
          multiple: true,
          maxCount: 5,
          previewFullImage: true
        })
      } : {}, {
        j: common_vendor.t(fileList.value.length + videoList.value.length),
        k: common_vendor.p({
          label: "附件"
        }),
        l: common_vendor.o(($event) => common_vendor.unref(form).content = $event),
        m: common_vendor.p({
          maxlength: "100",
          showConfirmBar: false,
          placeholder: "请输入动态详情",
          modelValue: common_vendor.unref(form).content
        }),
        n: common_vendor.p({
          label: "动态详情",
          prop: "content",
          required: true
        }),
        o: common_vendor.o(submit),
        p: common_vendor.p({
          type: "warning",
          text: "提交留言",
          customStyle: "margin-top: 190px"
        }),
        q: common_vendor.sr(formRef, "6743cf35-0", {
          "k": "formRef"
        }),
        r: common_vendor.p({
          labelPosition: "left",
          model: common_vendor.unref(form),
          rules: common_vendor.unref(rules)
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6743cf35"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/history/release.vue"]]);
wx.createPage(MiniProgramPage);
