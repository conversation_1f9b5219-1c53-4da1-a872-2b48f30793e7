"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_store = require("../../../common/api/store.js");
const common_api_market = require("../../../common/api/market.js");
const common_api_history = require("../../../common/api/history.js");
const common_api_assistance = require("../../../common/api/assistance.js");
const common_utils_share = require("../../../common/utils/share.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/utils/common.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_uv_parse2 = common_vendor.resolveComponent("uv-parse");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  (_easycom_uv_swiper2 + _easycom_uv_icon2 + _easycom_uv_text2 + _easycom_uv_parse2 + _easycom_a_audio2 + _easycom_uv_button2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_uv_parse = () => "../../../uni_modules/uv-parse/components/uv-parse/uv-parse.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_icon + _easycom_uv_text + _easycom_uv_parse + _easycom_a_audio + _easycom_uv_button)();
}
const _sfc_defineComponent = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "details",
  setup(__props) {
    const list = common_vendor.ref([]);
    const id = common_vendor.ref(""), type = common_vendor.ref(), scrollHeight = common_vendor.ref();
    const data = common_vendor.ref({});
    const { setShare } = common_utils_share.useShare();
    let share = {};
    common_vendor.onShareAppMessage(() => {
      var _a, _b;
      if ((_a = data.value) == null ? void 0 : _a.activityId) {
        common_api_market.shareDynamic(data.value.activityId);
        console.log("分享动态，ID:", data.value.activityId);
      }
      return {
        ...share,
        path: `/pages/subpackA/history/details?id=${((_b = data.value) == null ? void 0 : _b.activityId) || id.value}${type.value ? "&type=" + type.value : ""}`
      };
    });
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 60;
    };
    const handleStatus = () => {
      var _a, _b;
      if (type.value === "1" || type.value === "2") {
        return ((_a = data.value) == null ? void 0 : _a.applyStatus) !== "0";
      } else {
        return ((_b = data.value) == null ? void 0 : _b.reviewStatus) !== "pending";
      }
    };
    const handleTypeTitle = () => {
      switch (type.value) {
        case "1":
          return "好物详情";
        case "2":
          return "二手闲置详情";
        case "3":
          return "微服务详情";
        case "4":
          return "微心愿详情";
        default:
          return "动态详情";
      }
    };
    function openLocation(item) {
      if (type.value === "3" || type.value === "4") {
        item.latitude = JSON.parse(item.location).latitude;
        item.longitude = JSON.parse(item.location).longitude;
      }
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: (item == null ? void 0 : item.address) || (item == null ? void 0 : item.locationInfo),
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    const submit = async (status) => {
      const res = await common_api_history.reviewData({
        type: type.value,
        ids: [id.value],
        status
      });
      if ((res == null ? void 0 : res.code) === 200) {
        common_vendor.index.setStorageSync("isBack", true);
        common_vendor.index.navigateBack();
      } else {
        throw console.log(res);
      }
    };
    common_vendor.onLoad((options) => {
      getScreenHeight();
      id.value = decodeURIComponent(options.id);
      type.value = options.type;
      if (!(options == null ? void 0 : options.type)) {
        common_vendor.index.setNavigationBarTitle({
          title: "动态详情"
        });
      }
      switch (options.type) {
        case "1":
          common_api_store.getDetails({
            id: id.value
          }).then((res) => {
            data.value = res.data;
            list.value.push({ url: data.value.coverImages });
            share = {
              title: data.value.title || "社区好物",
              desc: data.value.brief || "",
              imageUrl: data.value.coverImages
            };
            setShare({
              weapp: {
                ...share
              }
            });
          });
          break;
        case "2":
          common_api_market.getMarketDetails({
            id: id.value
          }).then((res) => {
            data.value = res.data;
            console.log("data.value", data.value);
            list.value.push({ url: data.value.coverImages });
            share = {
              title: data.value.title || "二手闲置",
              desc: data.value.brief || "",
              imageUrl: data.value.coverImages
            };
            setShare({
              weapp: {
                ...share
              }
            });
          });
          break;
        case "3":
          common_api_assistance.getWishlistsInfo(id.value).then((res) => {
            var _a;
            data.value = res.data;
            list.value = data.value.imageOssVos;
            if (list.value.length === 0) {
              list.value.push({ url: "https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/2c3406a9754a4a75871fa17923f9f635.png" });
            }
            share = {
              title: data.value.title || "微服务",
              desc: data.value.brief || "",
              imageUrl: (_a = list.value[0]) == null ? void 0 : _a.url
            };
            setShare({
              weapp: {
                ...share
              }
            });
          });
          break;
        case "4":
          common_api_assistance.getWishlistsInfo(id.value).then((res) => {
            var _a;
            data.value = res.data;
            list.value = data.value.imageOssVos;
            if (list.value.length === 0) {
              list.value.push({ url: "https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/2c3406a9754a4a75871fa17923f9f635.png" });
            }
            share = {
              title: data.value.title || "微心愿",
              desc: data.value.brief || "",
              imageUrl: (_a = list.value[0]) == null ? void 0 : _a.url
            };
            setShare({
              weapp: {
                ...share
              }
            });
          });
          break;
        default:
          common_api_history.getDetailsById(options.id).then((res) => {
            var _a;
            data.value = res.data;
            list.value = data.value.imageOssVos;
            share = {
              title: data.value.title || "社区动态",
              desc: data.value.brief || "",
              imageUrl: (_a = list.value[0]) == null ? void 0 : _a.url
            };
            setShare({
              weapp: {
                ...share
              }
            });
          });
      }
    });
    common_vendor.onShow(() => {
      setShare({
        weapp: {
          ...share
        }
      });
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u;
      return common_vendor.e({
        a: common_vendor.p({
          height: "500rpx",
          width: "100%",
          keyName: "url",
          list: list.value
        }),
        b: common_vendor.t((_a = data.value) == null ? void 0 : _a.title),
        c: ((_b = data.value) == null ? void 0 : _b.address) || ((_c = data.value) == null ? void 0 : _c.locationInfo)
      }, ((_d = data.value) == null ? void 0 : _d.address) || ((_e = data.value) == null ? void 0 : _e.locationInfo) ? {
        d: common_vendor.p({
          name: "map-fill",
          color: "#999999",
          size: "16"
        }),
        e: common_vendor.t(((_f = data.value) == null ? void 0 : _f.address) || ((_g = data.value) == null ? void 0 : _g.locationInfo)),
        f: common_vendor.o(($event) => openLocation(data.value))
      } : {}, {
        g: (_h = data.value) == null ? void 0 : _h.contactPhone
      }, ((_i = data.value) == null ? void 0 : _i.contactPhone) ? {
        h: common_vendor.p({
          name: "account-fill",
          color: "#999999",
          size: "16"
        }),
        i: common_vendor.t((_j = data.value) == null ? void 0 : _j.contactPhone)
      } : {}, {
        j: (_k = data.value) == null ? void 0 : _k.price
      }, ((_l = data.value) == null ? void 0 : _l.price) ? {
        k: common_vendor.p({
          size: "14",
          color: "red",
          text: data.value.price
        })
      } : {}, {
        l: common_vendor.t((_m = data.value) == null ? void 0 : _m.brief),
        m: common_vendor.t(handleTypeTitle()),
        n: common_vendor.p({
          content: ((_n = data.value) == null ? void 0 : _n.details) || ((_o = data.value) == null ? void 0 : _o.content)
        }),
        o: (_p = data.value) == null ? void 0 : _p.voiceUrl
      }, ((_q = data.value) == null ? void 0 : _q.voiceUrl) ? {
        p: common_vendor.p({
          voiceUrl: (_r = data.value) == null ? void 0 : _r.voiceUrl
        })
      } : {}, {
        q: (_s = data.value) == null ? void 0 : _s.videoUrl
      }, ((_t = data.value) == null ? void 0 : _t.videoUrl) ? {
        r: (_u = data.value) == null ? void 0 : _u.videoUrl
      } : {}, {
        s: scrollHeight.value + "px",
        t: type.value
      }, type.value ? {
        v: common_vendor.o(($event) => submit(2)),
        w: common_vendor.p({
          color: "#FF3B30",
          text: "审核驳回",
          customStyle: "margin-top: 10px",
          disabled: handleStatus()
        }),
        x: common_vendor.o(($event) => submit(1)),
        y: common_vendor.p({
          color: "#04B578",
          text: "审核通过",
          customStyle: "margin-top: 10px",
          disabled: handleStatus()
        })
      } : {});
    };
  }
});
_sfc_defineComponent.__runtimeHooks = 2;
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_defineComponent, [["__scopeId", "data-v-eabe1ba6"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/history/details.vue"]]);
wx.createPage(MiniProgramPage);
