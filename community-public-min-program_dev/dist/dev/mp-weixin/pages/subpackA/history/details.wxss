/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #F5F5F5;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fixed.data-v-eabe1ba6 {
  position: fixed;
  width: 100%;
  bottom: 0;
  background: #fff;
  height: 60px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.icon.data-v-eabe1ba6 {
  height: 40rpx;
  width: 40rpx;
  min-width: 40rpx;
}
.title.data-v-eabe1ba6 {
  font-weight: bold;
  color: #222222;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.line.data-v-eabe1ba6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999999;
  font-size: 14px;
  padding: 10rpx 0;
  line-height: 25px;
}
.content.data-v-eabe1ba6 {
  position: relative;
  /*height: calc(100vh - 500rpx);*/
  min-height: 50rpx;
  background: #FFFFFF;
  border-radius: 50rpx 50rpx 0 0;
  margin-top: -50rpx;
  padding: 30rpx 20rpx 0 20rpx;
}
.details.data-v-eabe1ba6 {
  margin-top: 15rpx;
  padding: 30rpx 20rpx 0 20rpx;
  background: #FFFFFF;
}
.share-btn.data-v-eabe1ba6 {
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  padding: 0;
  margin: 0;
}
.share-btn.data-v-eabe1ba6::after {
  border: none;
}