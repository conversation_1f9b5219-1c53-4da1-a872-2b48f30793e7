<view class="data-v-f52152ea"><z-paging wx:if="{{l}}" class="r data-v-f52152ea" u-s="{{['d']}}" u-r="paging" bindquery="{{j}}" u-i="f52152ea-0" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"><view class="top data-v-f52152ea"></view><view class="content_view data-v-f52152ea"><view class="button data-v-f52152ea"><text class="tag data-v-f52152ea">历史发布</text><uv-button class="data-v-f52152ea" u-s="{{['d']}}" bindtap="{{a}}" u-i="f52152ea-1,f52152ea-0" bind:__l="__l"> +新增发布</uv-button></view><view class="uv-input data-v-f52152ea"><uv-input wx:if="{{d}}" class="data-v-f52152ea" bindchange="{{b}}" u-i="f52152ea-2,f52152ea-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"></uv-input></view><view class="tab_content data-v-f52152ea"><view class="content_bg data-v-f52152ea"><uv-checkbox-group wx:if="{{h}}" class="data-v-f52152ea" u-s="{{['d']}}" bindchange="{{f}}" u-i="f52152ea-3,f52152ea-0" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><block wx:for="{{e}}" wx:for-item="item" wx:key="j"><view class="flex data-v-f52152ea" style="border-bottom:1px solid #ececec;padding-bottom:10px;margin-bottom:12px"><uv-checkbox wx:if="{{item.a}}" class="data-v-f52152ea" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></uv-checkbox><view wx:else class="data-v-f52152ea" style="width:24px"></view><view class="flex data-v-f52152ea" style="width:85vw"><image class="data-v-f52152ea" src="{{item.d}}" mode="aspectFit"></image><view class="flex-col data-v-f52152ea" style="width:40vw"><view class="title overflow-hidden data-v-f52152ea">{{item.e}}</view><view class="time data-v-f52152ea">{{item.f}}</view></view><view class="flex-col data-v-f52152ea"><view class="data-v-f52152ea" catchtap="{{item.g}}" style="border-bottom:1px solid #FF9F18;color:#FF9F18;font-size:28rpx;text-align:center;line-height:44rpx">查看详情</view><view class="{{['data-v-f52152ea', item.i]}}" style="text-align:center">{{item.h}}</view></view></view></view></block></uv-checkbox-group></view></view></view></z-paging><view class="fixed data-v-f52152ea"><uv-checkbox-group wx:if="{{p}}" class="data-v-f52152ea" u-s="{{['d']}}" bindchange="{{n}}" u-i="f52152ea-5" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"><uv-checkbox wx:if="{{m}}" class="data-v-f52152ea" u-i="f52152ea-6,f52152ea-5" bind:__l="__l" u-p="{{m}}"></uv-checkbox></uv-checkbox-group><view class="del-btn data-v-f52152ea" customStyle="margin-top: 10px" bindtap="{{q}}">删除</view></view></view>