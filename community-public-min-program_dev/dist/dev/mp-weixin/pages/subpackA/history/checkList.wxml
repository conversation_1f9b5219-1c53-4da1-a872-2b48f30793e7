<view class="data-v-05d899a9"><z-paging wx:if="{{l}}" class="r data-v-05d899a9" u-s="{{['d']}}" u-r="paging" bindquery="{{j}}" u-i="05d899a9-0" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"><view class="top data-v-05d899a9"></view><view class="content_view data-v-05d899a9"><view class="tab_content data-v-05d899a9"><view class="custom-tabs data-v-05d899a9"><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="{{['data-v-05d899a9', 'custom-tab-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view><view class="uv-input data-v-05d899a9"><uv-input wx:if="{{d}}" class="data-v-05d899a9" bindchange="{{b}}" u-i="05d899a9-1,05d899a9-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"></uv-input></view><view class="content_bg data-v-05d899a9"><uv-checkbox-group wx:if="{{h}}" class="data-v-05d899a9" u-s="{{['d']}}" bindchange="{{f}}" u-i="05d899a9-2,05d899a9-0" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><block wx:for="{{e}}" wx:for-item="item" wx:key="l"><view class="flex data-v-05d899a9" style="border-bottom:1px solid #ececec;padding:10px 0;margin-bottom:12px"><uv-checkbox wx:if="{{item.a}}" class="data-v-05d899a9" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></uv-checkbox><view wx:else class="data-v-05d899a9" style="width:24px"></view><view class="flex data-v-05d899a9" style="width:85vw"><image class="data-v-05d899a9" src="{{item.d}}" mode=""></image><view class="flex-col data-v-05d899a9" style="width:40vw"><view class="title overflow-hidden data-v-05d899a9">{{item.e}}</view><view class="area overflow-hidden data-v-05d899a9">所属社区：{{item.f}}</view><view class="area data-v-05d899a9">提交人：{{item.g}}</view><view class="time data-v-05d899a9">{{item.h}}</view></view><view class="flex-col data-v-05d899a9"><view class="data-v-05d899a9" catchtap="{{item.i}}" style="font-size:32rpx"><image class="icon-right data-v-05d899a9" src="/static/images/<EMAIL>" mode="aspectFit"></image></view><view class="{{['data-v-05d899a9', item.k]}}">{{item.j}}</view></view></view></view></block></uv-checkbox-group></view></view></view></z-paging><view class="fixed data-v-05d899a9"><uv-checkbox-group wx:if="{{p}}" class="data-v-05d899a9" u-s="{{['d']}}" bindchange="{{n}}" u-i="05d899a9-4" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"><uv-checkbox wx:if="{{m}}" class="data-v-05d899a9" u-i="05d899a9-5,05d899a9-4" bind:__l="__l" u-p="{{m}}"></uv-checkbox></uv-checkbox-group><view class="btn-reject btn-item data-v-05d899a9" bindtap="{{q}}">审核驳回</view><view class="btn-pass btn-item data-v-05d899a9" bindtap="{{r}}">审核通过</view></view></view>