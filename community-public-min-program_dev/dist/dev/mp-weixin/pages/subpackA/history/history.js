"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_history = require("../../../common/api/history.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_checkbox2 = common_vendor.resolveComponent("uv-checkbox");
  const _easycom_uv_checkbox_group2 = common_vendor.resolveComponent("uv-checkbox-group");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_button2 + _easycom_uv_input2 + _easycom_uv_checkbox2 + _easycom_uv_checkbox_group2 + _easycom_z_paging2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_checkbox = () => "../../../uni_modules/uv-checkbox/components/uv-checkbox/uv-checkbox.js";
const _easycom_uv_checkbox_group = () => "../../../uni_modules/uv-checkbox/components/uv-checkbox-group/uv-checkbox-group.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_input + _easycom_uv_checkbox + _easycom_uv_checkbox_group + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "history",
  setup(__props) {
    stores_store.useUserStore();
    stores_store.useAreaStore();
    const searchObj = common_vendor.reactive({
      key: "",
      pageNum: 1
    });
    const scrollHeight = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const paging = common_vendor.ref();
    let checkboxValue = common_vendor.reactive([]), radioVal = common_vendor.ref();
    const handleStatus = (status) => {
      switch (status) {
        case "0":
          return { text: "展示中" };
        case "1":
          return { text: "已删除" };
      }
    };
    function toPage(url, id = "") {
      common_vendor.index.navigateTo({
        url: url + `?id=${id}`
      });
    }
    function toDetails(item) {
      let url = `details?id=${item.activityId}`;
      common_vendor.index.navigateTo({
        url
      });
    }
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight;
    };
    const debounce = (fn, delay = 1e3) => {
      let timer;
      return function() {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(() => {
          fn();
          timer = null;
        }, delay);
      };
    };
    const searchKey = debounce(() => {
      searchObj.pageNum = 1;
      getPageList();
    }, 300);
    const queryList = (pageNo, pageSize) => {
      console.log(pageNo, pageSize);
      searchObj.pageNum = pageNo;
      getPageList();
    };
    const getPageList = async () => {
      const res = await common_api_history.getrecentlyActivity({
        ...searchObj,
        pageSize: 10
      });
      if ((res == null ? void 0 : res.code) === 200) {
        paging.value.complete(res.rows);
      } else {
        throw console.log(res);
      }
    };
    const choose = (val) => {
      radioVal.value = [];
      if (val.length === dataList.value.length) {
        radioVal.value = ["1"];
      }
    };
    const allChoose = (val) => {
      checkboxValue = [];
      if (val.length && dataList.value.length) {
        dataList.value.forEach((item) => {
          item.status == "0" && checkboxValue.push(item.activityId);
        });
      }
    };
    const submit = async () => {
      if (checkboxValue.length === 0) {
        common_vendor.index.showToast({
          icon: "none",
          title: "请选择数据"
        });
        return;
      }
      const res = await common_api_history.deleteActivity({
        ids: checkboxValue
      });
      if ((res == null ? void 0 : res.code) === 200) {
        radioVal.value = [];
        checkboxValue = [];
        searchObj.pageNum = 1;
        getPageList();
      } else {
        throw console.log(res);
      }
    };
    common_vendor.onLoad(async () => {
      getScreenHeight();
    });
    common_vendor.onShow(() => {
      if (common_vendor.index.getStorageSync("isBack")) {
        searchObj.pageNum = 1;
        getPageList();
        common_vendor.index.setStorageSync("isBack", false);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => toPage("/pages/subpackA/history/release")),
        b: common_vendor.o(common_vendor.unref(searchKey)),
        c: common_vendor.o(($event) => searchObj.key = $event),
        d: common_vendor.p({
          placeholder: "搜索",
          prefixIcon: "search",
          prefixIconStyle: "font-size: 36rpx;color: #909399",
          border: "surround",
          modelValue: searchObj.key
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.status == "0"
          }, item.status == "0" ? {
            b: "f52152ea-4-" + i0 + ",f52152ea-3",
            c: common_vendor.p({
              name: item.activityId,
              activeColor: "#FF9F18"
            })
          } : {}, {
            d: item == null ? void 0 : item.coverImage,
            e: common_vendor.t(item == null ? void 0 : item.title),
            f: common_vendor.t(item == null ? void 0 : item.createTime),
            g: common_vendor.o(($event) => toDetails(item), index),
            h: common_vendor.t(handleStatus(item.status).text),
            i: common_vendor.n(item.status === "0" ? "ing" : "del"),
            j: index
          });
        }),
        f: common_vendor.o(choose),
        g: common_vendor.o(($event) => common_vendor.isRef(checkboxValue) ? checkboxValue.value = $event : checkboxValue = $event),
        h: common_vendor.p({
          modelValue: common_vendor.unref(checkboxValue)
        }),
        i: common_vendor.sr(paging, "f52152ea-0", {
          "k": "paging"
        }),
        j: common_vendor.o(queryList),
        k: common_vendor.o(($event) => dataList.value = $event),
        l: common_vendor.p({
          ["paging-style"]: {
            "height": scrollHeight.value + "px"
          },
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        m: common_vendor.p({
          customStyle: {
            marginBottom: "8px"
          },
          name: "1",
          activeColor: "#FF9F18",
          label: "全选"
        }),
        n: common_vendor.o(allChoose),
        o: common_vendor.o(($event) => common_vendor.isRef(radioVal) ? radioVal.value = $event : radioVal = $event),
        p: common_vendor.p({
          modelValue: common_vendor.unref(radioVal)
        }),
        q: common_vendor.o(submit)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f52152ea"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/history/history.vue"]]);
wx.createPage(MiniProgramPage);
