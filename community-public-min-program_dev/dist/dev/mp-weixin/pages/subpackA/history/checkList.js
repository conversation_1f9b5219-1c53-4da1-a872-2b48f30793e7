"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_history = require("../../../common/api/history.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_checkbox2 = common_vendor.resolveComponent("uv-checkbox");
  const _easycom_uv_checkbox_group2 = common_vendor.resolveComponent("uv-checkbox-group");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_input2 + _easycom_uv_checkbox2 + _easycom_uv_checkbox_group2 + _easycom_z_paging2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_checkbox = () => "../../../uni_modules/uv-checkbox/components/uv-checkbox/uv-checkbox.js";
const _easycom_uv_checkbox_group = () => "../../../uni_modules/uv-checkbox/components/uv-checkbox-group/uv-checkbox-group.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_checkbox + _easycom_uv_checkbox_group + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "checkList",
  setup(__props) {
    stores_store.useUserStore();
    stores_store.useAreaStore();
    const tabs = Object.freeze([
      { name: "全部", type: "99" },
      { name: "待审核", type: "0" },
      { name: "审核通过", type: "1" },
      { name: "审核驳回", type: "2" }
    ]);
    const searchObj = common_vendor.reactive({
      key: "",
      tab: "99",
      type: "",
      pageNum: 1
    });
    const scrollHeight = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const paging = common_vendor.ref();
    let checkboxValue = common_vendor.reactive([]), radioVal = common_vendor.ref();
    const handleStatus = (status) => {
      switch (status) {
        case "0":
          return { text: "待审核", type: "ing" };
        case "1":
          return { text: "审核通过", type: "pass" };
        case "2":
          return { text: "审核驳回", type: "reject" };
      }
    };
    function toDetails(item) {
      let url = `details?id=${item.id}&type=${searchObj.type}`;
      common_vendor.index.navigateTo({
        url
      });
    }
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 70;
    };
    const debounce = (fn, delay = 1e3) => {
      let timer;
      return function() {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(() => {
          fn();
          timer = null;
        }, delay);
      };
    };
    const searchKey = debounce(() => {
      searchObj.pageNum = 1;
      getPageList();
    }, 300);
    const changeTabs = (e) => {
      console.log(e);
      searchObj.tab = e.type;
      searchObj.pageNum = 1;
      getPageList();
    };
    const queryList = (pageNo, pageSize) => {
      console.log(pageNo, pageSize);
      searchObj.pageNum = pageNo;
      getPageList();
    };
    const getPageList = async () => {
      const res = await common_api_history.getReviewList({
        ...searchObj,
        pageSize: 10
      });
      if ((res == null ? void 0 : res.code) === 200) {
        paging.value.complete(res.rows);
      } else {
        throw console.log(res);
      }
    };
    const choose = (val) => {
      radioVal.value = [];
      if (val.length === dataList.value.length) {
        radioVal.value = ["1"];
      }
    };
    const allChoose = (val) => {
      checkboxValue = [];
      if (val.length && dataList.value.length) {
        dataList.value.forEach((item) => {
          item.tab == "0" && checkboxValue.push(item.id);
        });
      }
    };
    const submit = async (status) => {
      if (checkboxValue.length === 0) {
        common_vendor.index.showToast({
          icon: "none",
          title: "请选择数据"
        });
        return;
      }
      const res = await common_api_history.reviewData({
        type: parseInt(searchObj.type),
        ids: checkboxValue,
        status
      });
      if ((res == null ? void 0 : res.code) === 200) {
        radioVal.value = [];
        checkboxValue = [];
        searchObj.pageNum = 1;
        getPageList();
      } else {
        throw console.log(res);
      }
    };
    common_vendor.onLoad((option) => {
      getScreenHeight();
      console.log(option, "option");
      searchObj.type = option.type;
    });
    common_vendor.onShow(() => {
      if (common_vendor.index.getStorageSync("isBack")) {
        searchObj.pageNum = 1;
        getPageList();
        common_vendor.index.setStorageSync("isBack", false);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(common_vendor.unref(tabs), (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.n(searchObj.tab === item.type ? "active-tab" : "inactive-tab"),
            d: common_vendor.o(($event) => changeTabs({
              type: item.type,
              index
            }), index)
          };
        }),
        b: common_vendor.o(common_vendor.unref(searchKey)),
        c: common_vendor.o(($event) => searchObj.key = $event),
        d: common_vendor.p({
          placeholder: "搜索",
          prefixIcon: "search",
          prefixIconStyle: "font-size: 36rpx;color: #909399",
          border: "surround",
          modelValue: searchObj.key
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.tab != "0"
          }, item.tab != "0" ? {
            b: "05d899a9-3-" + i0 + ",05d899a9-2",
            c: common_vendor.p({
              name: item.id,
              activeColor: "#FF9F18"
            })
          } : {}, {
            d: (item == null ? void 0 : item.coverImage) || "https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/2c3406a9754a4a75871fa17923f9f635.png",
            e: common_vendor.t(item.title),
            f: common_vendor.t(item.districtName),
            g: common_vendor.t(item.userName),
            h: common_vendor.t(item.createTime),
            i: common_vendor.o(($event) => toDetails(item), index),
            j: common_vendor.t(handleStatus(item.tab).text),
            k: common_vendor.n(handleStatus(item.tab).type),
            l: index
          });
        }),
        f: common_vendor.o(choose),
        g: common_vendor.o(($event) => common_vendor.isRef(checkboxValue) ? checkboxValue.value = $event : checkboxValue = $event),
        h: common_vendor.p({
          modelValue: common_vendor.unref(checkboxValue)
        }),
        i: common_vendor.sr(paging, "05d899a9-0", {
          "k": "paging"
        }),
        j: common_vendor.o(queryList),
        k: common_vendor.o(($event) => dataList.value = $event),
        l: common_vendor.p({
          ["paging-style"]: {
            "height": scrollHeight.value + "px"
          },
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        m: common_vendor.p({
          customStyle: {
            marginBottom: "8px"
          },
          name: "1",
          label: "全选",
          activeColor: "#FF9F18"
        }),
        n: common_vendor.o(allChoose),
        o: common_vendor.o(($event) => common_vendor.isRef(radioVal) ? radioVal.value = $event : radioVal = $event),
        p: common_vendor.p({
          modelValue: common_vendor.unref(radioVal)
        }),
        q: common_vendor.o(($event) => submit(2)),
        r: common_vendor.o(($event) => submit(1))
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-05d899a9"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/history/checkList.vue"]]);
wx.createPage(MiniProgramPage);
