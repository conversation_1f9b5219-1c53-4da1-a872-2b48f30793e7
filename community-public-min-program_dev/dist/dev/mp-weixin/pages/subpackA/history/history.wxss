/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.overflow-hidden.data-v-f52152ea {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.flex.data-v-f52152ea {
  display: flex;
}
.flex-col.data-v-f52152ea {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.top.data-v-f52152ea {
  background: #FFCC35;
  padding: 24rpx;
}
.content_view.data-v-f52152ea {
  margin-top: -26rpx;
  padding: 28rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view .button.data-v-f52152ea {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.content_view .button .tag.data-v-f52152ea {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
}
.content_view .button .tag.data-v-f52152ea::before {
  content: "";
  display: block;
  width: 8rpx;
  height: 28rpx;
  background: #FF9F18;
  border-radius: 2rpx;
  margin-right: 8px;
}
.content_view .button text.data-v-f52152ea {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 34rpx;
  color: #000000;
}
.content_view .button.data-v-f52152ea .uv-button--info {
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  line-height: 44rpx;
  border: none;
  border-radius: 4rpx !important;
  background: #FF9F18;
}
.content_view .uv-input.data-v-f52152ea {
  width: 100%;
  margin: 32rpx 0;
}
.content_view .uv-input uv-input.data-v-f52152ea {
  width: 100% !important;
}
.content_view .tab_content.data-v-f52152ea {
  width: 100%;
}
.content_view .tab_content .content_bg.data-v-f52152ea {
  margin: 16rpx 0;
  padding-bottom: 16rpx;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}
.content_view .tab_content image.data-v-f52152ea {
  width: 221rpx;
  height: 141rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
}
.content_view .tab_content .title.data-v-f52152ea {
  font-weight: 500;
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
}
.content_view .tab_content .area.data-v-f52152ea {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
}
.content_view .tab_content .time.data-v-f52152ea {
  font-weight: 400;
  font-size: 28rpx;
  color: #CCCCCC;
  line-height: 44rpx;
}
.content_view .tab_content .ing.data-v-f52152ea {
  background: rgba(255, 159, 24, 0.1);
  border-radius: 4rpx;
  color: #FF9F18;
  font-size: 24rpx;
  padding: 4rpx 6rpx;
}
.content_view .tab_content .del.data-v-f52152ea {
  background: #F5F5F5;
  border-radius: 4rpx;
  color: #CCCCCC;
  font-size: 24rpx;
  padding: 4rpx 6rpx;
}
.fixed.data-v-f52152ea {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  height: 70px;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.fixed.data-v-f52152ea .uv-checkbox-group {
  flex: none !important;
}
.fixed.data-v-f52152ea .uv-checkbox {
  margin: 0 !important;
}
.fixed .del-btn.data-v-f52152ea {
  width: 240rpx;
  height: 80rpx;
  background: #FF3B30;
  border-radius: 16rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
}