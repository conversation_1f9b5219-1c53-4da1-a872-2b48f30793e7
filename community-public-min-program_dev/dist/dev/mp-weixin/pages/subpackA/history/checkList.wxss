/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.active-tab.data-v-05d899a9 {
  font-weight: 500;
  font-size: 40rpx;
  color: #333333;
  line-height: 56rpx;
  position: relative;
}
.active-tab.data-v-05d899a9::before {
  content: "";
  display: block;
  background: #FF9F18;
  border-radius: 4rpx;
  width: 48rpx;
  height: 8rpx;
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
}
.inactive-tab.data-v-05d899a9 {
  font-weight: 400;
  font-size: 32rpx;
  color: #999999;
  line-height: 48rpx;
}
.custom-tabs.data-v-05d899a9 {
  display: flex;
  align-items: center;
  position: relative;
  height: 88rpx;
}
.custom-tab-item.data-v-05d899a9 {
  text-align: center;
  padding: 0 20rpx;
}
.overflow-hidden.data-v-05d899a9 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.flex.data-v-05d899a9 {
  display: flex;
  justify-content: space-between;
}
.flex-col.data-v-05d899a9 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}
.flex-col .icon-right.data-v-05d899a9 {
  width: 32rpx !important;
  height: 32rpx !important;
  position: absolute;
  right: 10rpx;
  top: 10rpx;
}
.top.data-v-05d899a9 {
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  padding: 24rpx;
}
.content_view.data-v-05d899a9 {
  margin-top: -26rpx;
  padding: 28rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view .button.data-v-05d899a9 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.content_view .button .tag.data-v-05d899a9::before {
  content: "";
  display: inline-block;
  background: #ffffff;
  height: 6px;
  border: 2px solid #FF9F18;
  margin-right: 10px;
}
.content_view .button text.data-v-05d899a9 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 34rpx;
  color: #000000;
}
.content_view .button.data-v-05d899a9 .uv-button--info {
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  background: #FF9F18;
}
.content_view .uv-input.data-v-05d899a9 {
  width: 100%;
  margin: 16rpx 0;
}
.content_view .uv-input uv-input.data-v-05d899a9 {
  width: 100% !important;
}
.content_view .tab_content.data-v-05d899a9 {
  width: 100%;
}
.content_view .tab_content .content_bg.data-v-05d899a9 {
  margin: 16rpx 0;
  padding-bottom: 16rpx;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}
.content_view .tab_content image.data-v-05d899a9 {
  width: 180rpx;
  height: 240rpx;
  border-radius: 10rpx;
}
.content_view .tab_content .title.data-v-05d899a9 {
  font-weight: 500;
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
  margin-bottom: 8rpx;
}
.content_view .tab_content .area.data-v-05d899a9 {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
}
.content_view .tab_content .time.data-v-05d899a9 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #CCCCCC;
  line-height: 44rpx;
}
.content_view .tab_content .ing.data-v-05d899a9 {
  background: rgba(255, 159, 24, 0.1);
  border-radius: 4rpx;
  color: #FF9F18;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
}
.content_view .tab_content .pass.data-v-05d899a9 {
  background: rgba(4, 181, 120, 0.1);
  border-radius: 4rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #04B578;
  line-height: 40rpx;
  padding: 4rpx 12rpx;
  box-sizing: border-box;
}
.content_view .tab_content .reject.data-v-05d899a9 {
  background: rgba(255, 59, 48, 0.1);
  border-radius: 4rpx;
  color: #FF3B30;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  line-height: 40rpx;
  box-sizing: border-box;
}
.fixed.data-v-05d899a9 {
  position: fixed;
  width: 100%;
  bottom: 0;
  box-sizing: border-box;
  padding: 32rpx;
  background: #fff;
  height: 70px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.fixed.data-v-05d899a9 .uv-checkbox-group {
  flex: none !important;
}
.fixed.data-v-05d899a9 .uv-checkbox {
  margin: 0 !important;
}
.fixed .btn-reject.data-v-05d899a9 {
  background: #FF3B30;
}
.fixed .btn-pass.data-v-05d899a9 {
  background: #04B578;
}
.fixed .btn-item.data-v-05d899a9 {
  width: 240rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 16rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
}