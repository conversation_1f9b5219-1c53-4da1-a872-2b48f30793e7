/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.top {
  padding: 8px 0 26px 16px;
  text-align: left;
  color: #ffffff;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.top .title {
  font-weight: 500;
  font-size: 48rpx;
  color: #FFFFFF;
  line-height: 64rpx;
  margin-bottom: 4rpx;
}
.top .top-location-box {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
}
.top .top-location-box image:first-of-type {
  width: 29rpx;
  height: 29rpx;
  margin-right: 4rpx;
}
.ai_fixed {
  position: fixed;
  top: 0;
}
.middle_view {
  width: 100%;
  padding: 12px 16px 50px 16px;
  box-sizing: border-box;
  margin-top: -10px;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.middle_view .tag {
  padding: 8px 14px;
  background: #F5F5F5;
  position: absolute;
  bottom: 0;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
  font-size: 14px;
  color: #222222;
  display: flex;
  align-items: center;
}
.middle_view .tag image {
  width: 33rpx;
  height: 33rpx;
  margin-right: 0;
  border-radius: 0;
  margin-left: 10rpx;
}
.middle_view .phone-icon {
  width: 81rpx;
  height: 81rpx;
  margin-right: 0;
  border-radius: 0;
}
.middle_view image {
  width: 96rpx;
  height: 108rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
}
.middle_view .ai_desc {
  display: flex;
  flex-direction: column;
  width: 70%;
}
.middle_view .ai_desc .name {
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 44rpx;
  margin-bottom: 4rpx;
}
.middle_view .ai_desc .phone-text {
  margin-top: 4rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: #FF9F18; /* 突出显示手机号 */
  line-height: 32rpx;
}
.middle_view .user_bg {
  font-size: 0;
  margin-bottom: 4rpx;
}
.middle_view .user_bg text {
  font-weight: 400;
  font-size: 20rpx;
  color: #FF9F18;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background: rgba(255, 159, 24, 0.4);
  line-height: 32rpx;
}
.middle_view .user_desc {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  line-height: 32rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ai_view {
  position: relative;
}
.content_body {
  background-color: white;
}
.content_body scroll-view {
  background-color: #F5F5F5;
  padding: 4px 0;
}
.content_body .flex-center {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.content_body .flex_row_end {
  display: flex;
  justify-content: flex-end;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}
.content_body .flex_row_start {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.content_body .play_record {
  background: #FFFFFF;
  padding: 6px 6px;
  margin: 4px 4px;
}
.content_body .right_border {
  border-radius: 8px 0 8px 8px;
}
.content_body .left_border {
  border-radius: 8px 8px 8px 0;
}
.content_body .self {
  max-width: 80%;
  padding: 16rpx 24rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  background: rgba(255, 159, 24, 0.2);
  border-radius: 16rpx 16rpx 0rpx 16rpx;
}
.search_input {
  position: fixed;
  bottom: 6px;
  left: 1%;
  width: 98%;
  padding: 2px 2px;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
}
.search_input .uni-textarea {
  width: 500rpx;
  background: #f00;
  height: 72rpx;
  line-height: 72rpx;
  box-sizing: border-box;
  padding: 14rpx 24rpx;
  background: white;
}
.search_input .icon_svg {
  width: 30px;
  height: 30px;
  cursor: pointer;
  z-index: 999;
}
.search_input .send-btn-box {
  width: 80rpx;
  height: 50rpx;
}
.search_input .send-btn {
  width: 80rpx;
  height: 50rpx;
  line-height: 50rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 20rpx;
  box-sizing: border-box;
}