<view><view class="top"><text class="title">社区呼叫铃</text><view class="top-location-box"><image src="/static/index/location.png" mode=""></image><text>{{a}}</text></view></view><view class="ai_view middle_view"><image class="home-icon" src="{{b}}"></image><view class="ai_desc"><text class="name">{{c}}</text><view class="user_bg"><text hidden="{{!e}}">{{d}}</text></view><text class="user_desc">{{f}}</text></view><image class="phone-icon" src="/static/found/phone.png" bindtap="{{g}}"></image><view class="tag"> 在线留言<image src="/static/ai/arrow.png" mode=""></image></view></view><view class="content_body"><scroll-view scroll-top="20" scroll-y="true" bindscrolltoupper="{{i}}" style="{{'height:' + j}}"><block wx:for="{{h}}" wx:for-item="item" wx:key="j"><view class="{{[item.i]}}"><view hidden="{{!item.d}}" class="{{['play_record', item.e]}}"><a-trumpet wx:if="{{item.c}}" bindtap="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></a-trumpet></view><view hidden="{{!item.g}}" class="{{['self', item.h]}}">{{item.f}}</view></view></block></scroll-view></view><view class="search_input"><image class="icon_svg home-icon" src="/static/found/sound.svg" bindtap="{{k}}" hidden="{{!l}}"></image><image class="icon_svg home-icon" src="/static/found/keyboard.svg" hidden="{{!m}}" bindtap="{{n}}"></image><view hidden="{{!r}}" class="uni-textarea" style="text-align:center;line-height:40px" bindtouchstart="{{s}}" bindtouchend="{{t}}"><text hidden="{{!o}}">按住说话</text><view hidden="{{!q}}" style="display:flex;justify-content:center"><text>正在录音,松开发送</text><uv-loading-icon wx:if="{{p}}" u-i="046ab914-1" bind:__l="__l" u-p="{{p}}"></uv-loading-icon></view></view><block wx:if="{{r0}}"><textarea id="textRef" placeholder="问点什么" auto-focus="{{v}}" hidden="{{!w}}" class="uni-textarea" show-confirm-bar="{{false}}" cursor-spacing="{{30}}" value="{{x}}" bindinput="{{y}}"/></block><view class="send-btn-box"><image hidden="{{!z}}" class="icon_svg home-icon" src="/static/ai/send.svg" bindtap="{{A}}"></image><view class="send-btn" hidden="{{!B}}" bindtap="{{C}}" style="background:green;color:white">发送</view></view></view></view>