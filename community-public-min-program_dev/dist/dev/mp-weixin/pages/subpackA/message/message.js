"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_api_found = require("../../../common/api/found.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_a_trumpet2 = common_vendor.resolveComponent("a-trumpet");
  const _easycom_uv_loading_icon2 = common_vendor.resolveComponent("uv-loading-icon");
  (_easycom_a_trumpet2 + _easycom_uv_loading_icon2)();
}
const _easycom_a_trumpet = () => "../../../components/a-trumpet/a-trumpet.js";
const _easycom_uv_loading_icon = () => "../../../uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon.js";
if (!Math) {
  (_easycom_a_trumpet + _easycom_uv_loading_icon)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "message",
  setup(__props) {
    const { proxy } = common_vendor.getCurrentInstance();
    const areaStore = stores_store.useAreaStore();
    const userStore = stores_store.useUserStore();
    let scrollHeight = common_vendor.ref(), textRef = common_vendor.ref(false);
    let userInfo = common_vendor.reactive({});
    let recordOrText = common_vendor.ref(true), isRecord = common_vendor.ref(false);
    let searchObj = common_vendor.reactive({
      keyWord: "",
      list: [],
      pageNum: 1,
      total: 0
    });
    const callWorker = () => {
      if (!userInfo.phone && !userInfo.telephone) {
        common_vendor.index.showToast({
          title: "暂无联系方式",
          icon: "none"
        });
        return;
      }
      const phoneNumber = userInfo.phone || userInfo.telephone || "";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        success: function() {
          console.log("拨打电话成功");
        },
        fail: function(err) {
          console.error("拨打电话失败", err);
        }
      });
    };
    const scrolltoupper = () => {
      searchObj.pageNum += 1;
      let remainder = Math.floor(searchObj.total / 12);
      let template = searchObj.total % 12 > 0 ? 1 : 0;
      if (searchObj.pageNum > remainder + template) {
        return;
      }
      getMessageList();
    };
    const getMessageList = async () => {
      const result = await common_api_found.getPageMsg(userInfo.workersId, {
        isAsc: "asc",
        pageNum: searchObj.pageNum,
        pageSize: 12
      });
      if ((result == null ? void 0 : result.code) === 200) {
        searchObj.total = result.total || 0;
        if (result.rows && Array.isArray(result.rows)) {
          result.rows.forEach((item) => {
            item.isPlay = false;
          });
          searchObj.list.unshift(...result.rows);
        }
      } else {
        console.error("获取消息列表失败:", result);
      }
    };
    const sendMsgPost = async (content, type) => {
      const result = await common_api_found.sendMsg({
        content,
        contentType: type,
        receiverId: userInfo.workersId
      });
      if ((result == null ? void 0 : result.code) === 200 && result.data) {
        console.log("发送消息成功:", result);
        searchObj.keyWord = "";
        searchObj.list.push({ ...result.data, isPlay: false });
      } else {
        console.error("发送消息失败:", result);
      }
    };
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - (46 + 86 + 119) - 8;
    };
    const innerAudioContext = common_vendor.index.createInnerAudioContext();
    const playRecord = (item) => {
      searchObj.list.forEach((msg) => {
        msg.isPlay = false;
      });
      item.isPlay = true;
      innerAudioContext.src = item.content;
      innerAudioContext.play();
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            console.log(uploadFileRes);
            const responseData = JSON.parse(uploadFileRes.data);
            if (responseData && responseData.data) {
              resolve(responseData.data);
            } else {
              reject(new Error("上传响应格式错误"));
            }
          },
          fail: function(err) {
            reject(err);
          }
        });
      });
    };
    const hidden = () => {
      recordOrText.value = false;
      textRef.value = false;
    };
    const showKeyboard = () => {
      recordOrText.value = true;
      proxy.$nextTick(() => {
        textRef.value = true;
      });
    };
    const recorderManager = common_vendor.index.getRecorderManager();
    const startRecord = () => {
      console.log("开始录音");
      isRecord.value = true;
      recorderManager.start({
        duration: 6e4
      });
    };
    const endRecord = () => {
      console.log("录音结束");
      recorderManager.stop();
      isRecord.value = false;
    };
    common_vendor.onLoad((option) => {
      if (option) {
        Object.assign(userInfo, option);
        console.log("接收到的参数:", option);
        console.log("接收到的phone:", option.phone);
      }
      recorderManager.onStop(async (res) => {
        console.log("recorder stop" + JSON.stringify(res), res.duration);
        if (res.duration <= 1e3) {
          common_vendor.index.showLoading({
            title: "说话声音太短",
            mask: true
          });
          setTimeout(() => {
            common_vendor.index.hideLoading();
          }, 800);
        } else {
          try {
            const uploadRes = await uploadFile(res.tempFilePath);
            sendMsgPost(uploadRes.url, "audio");
          } catch (err) {
            console.error("上传录音失败:", err);
            common_vendor.index.showToast({
              title: "上传失败",
              icon: "none"
            });
          }
        }
      });
      getScreenHeight();
      getMessageList();
      innerAudioContext.onEnded(() => {
        searchObj.list.forEach((item) => {
          item.isPlay = false;
        });
      });
    });
    common_vendor.onUnload(() => {
      innerAudioContext.pause();
      innerAudioContext.destroy();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(common_vendor.unref(areaStore).areaName),
        b: common_vendor.unref(userInfo).avatarUrl,
        c: common_vendor.t(common_vendor.unref(userInfo).name),
        d: common_vendor.t(common_vendor.unref(userInfo).title),
        e: common_vendor.unref(userInfo).title,
        f: common_vendor.t(common_vendor.unref(userInfo).remark),
        g: common_vendor.o(callWorker),
        h: common_vendor.f(common_vendor.unref(searchObj).list, (item, i, i0) => {
          var _a, _b, _c, _d;
          return {
            a: common_vendor.o(($event) => playRecord(item), i),
            b: "046ab914-0-" + i0,
            c: common_vendor.p({
              isPlay: item.isPlay,
              direction: item.senUserId === ((_a = common_vendor.unref(userStore).userInfo) == null ? void 0 : _a.userId) ? "left" : "right",
              color: "green"
            }),
            d: item.contentType === "audio",
            e: common_vendor.n(item.senUserId === ((_b = common_vendor.unref(userStore).userInfo) == null ? void 0 : _b.userId) ? "right_border" : "left_border"),
            f: common_vendor.t(item.content),
            g: item.contentType === "text",
            h: common_vendor.n(item.senUserId === ((_c = common_vendor.unref(userStore).userInfo) == null ? void 0 : _c.userId) ? "right_border" : "left_border"),
            i: common_vendor.n(item.senUserId === ((_d = common_vendor.unref(userStore).userInfo) == null ? void 0 : _d.userId) ? "flex_row_end" : "flex_row_start"),
            j: i
          };
        }),
        i: common_vendor.o(scrolltoupper),
        j: common_vendor.unref(scrollHeight) + "px",
        k: common_vendor.o(hidden),
        l: common_vendor.unref(recordOrText),
        m: !common_vendor.unref(recordOrText),
        n: common_vendor.o(showKeyboard),
        o: !common_vendor.unref(isRecord),
        p: common_vendor.p({
          size: "22"
        }),
        q: common_vendor.unref(isRecord),
        r: !common_vendor.unref(recordOrText),
        s: common_vendor.o(startRecord),
        t: common_vendor.o(endRecord),
        v: common_vendor.unref(textRef),
        w: common_vendor.unref(recordOrText),
        x: common_vendor.unref(searchObj).keyWord,
        y: common_vendor.o(($event) => common_vendor.unref(searchObj).keyWord = $event.detail.value),
        z: !common_vendor.unref(searchObj).keyWord,
        A: common_vendor.o(($event) => common_vendor.isRef(recordOrText) ? recordOrText.value = true : recordOrText = true),
        B: common_vendor.unref(searchObj).keyWord,
        C: common_vendor.o(($event) => sendMsgPost(common_vendor.unref(searchObj).keyWord, "text"))
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/message/message.vue"]]);
wx.createPage(MiniProgramPage);
