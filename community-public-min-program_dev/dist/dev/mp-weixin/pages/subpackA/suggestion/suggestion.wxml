<view class="data-v-0ce8bb84"><z-paging wx:if="{{j}}" class="r data-v-0ce8bb84" u-s="{{['d']}}" u-r="paging" bindquery="{{h}}" u-i="0ce8bb84-0" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"><view class="top data-v-0ce8bb84"><view class="title data-v-0ce8bb84"><view class="button data-v-0ce8bb84"><text class="data-v-0ce8bb84">建言建议广场</text><uv-button class="data-v-0ce8bb84" u-s="{{['d']}}" bindtap="{{a}}" u-i="0ce8bb84-1,0ce8bb84-0" bind:__l="__l"> 我要建言</uv-button></view><view class="top-location-box data-v-0ce8bb84" bindtap="{{c}}"><image class="data-v-0ce8bb84" src="/static/index/location.png" mode=""></image><text class="data-v-0ce8bb84">{{b}}</text><image class="data-v-0ce8bb84" src="/static/index/switch.png" mode=""></image></view></view></view><view class="content_view data-v-0ce8bb84"><uv-tabs wx:if="{{e}}" class="data-v-0ce8bb84" bindclick="{{d}}" u-i="0ce8bb84-2,0ce8bb84-0" bind:__l="__l" u-p="{{e}}"></uv-tabs><view class="tab_content data-v-0ce8bb84"><block wx:for="{{f}}" wx:for-item="item" wx:key="p"><view class="content_bg data-v-0ce8bb84" bindtap="{{item.o}}"><view class="img_bg data-v-0ce8bb84"><image class="data-v-0ce8bb84" src="{{item.a}}" style="border-top-right-radius:10rpx;border-top-left-radius:10rpx" mode=""></image><text wx:if="{{item.b}}" class="left_top data-v-0ce8bb84">{{item.c}}</text><text wx:if="{{item.d}}" class="right_top data-v-0ce8bb84" style="{{'background:' + item.f}}">{{item.e}}</text><view class="left_bottom data-v-0ce8bb84"><text wx:if="{{item.g}}" class="data-v-0ce8bb84">已建言{{item.h}}</text><text wx:if="{{item.i}}" class="data-v-0ce8bb84">{{item.j}}</text><text wx:if="{{item.k}}" class="data-v-0ce8bb84" style="display:inline-block;width:100%;text-align:right">{{item.l}}</text></view></view><text class="title data-v-0ce8bb84">{{item.m}}</text><view class="overflow-hidden data-v-0ce8bb84">{{item.n}}</view></view></block></view></view></z-paging></view>