"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_api_system = require("../../../common/api/system.js");
const common_api_suggestion = require("../../../common/api/suggestion.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/request.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_a_record2 = common_vendor.resolveComponent("a-record");
  const _easycom_uv_upload2 = common_vendor.resolveComponent("uv-upload");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_uv_textarea2 + _easycom_a_audio2 + _easycom_a_record2 + _easycom_uv_upload2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_uv_picker2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_a_record = () => "../../../components/a-record/a-record.js";
const _easycom_uv_upload = () => "../../../uni_modules/uv-upload/components/uv-upload/uv-upload.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_uv_textarea + _easycom_a_audio + _easycom_a_record + _easycom_uv_upload + _easycom_uv_button + _easycom_uv_form + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "insert-suggest",
  setup(__props) {
    stores_store.useUserStore();
    stores_store.useAreaStore();
    const rules = Object.freeze({
      type: {
        type: "string",
        required: true,
        message: "请选择建言分类",
        trigger: ["blur", "change"]
      },
      title: {
        type: "string",
        required: true,
        message: "请输入建言主题",
        trigger: ["blur", "change"]
      },
      content: {
        type: "string",
        required: true,
        message: "请输入建言事由",
        trigger: ["blur", "change"]
      },
      stratagem: {
        type: "string",
        required: true,
        message: "请输入建言措施",
        trigger: ["blur", "change"]
      },
      department: {
        type: "string",
        required: false,
        message: "请选择接收单位",
        trigger: ["blur", "change"]
      },
      idCard: {
        type: "string",
        pattern: /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
        message: "请输入正确的身份证号",
        trigger: ["blur", "change"]
      },
      contactPhone: {
        type: "string",
        message: "请输入正确的联系电话",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      }
    });
    const fileList = common_vendor.ref([]);
    const audioFile = common_vendor.ref({});
    let form = common_vendor.reactive({
      title: null,
      type: null,
      content: null,
      stratagem: null,
      fileIds: [
        0
      ],
      voiceId: null,
      department: null,
      districtCode: null,
      contactName: null,
      contactPhone: null,
      idCard: null
    });
    const formRef = common_vendor.ref(), pickerRef = common_vendor.ref();
    let nameObj = common_vendor.reactive({
      areaName: null,
      departName: null,
      typeName: null
    });
    const handlePicker = common_vendor.reactive({
      pickerType: 1,
      // 1 --分类，2- 部门
      pickerList: [],
      labelList: [],
      departList: [],
      open: (type) => {
        handlePicker.pickerType = type;
        handlePicker.pickerList[0] = type === 1 ? handlePicker.labelList : handlePicker.departList;
        console.log(handlePicker.pickerList);
        pickerRef.value.open();
      },
      onInit: async () => {
        const res = await common_api_system.getSysQuestionnaireLabel();
        handlePicker.labelList = res.data;
        const result = await common_api_system.getSysQuestionnaireDepartment();
        handlePicker.departList = result.data;
        console.log(handlePicker.departList);
      },
      confirm: (e) => {
        switch (handlePicker.pickerType) {
          case 1:
            nameObj.typeName = e.value[0].dictLabel;
            form.type = e.value[0].dictCode;
            break;
          case 2:
            nameObj.departName = e.value[0].dictLabel;
            form.department = e.value[0].dictValue;
            break;
        }
      }
    });
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    const afterRead = (e) => {
      const listReq = [];
      e.file.map((item) => {
        listReq.push(uploadFile(item.url));
      });
      Promise.all(listReq).then((result) => {
        let resFile = [];
        result.map((item) => {
          resFile.push({ url: item.url, message: item.ossId });
        });
        fileList.value = fileList.value.concat(resFile);
        console.log(fileList.value);
      });
    };
    const deletePic = (e) => {
      fileList.value.splice(e.index, 1);
    };
    const getAudio = (res) => {
      form.voiceId = res.ossId;
      audioFile.value = res;
      console.log("录音完成，时长:", formatDuration(res.duration));
    };
    const formatDuration = (duration) => {
      if (!duration)
        return "0秒";
      const seconds = Math.ceil(duration / 1e3);
      return `${seconds}秒`;
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    const submit = async () => {
      form.fileIds = [];
      fileList.value.length && fileList.value.map((item) => {
        form.fileIds.push(item.message);
      });
      formRef.value.validate().then(async (result) => {
        console.log("提交的表单数据:", form);
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        const res = await common_api_suggestion.insertSuggest(form);
        if ((res == null ? void 0 : res.code) === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.setStorageSync("isBack", true);
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.hideLoading();
        }
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    common_vendor.onLoad(async () => {
      common_vendor.index.$on("selectionRegion", async (data) => {
        form.districtCode = data.code;
        nameObj.areaName = data.name;
      });
      handlePicker.onInit();
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f;
      return common_vendor.e({
        a: common_vendor.o(($event) => common_vendor.unref(form).title = $event),
        b: common_vendor.p({
          placeholder: "请输入建言主题",
          modelValue: common_vendor.unref(form).title
        }),
        c: common_vendor.p({
          label: "建言主题",
          prop: "title",
          required: true
        }),
        d: common_vendor.o(($event) => handlePicker.open(1)),
        e: common_vendor.o(($event) => common_vendor.unref(nameObj).typeName = $event),
        f: common_vendor.p({
          placeholder: "请选择建言分类",
          disabled: true,
          disabledColor: "#ffffff",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(nameObj).typeName
        }),
        g: common_vendor.p({
          label: "建言分类",
          prop: "type",
          required: true
        }),
        h: common_vendor.o(($event) => common_vendor.unref(form).content = $event),
        i: common_vendor.p({
          placeholder: "请输入建言事由",
          maxlength: "100",
          showConfirmBar: false,
          modelValue: common_vendor.unref(form).content
        }),
        j: common_vendor.p({
          label: "建言事由",
          prop: "content",
          required: true
        }),
        k: common_vendor.o(($event) => common_vendor.unref(form).stratagem = $event),
        l: common_vendor.p({
          placeholder: "请输入建言措施",
          maxlength: "100",
          showConfirmBar: false,
          modelValue: common_vendor.unref(form).stratagem
        }),
        m: common_vendor.p({
          label: "建言措施",
          prop: "stratagem",
          required: true
        }),
        n: common_vendor.p({
          voiceUrl: (_a = audioFile.value) == null ? void 0 : _a.url,
          duration: (_b = audioFile.value) == null ? void 0 : _b.duration
        }),
        o: common_vendor.o(getAudio),
        p: (_c = audioFile.value) == null ? void 0 : _c.duration
      }, ((_d = audioFile.value) == null ? void 0 : _d.duration) ? {
        q: common_vendor.t(formatDuration(audioFile.value.duration))
      } : {}, {
        r: (_e = audioFile.value) == null ? void 0 : _e.text
      }, ((_f = audioFile.value) == null ? void 0 : _f.text) ? {
        s: common_vendor.t(audioFile.value.text)
      } : {}, {
        t: common_vendor.p({
          label: "语音建言"
        }),
        v: common_vendor.o(afterRead),
        w: common_vendor.o(deletePic),
        x: common_vendor.p({
          fileList: fileList.value,
          name: "1",
          multiple: true,
          maxCount: 5,
          accept: "media",
          uploadIcon: "plus",
          previewFullImage: true
        }),
        y: common_vendor.t(fileList.value.length),
        z: common_vendor.p({
          label: "附件"
        }),
        A: common_vendor.o(($event) => handlePicker.open(2)),
        B: common_vendor.o(($event) => common_vendor.unref(nameObj).departName = $event),
        C: common_vendor.p({
          placeholder: "请选择建言接收单位",
          disabled: true,
          disabledColor: "#ffffff",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(nameObj).departName
        }),
        D: common_vendor.p({
          label: "接收单位",
          prop: "department"
        }),
        E: common_vendor.o(($event) => toPage("/pages/index/area/area?scene=auth")),
        F: common_vendor.o(($event) => common_vendor.unref(nameObj).areaName = $event),
        G: common_vendor.p({
          placeholder: "请选择所属社区",
          disabled: true,
          disabledColor: "#ffffff",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(nameObj).areaName
        }),
        H: common_vendor.p({
          label: "所属社区"
        }),
        I: common_vendor.o(($event) => common_vendor.unref(form).contactName = $event),
        J: common_vendor.p({
          placeholder: "请输入提交人姓名",
          modelValue: common_vendor.unref(form).contactName
        }),
        K: common_vendor.p({
          label: "提交人"
        }),
        L: common_vendor.o(($event) => common_vendor.unref(form).idCard = $event),
        M: common_vendor.p({
          placeholder: "请输入身份证",
          modelValue: common_vendor.unref(form).idCard
        }),
        N: common_vendor.p({
          label: "身份证",
          prop: "idCard"
        }),
        O: common_vendor.o(($event) => common_vendor.unref(form).contactPhone = $event),
        P: common_vendor.p({
          type: "number",
          placeholder: "请输入联系电话",
          modelValue: common_vendor.unref(form).contactPhone
        }),
        Q: common_vendor.p({
          label: "联系电话",
          prop: "contactPhone"
        }),
        R: common_vendor.o(submit),
        S: common_vendor.p({
          type: "warning",
          text: "我要建言",
          customStyle: "margin-top: 10px"
        }),
        T: common_vendor.sr(formRef, "bccf12c2-0", {
          "k": "formRef"
        }),
        U: common_vendor.p({
          labelPosition: "left",
          model: common_vendor.unref(form),
          rules: common_vendor.unref(rules)
        }),
        V: common_vendor.sr(pickerRef, "bccf12c2-25", {
          "k": "pickerRef"
        }),
        W: common_vendor.o(handlePicker.confirm),
        X: common_vendor.p({
          columns: handlePicker.pickerList,
          keyName: "dictLabel"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bccf12c2"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/suggestion/insert-suggest.vue"]]);
wx.createPage(MiniProgramPage);
