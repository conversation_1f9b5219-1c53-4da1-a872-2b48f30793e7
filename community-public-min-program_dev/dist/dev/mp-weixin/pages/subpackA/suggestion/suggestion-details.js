"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_suggestion = require("../../../common/api/suggestion.js");
const common_utils_common = require("../../../common/utils/common.js");
const pages_activity_common = require("../../activity/common.js");
const common_utils_share = require("../../../common/utils/share.js");
const common_api_user = require("../../../common/api/user.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/api/system.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_avatar2 = common_vendor.resolveComponent("uv-avatar");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_a_popup2 = common_vendor.resolveComponent("a-popup");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_uv_icon2 + _easycom_uv_avatar2 + _easycom_uv_button2 + _easycom_a_popup2 + _easycom_uv_popup2)();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_avatar = () => "../../../uni_modules/uv-avatar/components/uv-avatar/uv-avatar.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_a_popup = () => "../../../components/a-popup/a-popup.js";
const _easycom_uv_popup = () => "../../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_uv_avatar + formComponent + _easycom_uv_button + _easycom_a_popup + _easycom_uv_popup)();
}
const formComponent = () => "./form2.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "suggestion-details",
  setup(__props) {
    const { setShare } = common_utils_share.useShare();
    const userStore = stores_store.useUserStore();
    const pageData = common_vendor.ref(null), contentCall = common_vendor.ref("已报名成功。"), popup = common_vendor.ref();
    let labelList = common_vendor.reactive({});
    const formComRef = common_vendor.ref();
    const isSubmitting = common_vendor.ref(false);
    const activeTab = common_vendor.ref("suggestion");
    const excellentList = common_vendor.ref([]);
    const selectedExcellentItem = common_vendor.ref(null);
    const excellentDetailPopup = common_vendor.ref(null);
    const currentType = common_vendor.ref("");
    const popupCustomStyle = {
      width: "700rpx",
      maxHeight: "80vh"
    };
    const bindIdNumberApi = async (idNumber) => {
      return await common_api_user.bindIdNumber({ idNumber });
    };
    const switchTab = (tab) => {
      var _a, _b, _c, _d;
      activeTab.value = tab;
      if (tab === "excellent" && excellentList.value.length === 0 && ((_b = (_a = pageData.value) == null ? void 0 : _a.questionnaire) == null ? void 0 : _b.type) === "1") {
        const questionnaireId = (_d = (_c = pageData.value) == null ? void 0 : _c.questionnaire) == null ? void 0 : _d.questionnaireId;
        if (questionnaireId) {
          getExcellentList(questionnaireId);
        }
      }
    };
    const getExcellentList = async (questionnaireId) => {
      var _a, _b;
      try {
        const id = questionnaireId || ((_b = (_a = pageData.value) == null ? void 0 : _a.questionnaire) == null ? void 0 : _b.questionnaireId);
        if (!id) {
          console.error("获取优秀建言失败: questionnaireId不存在");
          return;
        }
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const res = await common_api_suggestion.getExcellentSuggestions(id);
        console.log(res, "res");
        if ((res == null ? void 0 : res.code) === 200) {
          console.log("优秀建言数据:", res.rows);
          const processedData = res.rows.map((item) => ({
            id: item.submissionId,
            title: `${item.districtName} - ${item.userName}的建言`,
            // 组合标题
            content: item.content || "无内容",
            authorName: item.userName || "匿名",
            createTime: item.createTime || "",
            likeCount: 0,
            // 当前接口没有点赞数字段，设为0
            commentCount: item.reply || 0,
            // 使用reply字段作为评论数
            districtName: item.districtName || "",
            browseNumber: item.browseNumber || 0,
            avatar: item.avatar || "",
            adopt: item.adopt || 0,
            // 采纳状态
            submissionId: item.submissionId,
            phone: item.phone
          }));
          excellentList.value = processedData;
          common_vendor.index.hideLoading();
        } else {
          common_vendor.index.hideLoading();
          console.error("获取优秀建言失败:", res);
          common_vendor.index.showToast({
            title: (res == null ? void 0 : res.message) || "获取优秀建言失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("获取优秀建言失败:", error);
        common_vendor.index.showToast({
          title: "获取优秀建言失败",
          icon: "none"
        });
      }
    };
    const viewExcellentDetail = async (item) => {
      try {
        common_vendor.index.showLoading({
          title: "加载详情...",
          mask: true
        });
        const res = await common_api_suggestion.getLeaveWordDetail(String(item.submissionId));
        console.log("建言详情数据:", res);
        if ((res == null ? void 0 : res.code) === 200) {
          const detail = res.data;
          selectedExcellentItem.value = {
            ...item,
            ...detail,
            authorName: detail.userName || item.authorName,
            districtName: detail.districtName || item.districtName,
            createTime: detail.createTime || item.createTime,
            browseNumber: detail.browseNumber || item.browseNumber,
            content: detail.content || item.content,
            adopt: detail.adopt || item.adopt
          };
          common_vendor.index.hideLoading();
          if (excellentDetailPopup.value) {
            excellentDetailPopup.value.open();
          }
        } else {
          common_vendor.index.hideLoading();
          selectedExcellentItem.value = {
            ...item,
            adopt: item.adopt
          };
          if (excellentDetailPopup.value) {
            excellentDetailPopup.value.open();
          }
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("获取建言详情失败:", error);
        selectedExcellentItem.value = {
          ...item,
          adopt: item.adopt
        };
        if (excellentDetailPopup.value) {
          excellentDetailPopup.value.open();
        }
      }
    };
    const toUrl = (url) => {
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制，请在浏览器打开",
            icon: "none"
          });
        }
      });
    };
    const toNav = () => {
      const { longitude, latitude, location } = pageData.value.questionnaire;
      common_vendor.index.navigateTo({
        url: `/pages/map/navigation/navigation?longitude=${longitude}&latitude=${latitude}&name=${location}&address=${location}`
      });
    };
    const disabledBtn = () => {
      var _a, _b, _c;
      if (!pageData.value) {
        return false;
      }
      return ((_a = pageData.value) == null ? void 0 : _a.participate) || ((_c = (_b = pageData.value) == null ? void 0 : _b.questionnaire) == null ? void 0 : _c.status) !== "0" || isSubmitting.value;
    };
    const getPageList = async (id) => {
      var _a, _b, _c, _d, _e, _f, _g, _h;
      const res = await common_api_suggestion.getSuggestInfo(id);
      if ((res == null ? void 0 : res.code) === 200) {
        console.log(res.data);
        pageData.value = res.data;
        pageData.value.questionnaire.startTime = common_utils_common.formatDate(new Date((_b = (_a = pageData.value) == null ? void 0 : _a.questionnaire) == null ? void 0 : _b.startTime), 3);
        pageData.value.questionnaire.endTime = common_utils_common.formatDate(new Date((_d = (_c = pageData.value) == null ? void 0 : _c.questionnaire) == null ? void 0 : _d.endTime), 3);
        if (activeTab.value === "excellent" && ((_f = (_e = pageData.value) == null ? void 0 : _e.questionnaire) == null ? void 0 : _f.type) !== "1") {
          activeTab.value = "suggestion";
        }
        if (currentType.value === "1" && activeTab.value === "excellent" && ((_h = (_g = pageData.value) == null ? void 0 : _g.questionnaire) == null ? void 0 : _h.type) === "1") {
          getExcellentList(id);
        }
      } else {
        throw console.log(res);
      }
    };
    const submitForm = async () => {
      var _a, _b, _c;
      const formVal = await formComRef.value.submit();
      if (formVal.type) {
        const target = [];
        (_a = pageData.value) == null ? void 0 : _a.topicList.map((item) => {
          const { topicId, type, options } = item;
          let optionId = null, multipleOP = null, defaultValue = null, fileNames = null, fileDuration = null;
          if (type === "1") {
            const singleOP = options.find((info) => {
              return info.score === formVal[topicId];
            });
            optionId = singleOP.optionId;
          } else if (type === "2") {
            multipleOP = [];
            options.map((info) => {
              if (formVal[topicId].includes(info.score)) {
                multipleOP.push(info.optionId);
              }
            });
          } else if (type === "6") {
            const { fileName, duration, url } = JSON.parse(formVal[topicId]);
            fileNames = fileName;
            fileDuration = duration;
            defaultValue = url;
          } else {
            defaultValue = formVal[topicId];
          }
          target.push({ topicId, optionId, options: multipleOP, defaultValue, fileName: fileNames, fileDuration });
        });
        console.log(target);
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        const res = await common_api_suggestion.addSuggest({ topicList: target, questionnaireId: (_b = pageData.value) == null ? void 0 : _b.questionnaire.questionnaireId });
        if ((res == null ? void 0 : res.code) === 200) {
          contentCall.value = "参与成功。";
          popup.value.open();
          common_vendor.index.hideLoading();
          getPageList((_c = pageData.value) == null ? void 0 : _c.questionnaire.questionnaireId);
          isSubmitting.value = false;
        } else {
          common_vendor.index.hideLoading();
          isSubmitting.value = false;
          throw console.log(res);
        }
      } else {
        isSubmitting.value = false;
      }
    };
    const submit = async () => {
      if (disabledBtn()) {
        common_vendor.index.showToast({
          title: "当前状态不允许参与",
          icon: "none"
        });
        return;
      }
      isSubmitting.value = true;
      if (!userStore.userInfo) {
        try {
          const { getUserInfo } = await "../../../common/api/user.js";
          await getUserInfo();
        } catch (error) {
        }
        isSubmitting.value = false;
        return;
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "",
          content: "您还未进行实名认证，是否去认证？",
          confirmText: "去认证",
          cancelText: "取消",
          confirmColor: "#2BBC4A",
          success: (modalRes) => {
            if (modalRes.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        isSubmitting.value = false;
        return;
      }
      const userInfo = userStore.userInfo;
      console.log("idNumber字段:", userInfo.idNumber);
      if (!userInfo.idNumber) {
        console.log("没有身份证号，显示弹窗");
        common_vendor.index.showModal({
          title: "身份证认证",
          content: "",
          editable: true,
          placeholderText: "请输入18位身份证号码",
          confirmText: "确认",
          cancelText: "取消",
          success: async (res) => {
            if (res.confirm && res.content) {
              const idNumber = res.content.trim();
              const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
              if (!idCardRegex.test(idNumber)) {
                common_vendor.index.showToast({
                  title: "请输入正确的身份证号码",
                  icon: "none"
                });
                isSubmitting.value = false;
                return;
              }
              try {
                common_vendor.index.showLoading({ title: "绑定中..." });
                const res2 = await bindIdNumberApi(idNumber);
                common_vendor.index.hideLoading();
                if ((res2 == null ? void 0 : res2.code) === 200) {
                  const userInfo2 = userStore.userInfo;
                  if (userInfo2) {
                    userInfo2.idNumber = idNumber;
                    userStore.setUser(userInfo2);
                    common_vendor.index.setStorageSync("userInfo", userInfo2);
                  }
                  common_vendor.index.showToast({
                    title: "身份证绑定成功",
                    icon: "success"
                  });
                  setTimeout(() => {
                    submitForm();
                  }, 1500);
                } else {
                  common_vendor.index.showToast({
                    title: (res2 == null ? void 0 : res2.message) || "绑定失败，请重试",
                    icon: "none"
                  });
                  isSubmitting.value = false;
                }
              } catch (error) {
                common_vendor.index.hideLoading();
                console.error("身份证绑定失败:", error);
                common_vendor.index.showToast({
                  title: "绑定失败，请重试",
                  icon: "none"
                });
                isSubmitting.value = false;
              }
            } else {
              isSubmitting.value = false;
            }
          }
        });
        return;
      }
      console.log("身份证号存在，继续提交:", userInfo.idNumber);
      try {
        await submitForm();
      } catch (error) {
        console.error("提交失败:", error);
        isSubmitting.value = false;
      }
    };
    common_vendor.onLoad(async (option) => {
      if (option == null ? void 0 : option.id) {
        getPageList(String(option.id));
      }
      currentType.value = (option == null ? void 0 : option.type) || "";
      if ((option == null ? void 0 : option.type) === "1") {
        activeTab.value = "excellent";
      }
      const audienceData = await pages_activity_common.getSysAudience();
      Object.assign(labelList, audienceData);
    });
    common_vendor.onShow(() => {
      var _a, _b, _c, _d;
      setShare({
        weapp: {
          title: (_b = (_a = pageData.value) == null ? void 0 : _a.questionnaire) == null ? void 0 : _b.title,
          desc: "",
          imageUrl: (_d = (_c = pageData.value) == null ? void 0 : _c.questionnaire) == null ? void 0 : _d.coverImages
        }
      });
    });
    common_vendor.onHide(() => {
      common_vendor.index.setStorageSync("isBack", true);
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _A, _B, _C, _D, _E, _F, _G, _H, _I, _J, _K, _L, _M, _N, _O, _P, _Q, _R, _S, _T, _U, _V, _W, _X, _Y, _Z, __, _$, _aa, _ba, _ca, _da, _ea, _fa, _ga, _ha, _ia, _ja, _ka, _la, _ma, _na, _oa, _pa, _qa, _ra, _sa, _ta, _ua, _va, _wa, _xa, _ya, _za, _Aa, _Ba, _Ca, _Da, _Ea;
      return common_vendor.e({
        a: common_vendor.t((_b = (_a = pageData.value) == null ? void 0 : _a.questionnaire) == null ? void 0 : _b.title),
        b: ((_d = (_c = pageData.value) == null ? void 0 : _c.questionnaire) == null ? void 0 : _d.status) != "2"
      }, ((_f = (_e = pageData.value) == null ? void 0 : _e.questionnaire) == null ? void 0 : _f.status) != "2" ? {
        c: common_vendor.p({
          name: "map-fill",
          color: "#fafafa",
          size: "14"
        }),
        d: common_vendor.t((_h = (_g = pageData.value) == null ? void 0 : _g.questionnaire) == null ? void 0 : _h.location),
        e: common_vendor.o(toNav),
        f: common_vendor.p({
          name: "share-square",
          color: "#ffffff",
          size: "20"
        })
      } : {}, {
        g: common_vendor.p({
          src: (_j = (_i = pageData.value) == null ? void 0 : _i.questionnaire) == null ? void 0 : _j.avatar
        }),
        h: common_vendor.t((_l = (_k = pageData.value) == null ? void 0 : _k.questionnaire) == null ? void 0 : _l.contactName),
        i: common_vendor.t((_n = (_m = pageData.value) == null ? void 0 : _m.questionnaire) == null ? void 0 : _n.createTime),
        j: ((_p = (_o = pageData.value) == null ? void 0 : _o.questionnaire) == null ? void 0 : _p.status) == "2"
      }, ((_r = (_q = pageData.value) == null ? void 0 : _q.questionnaire) == null ? void 0 : _r.status) == "2" ? {
        k: common_vendor.p({
          name: "share-square",
          color: "#ffffff",
          size: "24"
        })
      } : {}, {
        l: common_vendor.t((_t = (_s = pageData.value) == null ? void 0 : _s.questionnaire) == null ? void 0 : _t.registeredCount),
        m: ((_v = (_u = pageData.value) == null ? void 0 : _u.questionnaire) == null ? void 0 : _v.status) != "2",
        n: ((_x = (_w = pageData.value) == null ? void 0 : _w.questionnaire) == null ? void 0 : _x.status) != "2"
      }, ((_z = (_y = pageData.value) == null ? void 0 : _y.questionnaire) == null ? void 0 : _z.status) != "2" ? common_vendor.e({
        o: common_vendor.t(((_B = (_A = pageData.value) == null ? void 0 : _A.questionnaire) == null ? void 0 : _B.startTime) + "至" + ((_D = (_C = pageData.value) == null ? void 0 : _C.questionnaire) == null ? void 0 : _D.endTime)),
        p: common_vendor.t((_F = (_E = pageData.value) == null ? void 0 : _E.questionnaire) == null ? void 0 : _F.location),
        q: common_vendor.t(common_vendor.unref(labelList)[(_H = (_G = pageData.value) == null ? void 0 : _G.questionnaire) == null ? void 0 : _H.targetAudience]),
        r: (_J = (_I = pageData.value) == null ? void 0 : _I.questionnaire) == null ? void 0 : _J.content,
        s: common_vendor.t(((_L = (_K = pageData.value) == null ? void 0 : _K.questionnaire) == null ? void 0 : _L.type) == "1" ? "我要建言" : ((_N = (_M = pageData.value) == null ? void 0 : _M.questionnaire) == null ? void 0 : _N.type) == "2" ? "投票征集" : "问卷征集"),
        t: activeTab.value === "suggestion" ? 1 : "",
        v: common_vendor.o(($event) => switchTab("suggestion")),
        w: ((_P = (_O = pageData.value) == null ? void 0 : _O.questionnaire) == null ? void 0 : _P.type) === "1"
      }, ((_R = (_Q = pageData.value) == null ? void 0 : _Q.questionnaire) == null ? void 0 : _R.type) === "1" ? {
        x: activeTab.value === "excellent" ? 1 : "",
        y: common_vendor.o(($event) => switchTab("excellent"))
      } : {}, {
        z: ((_T = (_S = pageData.value) == null ? void 0 : _S.questionnaire) == null ? void 0 : _T.type) !== "1" ? 1 : "",
        A: activeTab.value === "suggestion"
      }, activeTab.value === "suggestion" ? common_vendor.e({
        B: (_U = pageData.value) == null ? void 0 : _U.topicList
      }, ((_V = pageData.value) == null ? void 0 : _V.topicList) ? {
        C: common_vendor.sr(formComRef, "0000e196-4", {
          "k": "formComRef"
        }),
        D: common_vendor.p({
          disabled: disabledBtn(),
          type: (_X = (_W = pageData.value) == null ? void 0 : _W.questionnaire) == null ? void 0 : _X.type,
          formList: (_Y = pageData.value) == null ? void 0 : _Y.topicList
        })
      } : {}) : {}, {
        E: activeTab.value === "excellent" && ((__ = (_Z = pageData.value) == null ? void 0 : _Z.questionnaire) == null ? void 0 : __.type) === "1"
      }, activeTab.value === "excellent" && ((_aa = (_$ = pageData.value) == null ? void 0 : _$.questionnaire) == null ? void 0 : _aa.type) === "1" ? common_vendor.e({
        F: excellentList.value.length === 0
      }, excellentList.value.length === 0 ? {} : {
        G: common_vendor.f(excellentList.value, (item, index, i0) => {
          return common_vendor.e({
            a: "0000e196-5-" + i0,
            b: common_vendor.p({
              src: item.avatar,
              size: "60rpx"
            }),
            c: common_vendor.t(item.authorName),
            d: common_vendor.t(item.districtName),
            e: common_vendor.t(item.createTime),
            f: item.adopt
          }, item.adopt ? {} : {}, {
            g: common_vendor.t(item.content),
            h: "0000e196-6-" + i0,
            i: common_vendor.t(item.browseNumber || 0),
            j: item.commentCount > 0
          }, item.commentCount > 0 ? {
            k: common_vendor.t(item.commentCount)
          } : {}, {
            l: "0000e196-7-" + i0,
            m: index,
            n: common_vendor.o(($event) => viewExcellentDetail(item), index)
          });
        }),
        H: common_vendor.p({
          name: "eye"
        }),
        I: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#999"
        })
      }) : {}) : {}, {
        J: ((_ca = (_ba = pageData.value) == null ? void 0 : _ba.questionnaire) == null ? void 0 : _ca.status) == "2"
      }, ((_ea = (_da = pageData.value) == null ? void 0 : _da.questionnaire) == null ? void 0 : _ea.status) == "2" ? {
        K: common_vendor.t((_ga = (_fa = pageData.value) == null ? void 0 : _fa.questionnaire) == null ? void 0 : _ga.content),
        L: common_vendor.t((_ia = (_ha = pageData.value) == null ? void 0 : _ha.questionnaire) == null ? void 0 : _ia.stratagem),
        M: common_vendor.f((_ka = (_ja = pageData.value) == null ? void 0 : _ja.questionnaire) == null ? void 0 : _ka.ossVoList, (item, i, i0) => {
          return {
            a: common_vendor.t(item.url),
            b: common_vendor.o(($event) => toUrl(item.url), i),
            c: i
          };
        })
      } : {}, {
        N: activeTab.value !== "excellent"
      }, activeTab.value !== "excellent" ? {
        O: common_vendor.t((_ma = (_la = pageData.value) == null ? void 0 : _la.user) == null ? void 0 : _ma.districtName),
        P: common_vendor.t(((_oa = (_na = pageData.value) == null ? void 0 : _na.user) == null ? void 0 : _oa.submission) || ((_qa = (_pa = pageData.value) == null ? void 0 : _pa.questionnaire) == null ? void 0 : _qa.createTime)),
        Q: common_vendor.t((_sa = (_ra = pageData.value) == null ? void 0 : _ra.user) == null ? void 0 : _sa.realName),
        R: common_vendor.t((_ua = (_ta = pageData.value) == null ? void 0 : _ta.user) == null ? void 0 : _ua.idNumber),
        S: common_vendor.t((_wa = (_va = pageData.value) == null ? void 0 : _va.user) == null ? void 0 : _wa.phone)
      } : {}, {
        T: ((_ya = (_xa = pageData.value) == null ? void 0 : _xa.questionnaire) == null ? void 0 : _ya.status) != "2" && activeTab.value === "suggestion"
      }, ((_Aa = (_za = pageData.value) == null ? void 0 : _za.questionnaire) == null ? void 0 : _Aa.status) != "2" && activeTab.value === "suggestion" ? {
        U: common_vendor.o(submit),
        V: common_vendor.p({
          type: "warning",
          text: isSubmitting.value ? "提交中..." : ((_Ca = (_Ba = pageData.value) == null ? void 0 : _Ba.questionnaire) == null ? void 0 : _Ca.type) == "1" ? "提交留言" : ((_Ea = (_Da = pageData.value) == null ? void 0 : _Da.questionnaire) == null ? void 0 : _Ea.type) == "2" ? "确认投票" : "提交问卷",
          disabled: disabledBtn(),
          customStyle: "margin-top: 10px;margin-bottom: 10px;"
        })
      } : {}, {
        W: common_vendor.sr(popup, "0000e196-9", {
          "k": "popup"
        }),
        X: common_vendor.p({
          content: contentCall.value,
          confimText: "已知晓",
          cancelText: ""
        }),
        Y: selectedExcellentItem.value
      }, selectedExcellentItem.value ? common_vendor.e({
        Z: common_vendor.p({
          src: selectedExcellentItem.value.avatar,
          size: "80rpx"
        }),
        aa: common_vendor.t(selectedExcellentItem.value.authorName),
        ab: common_vendor.t(selectedExcellentItem.value.districtName),
        ac: selectedExcellentItem.value.adopt
      }, selectedExcellentItem.value.adopt ? {} : {}, {
        ad: common_vendor.t(selectedExcellentItem.value.createTime),
        ae: common_vendor.t(selectedExcellentItem.value.browseNumber || 0),
        af: selectedExcellentItem.value.commentCount > 0
      }, selectedExcellentItem.value.commentCount > 0 ? {
        ag: common_vendor.t(selectedExcellentItem.value.commentCount)
      } : {}, {
        ah: common_vendor.t(selectedExcellentItem.value.content)
      }) : {}, {
        ai: common_vendor.sr(excellentDetailPopup, "0000e196-10", {
          "k": "excellentDetailPopup"
        }),
        aj: common_vendor.p({
          mode: "center",
          closeable: true,
          round: "20",
          customStyle: popupCustomStyle
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0000e196"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/suggestion/suggestion-details.vue"]]);
wx.createPage(MiniProgramPage);
