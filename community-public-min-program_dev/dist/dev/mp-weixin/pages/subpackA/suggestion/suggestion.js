"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_suggestion = require("../../../common/api/suggestion.js");
const common_api_index = require("../../../common/api/index.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_tabs2 = common_vendor.resolveComponent("uv-tabs");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_button2 + _easycom_uv_tabs2 + _easycom_z_paging2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_tabs = () => "../../../uni_modules/uv-tabs/components/uv-tabs/uv-tabs.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_tabs + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "suggestion",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const areaStore = stores_store.useAreaStore();
    const isShow = common_vendor.ref(false);
    const tabs = Object.freeze([
      { name: "全部", type: "all" },
      { name: "征集中", type: "ing" },
      { name: "已结束", type: "end" },
      { name: "我参与的", type: "participate" },
      { name: "我发布的", type: "me" }
    ]);
    const searchObj = common_vendor.reactive({
      wishType: 0,
      queryType: "all",
      districtCode: areaStore.areaCode,
      pageNum: 1
    });
    const scrollHeight = common_vendor.ref();
    const dataList = common_vendor.ref([]), defaultImg = common_vendor.ref();
    const paging = common_vendor.ref();
    common_vendor.watch(
      () => areaStore.areaCode,
      (newVal, oldVal) => {
        if (newVal != oldVal) {
          searchObj.pageNum = 1;
          searchObj.districtCode = newVal;
          getPageList();
        }
      }
    );
    const handleStatus = (status) => {
      switch (status) {
        case "-1":
          return { text: "未开始", color: "#0f86d0" };
        case "0":
          return { text: "进行中", color: "#04B578" };
        case "1":
          return { text: "已结束", color: "#999999" };
        default:
          return { text: "未知状态", color: "#999999" };
      }
    };
    const formatDateString = (dateString) => {
      if (!dateString)
        return "";
      if (dateString.includes(" ")) {
        return dateString.split(" ")[0];
      }
      return dateString;
    };
    const handleItemClick = (item) => {
      if (item.status === "2") {
        toPage("/pages/subpackA/suggestion/suggestion-info", item.questionnaireId);
      } else {
        toPage("/pages/subpackA/suggestion/suggestion-details", item.questionnaireId);
      }
    };
    function toPage(url, id = "") {
      var _a;
      if (!common_vendor.index.getStorageSync("token")) {
        goLogin();
        return;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0 && !id) {
        goAuth();
        return;
      }
      common_vendor.index.navigateTo({
        url: url + `?id=${id}`
      });
    }
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight;
    };
    const changeTabs = (e) => {
      searchObj.queryType = e.type;
      searchObj.pageNum = 1;
      getPageList();
    };
    const queryList = (pageNo, pageSize) => {
      console.log(pageNo, pageSize);
      searchObj.pageNum = pageNo;
      getPageList();
    };
    const getPageList = async () => {
      console.log(searchObj);
      const res = await common_api_suggestion.getPageSuggest({
        ...searchObj,
        pageSize: 10
      });
      if ((res == null ? void 0 : res.code) === 200) {
        res.rows.map((item) => {
          if (item.status !== "2") {
            item.content = removeAllTags(item.content);
            item.content = item.content.replaceAll("&nbsp;", "");
          }
        });
        paging.value.complete(res.rows);
      } else {
        throw console.log(res);
      }
    };
    function removeAllTags(htmlString) {
      return htmlString.replace(/<[^>]+>/g, "");
    }
    const goAuth = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未进行实名认证，是否去认证？",
        confirmText: "去认证",
        cancelText: "取消",
        confirmColor: "#2BBC4A",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/my/auth/auth"
            });
          }
        }
      });
    };
    const goLogin = () => {
      common_vendor.index.showModal({
        title: "",
        content: "您还未登录，是否去登录？",
        confirmText: "去登录",
        cancelText: "取消",
        confirmColor: "#07C160",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.reLaunch({
              url: "/pages/auth/login/login"
            });
          }
        }
      });
    };
    common_vendor.onLoad(async () => {
      isShow.value = true;
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        return;
      }
      await initPageData();
    });
    const initPageData = async () => {
      try {
        let userInfo = userStore.userInfo;
        console.log("userStore.userInfo:", userInfo);
        if (!userInfo) {
          userInfo = common_vendor.index.getStorageSync("userInfo");
          console.log("storage userInfo:", userInfo);
          if (userInfo) {
            userStore.setUser(userInfo);
          } else {
            const { getUserInfo } = await "../../../common/api/user.js";
            const res2 = await getUserInfo();
            console.log("API getUserInfo res:", res2);
            if ((res2 == null ? void 0 : res2.code) === 200 && res2.data) {
              userInfo = res2.data;
              userStore.setUser(userInfo);
              common_vendor.index.setStorageSync("userInfo", userInfo);
            }
          }
        }
        console.log("最终 userInfo:", userInfo);
        console.log("认证状态 authentication:", userInfo == null ? void 0 : userInfo.authentication);
        if (userInfo && (userInfo.authentication === 0 || userInfo.authentication === "0" || !userInfo.authentication)) {
          console.log("需要认证，显示认证弹框");
          goAuth();
          return;
        }
        getScreenHeight();
        getPageList();
        const res = await common_api_index.getConfig({ code: "applet_jy_default" });
        if (res == null ? void 0 : res.data) {
          defaultImg.value = res.data.configUrl;
        }
      } catch (error) {
        console.error("初始化页面数据失败:", error);
      }
    };
    common_vendor.onShow(() => {
      if (common_vendor.index.getStorageSync("isBack")) {
        searchObj.pageNum = 1;
        getPageList();
        common_vendor.index.setStorageSync("isBack", false);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => toPage("/pages/subpackA/suggestion/insert-suggest")),
        b: common_vendor.t(common_vendor.unref(areaStore).areaName),
        c: common_vendor.o(($event) => toPage("/pages/index/area/area")),
        d: common_vendor.o(changeTabs),
        e: common_vendor.p({
          list: common_vendor.unref(tabs),
          lineColor: "#FF9F18",
          activeStyle: {
            color: "#333333",
            fontWeight: "bold",
            transform: "scale(1.05)"
          },
          inactiveStyle: {
            color: "#999999",
            transform: "scale(1)"
          }
        }),
        f: common_vendor.f(dataList.value, (item, i, i0) => {
          return common_vendor.e({
            a: item.status !== "2" ? item.coverImage : defaultImg.value,
            b: item.label
          }, item.label ? {
            c: common_vendor.t(item.label)
          } : {}, {
            d: item.status !== "2"
          }, item.status !== "2" ? {
            e: common_vendor.t(handleStatus(item.status).text),
            f: handleStatus(item.status).color
          } : {}, {
            g: item.status !== "2"
          }, item.status !== "2" ? {
            h: common_vendor.t(item.registeredCount)
          } : {}, {
            i: item.status !== "2"
          }, item.status !== "2" ? {
            j: common_vendor.t(formatDateString(item.startTime) + " 至 " + formatDateString(item.endTime))
          } : {}, {
            k: item.status === "2"
          }, item.status === "2" ? {
            l: common_vendor.t(formatDateString(item.startTime))
          } : {}, {
            m: common_vendor.t(item.title),
            n: common_vendor.t(item.content),
            o: common_vendor.o(($event) => handleItemClick(item), i),
            p: i
          });
        }),
        g: common_vendor.sr(paging, "0ce8bb84-0", {
          "k": "paging"
        }),
        h: common_vendor.o(queryList),
        i: common_vendor.o(($event) => dataList.value = $event),
        j: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0ce8bb84"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/suggestion/suggestion.vue"]]);
wx.createPage(MiniProgramPage);
