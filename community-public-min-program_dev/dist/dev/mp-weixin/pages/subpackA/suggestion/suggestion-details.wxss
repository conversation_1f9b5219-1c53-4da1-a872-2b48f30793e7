/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.flex_row.data-v-0000e196 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.flex_warp.data-v-0000e196 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
view.data-v-0000e196 .uv-button--primary {
  background-color: #999999 !important;
}
.mg.data-v-0000e196 {
  margin: 8rpx 0;
}
.location.data-v-0000e196 {
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
}
.top.data-v-0000e196 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #FFFFFF;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  padding: 32rpx 32rpx 52rpx 32rpx;
}
.top .top-text.data-v-0000e196 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
}
.top .contact_tip_count.data-v-0000e196 {
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 40rpx;
  text-decoration: underline;
}
.top .share-btn.data-v-0000e196 {
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  padding: 0;
  font-size: 22rpx;
}
.top .share-btn.data-v-0000e196::after {
  border: none;
}
.content_Body.data-v-0000e196 {
  background: #ffffff;
  padding-top: 28rpx;
  margin-top: -26rpx;
  z-index: 999 !important;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view.data-v-0000e196 {
  padding: 0 32rpx 20rpx 32rpx;
  box-sizing: border-box;
  background: #ffffff;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content_view .range_view.data-v-0000e196 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 16rpx;
}
.content_view .range_view .left.data-v-0000e196 {
  width: 332rpx;
  background: rgba(22, 119, 255, 0.2);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.content_view .range_view .left .range_view_content.data-v-0000e196 {
  margin-left: 12rpx;
}
.content_view .range_view .right.data-v-0000e196 {
  width: 332rpx;
  background: rgba(249, 58, 74, 0.2);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.content_view .range_view .right .range_view_content.data-v-0000e196 {
  margin-left: 12rpx;
}
.content_view .range_view .range_d.data-v-0000e196 {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
}
.content_view .time_backg.data-v-0000e196 {
  background: rgba(255, 159, 24, 0.2);
  border-radius: 8rpx;
}
.content_view .time_backg .time_bg.data-v-0000e196 {
  width: 180rpx;
  height: 72rpx;
  background: url("https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/40a43b210ab84935ad4a30c1a0842dc4.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.content_view .time_backg .time_bg.data-v-0000e196::after {
  content: "征集时间";
  line-height: 72rpx;
  font-family: YouSheBiaoTiHei;
  font-size: 36rpx;
  font-weight: 450;
  color: #FFFFFF;
  text-align: center;
  font-style: italic;
}
.content_view .time_backg .time_desc.data-v-0000e196 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  padding: 10rpx 14rpx;
}
.content_view .title_flex.data-v-0000e196 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.content_view .title.data-v-0000e196 {
  width: 88%;
  font-family: AppleColorEmoji;
  font-size: 32rpx;
  font-weight: 550;
  color: #222222;
  text-align: left;
  font-style: normal;
}
.content_view .label.data-v-0000e196 {
  width: 12%;
  font-weight: 400;
  font-size: 20rpx;
  color: #FFFFFF;
  text-align: center;
  border-radius: 4rpx;
  line-height: 32rpx;
  padding: 4rpx 8rpx;
}
.name.data-v-0000e196 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  margin-left: 20rpx;
}
.contact_tip.data-v-0000e196 {
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
}
.contact_tip image.data-v-0000e196 {
  width: 40px;
  height: 40px;
}
.contact_tip .contact_tip_box.data-v-0000e196 {
  flex: 1;
  display: flex;
}
.tip.data-v-0000e196 {
  background: #FFFFFF;
  padding: 20rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
}
.tip .tip_title.data-v-0000e196 {
  font-weight: 550;
  font-size: 28rpx;
  color: #222222;
}
.tip view.data-v-0000e196 {
  margin: 16rpx 0;
}
.tip-box.data-v-0000e196 {
  padding: 0 32rpx;
  background: #ffffff;
}
.tip-box .suggestion-box-content.data-v-0000e196 {
  margin-bottom: 20rpx;
}
.tip-box .suggestion-box-content .tip_content.data-v-0000e196 {
  color: #6d6a6a;
  border-radius: 4rpx;
  border: 2rpx solid #dadbde;
  padding: 10rpx 20rpx;
  margin-top: 10rpx;
}
.form_content.data-v-0000e196 .uv-form-item__body {
  display: flex;
  flex-direction: column !important;
}
.form_content.data-v-0000e196 .uv-form-item__body__left {
  width: 100% !important;
  margin-bottom: 6rpx !important;
}
.form_content.data-v-0000e196 .uv-form-item__body__left__content__label {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333333 !important;
}
.form_content.data-v-0000e196 .uv-form-item__body__right__message {
  margin-left: 0 !important;
  line-height: none !important;
}
.form_content.data-v-0000e196 .uv-radio__label-wrap {
  width: 100% !important;
}
.form_content.data-v-0000e196 .uv-radio__label-wrap text {
  font-weight: 400;
  font-size: 28rpx;
}
.form_content.data-v-0000e196 .uv-radio {
  background: #F5F5F5;
  border-radius: 8rpx;
  width: 100%;
  padding: 8rpx;
  margin: 8px 8px 0 0 !important;
}
.form_content.data-v-0000e196 .uv-checkbox {
  margin: 8px 8px 0 0 !important;
  background: #F5F5F5;
  border-radius: 8rpx;
  width: 100%;
  padding: 8rpx;
}
.data-v-0000e196 .uv-link {
  word-wrap: break-all;
}
.tab-container.data-v-0000e196 {
  padding: 20rpx 32rpx 0;
}
.tab-container .tab-list.data-v-0000e196 {
  display: flex;
}
.tab-container .tab-list .tab-item.data-v-0000e196 {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}
.tab-container .tab-list .tab-item.active.data-v-0000e196 {
  color: #FF9F18;
  font-weight: 500;
}
.tab-container .tab-list .tab-item.active.data-v-0000e196::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #FF9F18;
  border-radius: 2rpx;
}
.tab-container .tab-list.single-tab.data-v-0000e196 {
  justify-content: flex-start;
}
.tab-container .tab-list.single-tab .tab-item.data-v-0000e196 {
  flex: none;
  text-align: left;
  padding: 20rpx 0;
  min-width: auto;
}
.tab-container .tab-list.single-tab .tab-item.active.data-v-0000e196::after {
  left: 30rpx;
  transform: none;
}
.tab-content.data-v-0000e196 {
  padding: 20rpx 0;
}
.excellent-list .empty-state.data-v-0000e196 {
  text-align: center;
  padding: 80rpx 0;
}
.excellent-list .empty-state .empty-text.data-v-0000e196 {
  color: #999999;
  font-size: 28rpx;
}
.excellent-list .excellent-item.data-v-0000e196 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}
.excellent-list .excellent-item.data-v-0000e196:last-child {
  margin-bottom: 0;
}
.excellent-list .excellent-item .excellent-header.data-v-0000e196 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.excellent-list .excellent-item .excellent-header .author-info.data-v-0000e196 {
  display: flex;
  align-items: center;
  flex: 1;
}
.excellent-list .excellent-item .excellent-header .author-info .author-details.data-v-0000e196 {
  margin-left: 16rpx;
}
.excellent-list .excellent-item .excellent-header .author-info .author-details .author-name.data-v-0000e196 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.3;
}
.excellent-list .excellent-item .excellent-header .author-info .author-details .author-district.data-v-0000e196 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}
.excellent-list .excellent-item .excellent-header .excellent-meta.data-v-0000e196 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.excellent-list .excellent-item .excellent-header .excellent-meta .excellent-date.data-v-0000e196 {
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
}
.excellent-list .excellent-item .excellent-header .excellent-meta .adopt-badge.data-v-0000e196 {
  background: #FF9F18;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
}
.excellent-list .excellent-item .excellent-content.data-v-0000e196 {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.excellent-list .excellent-item .excellent-footer.data-v-0000e196 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.excellent-list .excellent-item .excellent-footer .excellent-stats.data-v-0000e196 {
  display: flex;
  gap: 20rpx;
}
.excellent-list .excellent-item .excellent-footer .excellent-stats .stats-text.data-v-0000e196 {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}
.excellent-list .excellent-item .excellent-footer .view-more.data-v-0000e196 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.excellent-list .excellent-item .excellent-footer .view-more .view-more-text.data-v-0000e196 {
  font-size: 24rpx;
  color: #FF9F18;
  font-weight: 500;
}
.excellent-detail-popup.data-v-0000e196 {
  padding: 40rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
}
.detail-header.data-v-0000e196 {
  text-align: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.detail-title.data-v-0000e196 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}
.detail-title.data-v-0000e196::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #FF9F18 0%, #FFA726 100%);
  border-radius: 2rpx;
}
.detail-content.data-v-0000e196 {
  margin-bottom: 30rpx;
}
.user-info-section.data-v-0000e196 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #fef7f0 0%, #fff1e6 100%);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #FF9F18;
}
.user-avatar-area.data-v-0000e196 {
  margin-right: 20rpx;
}
.user-avatar-area .uv-avatar.data-v-0000e196 {
  border: 2rpx solid #FF9F18;
  border-radius: 50%;
}
.user-details-area.data-v-0000e196 {
  flex: 1;
}
.user-name.data-v-0000e196 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.user-district.data-v-0000e196 {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: inline-block;
}
.adopt-status.data-v-0000e196 {
  margin-left: 10rpx;
}
.adopt-text.data-v-0000e196 {
  font-size: 20rpx;
  color: #fff;
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
}
.info-section.data-v-0000e196 {
  margin-bottom: 20rpx;
}
.info-row.data-v-0000e196 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}
.info-label.data-v-0000e196 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.info-value.data-v-0000e196 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.content-section.data-v-0000e196 {
  padding: 20rpx;
  background: #fff;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.content-label.data-v-0000e196 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.content-text.data-v-0000e196 {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.detail-footer.data-v-0000e196 {
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
  text-align: center;
}