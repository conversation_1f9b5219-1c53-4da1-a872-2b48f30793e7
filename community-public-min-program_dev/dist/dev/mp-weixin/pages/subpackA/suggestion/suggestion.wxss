/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.overflow-hidden.data-v-0ce8bb84 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 40rpx;
  padding: 0 16rpx;
  box-sizing: border-box;
}
.top.data-v-0ce8bb84 {
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  padding: 24rpx 32rpx 60rpx 32rpx;
}
.top image.data-v-0ce8bb84 {
  width: 686rpx;
  height: 320rpx;
}
.top .title.data-v-0ce8bb84 {
  display: flex;
  flex-direction: column;
}
.top .title view.data-v-0ce8bb84 {
  margin: 4rpx 0;
}
.top .title .button.data-v-0ce8bb84 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.top .title .button text.data-v-0ce8bb84 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 48rpx;
  color: #FFFFFF;
}
.top .title .button.data-v-0ce8bb84 .uv-button--info {
  font-weight: 400;
  font-size: 28rpx;
  color: #FF9F18;
  line-height: 44rpx;
  padding: 8rpx 24rpx;
  height: 60rpx !important;
  box-sizing: border-box;
  border-radius: 8rpx !important;
}
.top .title .top-location-box.data-v-0ce8bb84 {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
}
.top .title .top-location-box image.data-v-0ce8bb84:first-of-type {
  width: 26rpx;
  height: 31rpx;
  margin-right: 12rpx;
}
.top .title .top-location-box image.data-v-0ce8bb84:last-of-type {
  width: 25rpx;
  height: 21rpx;
  margin-left: 22rpx;
}
.content_view.data-v-0ce8bb84 {
  margin-top: -26rpx;
  padding: 28rpx 32rpx;
  background: #F5F5F5;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view .tab_content.data-v-0ce8bb84 {
  width: 100%;
}
.content_view .tab_content .content_bg.data-v-0ce8bb84 {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
  padding-bottom: 16rpx;
  margin-bottom: 24rpx;
}
.content_view .tab_content .img_bg.data-v-0ce8bb84 {
  position: relative;
}
.content_view .tab_content .img_bg image.data-v-0ce8bb84 {
  width: 100%;
  height: 281rpx;
  display: block;
}
.content_view .tab_content .img_bg .left_top.data-v-0ce8bb84 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  font-size: 24rpx;
  background: #FF9F18;
  color: #FFFFFF;
  border-radius: 4rpx;
  padding: 4rpx 6rpx;
}
.content_view .tab_content .img_bg .right_top.data-v-0ce8bb84 {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  border-radius: 4rpx;
  padding: 4rpx 6rpx;
}
.content_view .tab_content .img_bg .left_bottom.data-v-0ce8bb84 {
  width: 100%;
  position: absolute;
  bottom: 0;
  padding: 0 10rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  background: #000000;
  opacity: 0.5;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content_view .tab_content .img_bg .left_bottom text.data-v-0ce8bb84 {
  display: block;
}
.content_view .tab_content .title.data-v-0ce8bb84 {
  padding: 16rpx 16rpx 0;
  box-sizing: border-box;
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 44rpx;
}