"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_suggestion = require("../../../common/api/suggestion.js");
const common_api_system = require("../../../common/api/system.js");
const common_utils_common = require("../../../common/utils/common.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  _easycom_a_audio2();
}
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
if (!Math) {
  _easycom_a_audio();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "suggestion-info",
  setup(__props) {
    const formData = common_vendor.ref({});
    const fileList = common_vendor.ref([]);
    const typeList = common_vendor.ref([]);
    const departmentList = common_vendor.ref([]);
    const hideIdCard = (idCard) => {
      if (!idCard)
        return "";
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, "$1********$2");
    };
    const hidePhone = (phone) => {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    };
    const getStatusText = (status) => {
      switch (status) {
        case "-1":
          return "未开始";
        case "0":
          return "进行中";
        case "1":
          return "已结束";
        case "2":
          return "已提交";
        default:
          return "未知状态";
      }
    };
    const getTypeDisplayName = (typeId) => {
      if (!typeId)
        return "未知类型";
      const type = typeList.value.find((item) => item.dictCode === typeId);
      return type ? type.dictLabel : `类型${typeId}`;
    };
    const getDepartmentDisplayName = (departmentId) => {
      if (!departmentId)
        return "未知部门";
      const department = departmentList.value.find((item) => item.dictValue === departmentId);
      return department ? department.dictLabel : `部门${departmentId}`;
    };
    const initPickerData = async () => {
      try {
        const [typeRes, departmentRes] = await Promise.all([
          common_api_system.getSysQuestionnaireLabel(),
          common_api_system.getSysQuestionnaireDepartment()
        ]);
        if (typeRes == null ? void 0 : typeRes.data) {
          typeList.value = typeRes.data;
        }
        if (departmentRes == null ? void 0 : departmentRes.data) {
          departmentList.value = departmentRes.data;
        }
      } catch (error) {
        console.error("获取类型和部门数据失败:", error);
      }
    };
    const isImage = (url) => {
      var _a;
      if (!url)
        return false;
      const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      const extension = (_a = url.split(".").pop()) == null ? void 0 : _a.toLowerCase();
      return imageExtensions.includes(extension || "");
    };
    const previewFile = (file) => {
      if (isImage(file.url)) {
        common_vendor.index.previewImage({
          urls: fileList.value.filter((f) => isImage(f.url)).map((f) => f.url),
          current: file.url
        });
      } else {
        common_vendor.index.showToast({
          title: "暂不支持预览此类文件",
          icon: "none"
        });
      }
    };
    const getSuggestionInfo = async (id) => {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const res = await common_api_suggestion.getSuggestInfo(id);
        console.log("建言信息:", res);
        if ((res == null ? void 0 : res.code) === 200 && res.data) {
          const questionnaire = res.data.questionnaire || {};
          const user = res.data.user || {};
          formData.value = {
            title: questionnaire.title,
            typeName: questionnaire.typeName || getTypeDisplayName(questionnaire.type),
            // 如果没有typeName，显示type
            content: questionnaire.content,
            stratagem: questionnaire.stratagem,
            departmentName: questionnaire.departmentName || getDepartmentDisplayName(questionnaire.department),
            // 如果没有departmentName，显示department
            districtName: questionnaire.districtName,
            contactName: questionnaire.contactName,
            idCard: questionnaire.idCard,
            contactPhone: questionnaire.contactPhone,
            createTime: questionnaire.createTime,
            status: questionnaire.status,
            voiceUrl: questionnaire.voiceUrl,
            voiceDuration: questionnaire.voiceDuration,
            voiceText: questionnaire.voiceText
          };
          if (questionnaire.ossVoList && questionnaire.ossVoList.length > 0) {
            fileList.value = questionnaire.ossVoList.map((file) => ({
              url: file.url,
              name: file.originalName || file.fileName,
              id: file.ossId
            }));
          }
        } else {
          common_vendor.index.showToast({
            title: (res == null ? void 0 : res.msg) || "获取信息失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取建言信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    common_vendor.onLoad(async (options) => {
      const { id } = options;
      if (id) {
        await initPickerData();
        await getSuggestionInfo(id);
      } else {
        common_vendor.index.showToast({
          title: "缺少必要参数",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(formData.value.title || "--"),
        b: common_vendor.t(formData.value.typeName || "--"),
        c: common_vendor.t(formData.value.content || "--"),
        d: common_vendor.t(formData.value.stratagem || "--"),
        e: formData.value.voiceUrl
      }, formData.value.voiceUrl ? common_vendor.e({
        f: common_vendor.p({
          voiceUrl: formData.value.voiceUrl,
          duration: formData.value.voiceDuration
        }),
        g: formData.value.voiceText
      }, formData.value.voiceText ? {
        h: common_vendor.t(formData.value.voiceText)
      } : {}) : {}, {
        i: fileList.value.length > 0
      }, fileList.value.length > 0 ? {
        j: common_vendor.f(fileList.value, (file, index, i0) => {
          return common_vendor.e({
            a: isImage(file.url)
          }, isImage(file.url) ? {
            b: file.url
          } : {
            c: common_vendor.t(file.name || "文件")
          }, {
            d: index,
            e: common_vendor.o(($event) => previewFile(file), index)
          });
        })
      } : {}, {
        k: common_vendor.t(formData.value.departmentName || "--"),
        l: common_vendor.t(formData.value.districtName || "--"),
        m: common_vendor.t(formData.value.contactName || "--"),
        n: common_vendor.t(hideIdCard(formData.value.idCard) || "--"),
        o: common_vendor.t(hidePhone(formData.value.contactPhone) || "--"),
        p: common_vendor.t(common_vendor.unref(common_utils_common.formatDate)(formData.value.createTime) || "--"),
        q: common_vendor.t(getStatusText(formData.value.status) || "--")
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-64510041"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/suggestion/suggestion-info.vue"]]);
wx.createPage(MiniProgramPage);
