<z-paging wx:if="{{e}}" class="r data-v-07ecd671" u-s="{{['d']}}" u-r="paging" bindquery="{{c}}" u-i="07ecd671-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="w" class="data-v-07ecd671" style="border-bottom:solid 1px #EEEEEE;padding:20rpx"><view class="list data-v-07ecd671" style="border-bottom:none;margin:0"><view class="data-v-07ecd671"><image class="data-v-07ecd671" mode="aspectFill" style="height:300rpx;width:28vw" src="{{item.a}}"></image></view><view class="info data-v-07ecd671"><view class="data-v-07ecd671" style="display:flex;align-items:center;padding:0;flex-wrap:wrap;column-gap:10rpx"><view class="title data-v-07ecd671" style="margin-right:28rpx">{{item.b}}</view></view><view class="grey data-v-07ecd671" style="align-items:center;display:flex"><image class="icon data-v-07ecd671" src="/static/tourLine/location.png" mode=""></image><uv-text wx:if="{{item.d}}" class="data-v-07ecd671" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"></uv-text></view><view class="grey data-v-07ecd671" style="align-items:center;display:flex"><image class="icon data-v-07ecd671" src="/static/tourLine/person.png" mode=""></image><uv-text wx:if="{{item.f}}" class="data-v-07ecd671" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"></uv-text></view><view class="grey data-v-07ecd671" style="align-items:center;display:flex"><view class="data-v-07ecd671">状态：</view><uv-text wx:if="{{item.g}}" class="data-v-07ecd671" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"></uv-text><uv-text wx:if="{{item.j}}" class="data-v-07ecd671" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"></uv-text><uv-text wx:if="{{item.m}}" class="data-v-07ecd671" u-i="{{item.n}}" bind:__l="__l" u-p="{{item.o}}"></uv-text></view><view wx:if="{{item.p}}" class="grey data-v-07ecd671" style="align-items:center;display:flex"><view class="data-v-07ecd671">原因：</view><uv-text wx:if="{{item.r}}" class="data-v-07ecd671" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"></uv-text></view></view></view><view class="data-v-07ecd671" style="justify-content:left;display:flex"><view class="btn normal data-v-07ecd671" style="background:#FF9F18" bindtap="{{item.s}}">查看</view><view wx:if="{{item.t}}" class="btn normal data-v-07ecd671" style="background:#FF9F18" bindtap="{{item.v}}">修改</view></view></view></z-paging>