"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_storeUser = require("../../../common/api/storeUser.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_text2 + _easycom_z_paging2)();
}
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_text + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "my-apply",
  setup(__props) {
    common_vendor.ref();
    const userStore = stores_store.useUserStore();
    function edit(item) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        return;
      }
      const url = "/pages/subpackA/storeUser/apply?id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    function itemClick(item) {
      const url = "/pages/subpackA/storeUser/apply?isView=true&id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    function getDataList(offset, count) {
      common_api_storeUser.myApplyList({
        pageNum: offset,
        pageSize: count
        // districtCode:'',//全部
        // sortType: 1//最新
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    function reload() {
      paging.value.reload();
    }
    common_vendor.onLoad(() => {
      common_vendor.index.$on("refreshList", reload);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("refreshList", reload);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.unref(common_utils_common.img)(item.storeImages.split(",")[0]),
            b: common_vendor.t(item.name),
            c: "07ecd671-1-" + i0 + ",07ecd671-0",
            d: common_vendor.p({
              size: "14",
              color: "#999999",
              text: item.address
            }),
            e: "07ecd671-2-" + i0 + ",07ecd671-0",
            f: common_vendor.p({
              size: "14",
              color: "#999999",
              mode: "phone",
              text: item.contactPhone
            }),
            g: item.applyStatus == "0"
          }, item.applyStatus == "0" ? {
            h: "07ecd671-3-" + i0 + ",07ecd671-0",
            i: common_vendor.p({
              type: "primary",
              text: "审核中"
            })
          } : {}, {
            j: item.applyStatus == "1"
          }, item.applyStatus == "1" ? {
            k: "07ecd671-4-" + i0 + ",07ecd671-0",
            l: common_vendor.p({
              type: "success",
              text: "通过"
            })
          } : {}, {
            m: item.applyStatus == "2"
          }, item.applyStatus == "2" ? {
            n: "07ecd671-5-" + i0 + ",07ecd671-0",
            o: common_vendor.p({
              type: "error",
              text: "拒绝"
            })
          } : {}, {
            p: item.applyStatus == "2"
          }, item.applyStatus == "2" ? {
            q: "07ecd671-6-" + i0 + ",07ecd671-0",
            r: common_vendor.p({
              text: item.remark
            })
          } : {}, {
            s: common_vendor.o(($event) => itemClick(item), index),
            t: item.applyStatus == "0"
          }, item.applyStatus == "0" ? {
            v: common_vendor.o(($event) => edit(item), index)
          } : {}, {
            w: index
          });
        }),
        b: common_vendor.sr(paging, "07ecd671-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => dataList.value = $event),
        e: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-07ecd671"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/storeUser/my-apply.vue"]]);
wx.createPage(MiniProgramPage);
