"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../common/config.js");
const common_api_storeUser = require("../../../common/api/storeUser.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_upload_img2 = common_vendor.resolveComponent("upload-img");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_upload_img2 + _easycom_uv_button2 + _easycom_uv_form2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_upload_img = () => "../../../components/upload-img/upload-img.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_upload_img + _easycom_uv_button + _easycom_uv_form)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "apply",
  setup(__props) {
    const rules = Object.freeze({
      name: {
        type: "string",
        required: true,
        message: "请填写名称",
        trigger: ["blur", "change"]
      },
      legalPerson: {
        type: "string",
        required: true,
        message: "请填写负责人/法人",
        trigger: ["blur", "change"]
      },
      address: {
        type: "string",
        required: true,
        message: "请填写具体位置",
        trigger: ["blur", "change"]
      },
      storeImages: {
        type: "string",
        required: true,
        message: "请上传门店图片",
        trigger: ["blur", "change"],
        validator: (rule, value, callback) => {
          if (!value || value.length == 0) {
            callback(new Error("请上传门店图片！"));
          } else {
            callback();
          }
        }
      },
      licenseImages: {
        type: "string",
        required: true,
        message: "请上传营业执照",
        trigger: ["blur", "change"],
        validator: (rule, value, callback) => {
          if (!value || value.length == 0) {
            callback(new Error("请上传营业执照！"));
          } else {
            callback();
          }
        }
      },
      contactPhone: {
        type: "string",
        required: true,
        message: "请输入正确的联系电话",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      }
    });
    const storeImagesList = common_vendor.ref([]);
    const licenseImagesList = common_vendor.ref([]);
    const form = common_vendor.reactive({
      id: "",
      name: "",
      legalPerson: "",
      storeImages: "",
      licenseImages: "",
      address: "",
      longitude: 0,
      latitude: 0,
      contactPhone: ""
    });
    const formRef = common_vendor.ref();
    const getCurrentLocation = () => {
      if (isView.value) {
        return;
      }
      common_vendor.index.chooseLocation({
        success: function(res) {
          form.longitude = res.longitude;
          form.latitude = res.latitude;
          form.address = res.address;
        },
        fail(e) {
          console.log(e);
        }
      });
    };
    const submit = async () => {
      form.storeImages = storeImagesList.value.map((item) => {
        return item.url;
      }).join(",");
      form.licenseImages = licenseImagesList.value.map((item) => {
        return item.url;
      }).join(",");
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        common_api_storeUser.apply(form).then((res) => {
          common_vendor.index.hideLoading();
          common_vendor.index.navigateBack({
            success: function() {
              common_vendor.index.$emit("refreshList", {});
            }
          });
        });
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    const isView = common_vendor.ref(false);
    common_vendor.onLoad((options) => {
      if (options.isView) {
        common_vendor.index.setNavigationBarTitle({
          title: "查看入住商户信息"
        });
        isView.value = true;
      }
      if (options.id) {
        let id = decodeURIComponent(options.id);
        common_api_storeUser.getDetails({
          id
        }).then((res) => {
          Object.assign(form, res.data);
          form.name = res.data.name;
          storeImagesList.value = form.storeImages.split(",").map((item, index) => {
            return { url: item };
          });
          licenseImagesList.value = form.licenseImages.split(",").map((item, index) => {
            return { url: item };
          });
          common_vendor.index.setNavigationBarTitle({
            title: "修改商户信息"
          });
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => form.name = $event),
        b: common_vendor.p({
          placeholder: "请输入商家全称",
          disabled: isView.value,
          modelValue: form.name
        }),
        c: common_vendor.p({
          label: "商家全称",
          prop: "name",
          required: true
        }),
        d: common_vendor.o(($event) => form.legalPerson = $event),
        e: common_vendor.p({
          placeholder: "请输入负责人/法人",
          disabled: isView.value,
          modelValue: form.legalPerson
        }),
        f: common_vendor.p({
          label: "负责人/法人",
          prop: "legalPerson",
          required: true
        }),
        g: common_vendor.o(($event) => storeImagesList.value = $event),
        h: common_vendor.p({
          ["max-count"]: 5,
          disabled: isView.value,
          modelValue: storeImagesList.value
        }),
        i: common_vendor.p({
          label: "门店图片",
          prop: "storeImages",
          required: true
        }),
        j: common_vendor.o(($event) => licenseImagesList.value = $event),
        k: common_vendor.p({
          ["max-count"]: 5,
          disabled: isView.value,
          modelValue: licenseImagesList.value
        }),
        l: common_vendor.p({
          label: "营业执照",
          prop: "licenseImages",
          required: true
        }),
        m: common_vendor.o(getCurrentLocation),
        n: common_vendor.o(($event) => form.address = $event),
        o: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择地址",
          suffixIcon: "map-fill",
          suffixIconStyle: "color: #909399",
          modelValue: form.address
        }),
        p: common_vendor.p({
          label: "地址",
          prop: "address",
          required: true
        }),
        q: common_vendor.o(($event) => form.contactPhone = $event),
        r: common_vendor.p({
          type: "number",
          placeholder: "请输入联系电话",
          disabled: isView.value,
          modelValue: form.contactPhone
        }),
        s: common_vendor.p({
          label: "联系电话",
          prop: "contactPhone",
          required: true
        }),
        t: !isView.value
      }, !isView.value ? {
        v: common_vendor.o(submit),
        w: common_vendor.p({
          type: "warning",
          text: "提交审核",
          customStyle: "margin-top: 10px"
        })
      } : {}, {
        x: common_vendor.sr(formRef, "298b44be-0", {
          "k": "formRef"
        }),
        y: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-298b44be"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/storeUser/apply.vue"]]);
wx.createPage(MiniProgramPage);
