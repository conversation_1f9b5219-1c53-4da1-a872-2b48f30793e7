<view class="data-v-298b44be"><view class="form_view data-v-298b44be"><uv-form wx:if="{{y}}" class="r data-v-298b44be" u-s="{{['d']}}" u-r="formRef" u-i="298b44be-0" bind:__l="__l" u-p="{{y}}"><view class="bg_item data-v-298b44be"><uv-form-item wx:if="{{c}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-1,298b44be-0" bind:__l="__l" u-p="{{c}}"><uv-input wx:if="{{b}}" class="data-v-298b44be" u-i="298b44be-2,298b44be-1" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"></uv-input></uv-form-item><uv-form-item wx:if="{{f}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-3,298b44be-0" bind:__l="__l" u-p="{{f}}"><uv-input wx:if="{{e}}" class="data-v-298b44be" u-i="298b44be-4,298b44be-3" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"></uv-input></uv-form-item><uv-form-item wx:if="{{i}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-5,298b44be-0" bind:__l="__l" u-p="{{i}}"><upload-img wx:if="{{h}}" class="mt-[20rpx] data-v-298b44be" u-i="298b44be-6,298b44be-5" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"></upload-img></uv-form-item><uv-form-item wx:if="{{l}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-7,298b44be-0" bind:__l="__l" u-p="{{l}}"><upload-img wx:if="{{k}}" class="mt-[20rpx] data-v-298b44be" u-i="298b44be-8,298b44be-7" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"></upload-img></uv-form-item><uv-form-item wx:if="{{p}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-9,298b44be-0" bind:__l="__l" u-p="{{p}}"><uv-input wx:if="{{o}}" class="data-v-298b44be" bindtap="{{m}}" u-i="298b44be-10,298b44be-9" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"></uv-input></uv-form-item><text class="data-v-298b44be" style="font-weight:400;font-size:24rpx;color:#999999">提示：请先打开手机定位权限，否则获取位置</text></view><view class="bg_item data-v-298b44be"><uv-form-item wx:if="{{s}}" class="data-v-298b44be" u-s="{{['d']}}" u-i="298b44be-11,298b44be-0" bind:__l="__l" u-p="{{s}}"><uv-input wx:if="{{r}}" class="data-v-298b44be" u-i="298b44be-12,298b44be-11" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"></uv-input></uv-form-item></view><view wx:if="{{t}}" class="bg_item data-v-298b44be"><uv-button wx:if="{{w}}" class="data-v-298b44be" bindclick="{{v}}" u-i="298b44be-13,298b44be-0" bind:__l="__l" u-p="{{w}}"></uv-button></view></uv-form></view></view>