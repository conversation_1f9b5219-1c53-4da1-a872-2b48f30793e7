"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_store = require("../../../common/api/store.js");
const stores_store = require("../../../stores/store.js");
const common_api_storeUser = require("../../../common/api/storeUser.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_button2 + _easycom_uv_swiper2 + _easycom_uv_text2 + _easycom_z_paging2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_swiper + _easycom_uv_text + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    let hiddenSide = common_vendor.ref(false);
    const current1 = common_vendor.ref(0);
    const list = common_vendor.ref([common_utils_common.img("https://qlzhsq.qlzhsq.cn:30400/images/static/shequhaowu.png")]);
    const userStore = stores_store.useUserStore();
    function apply(type) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          cancelText: "取消",
          confirmText: "去认证",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        return;
      }
      if (type == 0) {
        const url = "/pages/subpackA/store/my-add";
        common_vendor.index.navigateTo({
          url
        });
      } else if (type == 1) {
        const url = "/pages/subpackA/store/add";
        common_vendor.index.navigateTo({
          url
        });
      } else if (type == 2) {
        const url = "/pages/subpackA/storeUser/my-apply";
        common_vendor.index.navigateTo({
          url
        });
      } else if (type == 3) {
        const url = "/pages/subpackA/storeUser/apply";
        common_vendor.index.navigateTo({
          url
        });
      }
    }
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    function itemClick(item) {
      const url = "/pages/subpackA/store/details?id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const name = common_vendor.ref("");
    function search() {
      paging.value.reload();
    }
    function getDataList(offset, count) {
      common_api_store.getList({
        pageNum: offset,
        pageSize: count,
        name: name.value
        // districtCode:'',//全部
        // sortType: 1//最新
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    const popularList = common_vendor.ref([]);
    function getPopularListData() {
      common_api_store.getPopularList({
        pageNum: 1,
        pageSize: 4
      }).then((res) => {
        popularList.value = res.rows;
      });
    }
    const applyStatus = common_vendor.ref(-1);
    common_vendor.onLoad((options) => {
      hiddenSide.value = (options == null ? void 0 : options.hidden) == 1 ? true : false;
      common_api_storeUser.checkAuth().then((res) => {
        applyStatus.value = res.data;
      });
      getPopularListData();
      common_vendor.index.$on("refreshList", search);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("refreshList", search);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !common_vendor.unref(hiddenSide)
      }, !common_vendor.unref(hiddenSide) ? {
        b: name.value,
        c: common_vendor.o(($event) => name.value = $event.detail.value),
        d: common_vendor.o(($event) => search()),
        e: common_vendor.p({
          type: "warning",
          customStyle: {
            height: "50rpx"
          }
        }),
        f: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.n(index === current1.value && "indicator__dot--active")
          };
        }),
        g: common_vendor.o((e) => current1.value = e.current),
        h: common_vendor.p({
          height: "300rpx",
          list: list.value,
          autoplay: false
        })
      } : {}, {
        i: applyStatus.value == 1
      }, applyStatus.value == 1 ? {
        j: common_vendor.o(($event) => apply(0))
      } : {}, {
        k: applyStatus.value == 1
      }, applyStatus.value == 1 ? {
        l: common_vendor.o(($event) => apply(1))
      } : {}, {
        m: common_vendor.o(($event) => apply(2)),
        n: applyStatus.value != 1 && applyStatus.value != 0
      }, applyStatus.value != 1 && applyStatus.value != 0 ? {
        o: common_vendor.o(($event) => apply(3))
      } : {}, {
        p: !common_vendor.unref(hiddenSide)
      }, !common_vendor.unref(hiddenSide) ? {
        q: common_vendor.f(popularList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.o(($event) => itemClick(item), index)
          };
        })
      } : {}, {
        r: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: "9f923d17-3-" + i0 + ",9f923d17-0",
            e: common_vendor.p({
              lines: 2,
              size: "14",
              text: item.brief
            }),
            f: common_vendor.t(item.address),
            g: common_vendor.o(($event) => openLocation(item), index),
            h: common_vendor.t(item.contactPhone),
            i: common_vendor.o(() => {
            }, index),
            j: common_vendor.t(item.price),
            k: index,
            l: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        s: common_vendor.sr(paging, "9f923d17-0", {
          "k": "paging"
        }),
        t: common_vendor.o(queryList),
        v: common_vendor.o(($event) => dataList.value = $event),
        w: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9f923d17"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/store/index.vue"]]);
wx.createPage(MiniProgramPage);
