/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.indicator.data-v-d4db0a4f {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: #000000;
  opacity: 0.7;
  padding: 2px;
  border-radius: 6px;
}
.indicator__dot.data-v-d4db0a4f {
  height: 6px;
  width: 6px;
  border-radius: 100px;
  background-color: rgba(255, 255, 255, 0.35);
  margin: 0 3px;
  transition: background-color 0.3s;
}
.indicator__dot--active.data-v-d4db0a4f {
  background-color: #ffffff;
}
.indicator-num.data-v-d4db0a4f {
  padding: 2px 0;
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.indicator-num__text.data-v-d4db0a4f {
  color: #FFFFFF;
  font-size: 12px;
}
.data-v-d4db0a4f .uv-swiper {
  background-color: rgba(255, 0, 0, 0) !important;
}
.icon.data-v-d4db0a4f {
  height: 40rpx;
  width: 40rpx;
  min-width: 40rpx;
}
.btn.data-v-d4db0a4f {
  min-width: 200rpx;
  height: 80rpx;
  background-size: 100%;
  text-align: center;
  line-height: 65rpx;
}
.btn.data-v-d4db0a4f:nth-of-type(n+2) {
  margin-left: 10rpx;
}
.normal.data-v-d4db0a4f {
  background-repeat: repeat;
  border-radius: 50rpx;
  color: #FFFFFF;
  border: 2rpx solid #FFFFFF;
  height: 65rpx;
  line-height: 55rpx;
  margin-top: 4rpx;
  padding: 0 15rpx;
}
.search.data-v-d4db0a4f {
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 15rpx;
  color: #395C16;
  background-color: rgba(255, 255, 255, 0.2);
}
.content.data-v-d4db0a4f {
  position: relative;
  /*height: calc(100vh - 500rpx);*/
  min-height: 50rpx;
  background: #FFFFFF;
  border-radius: 50rpx 50rpx 0 0;
  margin-top: -50rpx;
  padding: 30rpx 20rpx 0 20rpx;
}
.hots.data-v-d4db0a4f {
  display: flex;
  justify-content: left;
  align-items: baseline;
}
.title.data-v-d4db0a4f {
  font-weight: bold;
  color: #333333;
  font-size: 17px;
}
.labels.data-v-d4db0a4f {
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10rpx;
}
.labels .label.data-v-d4db0a4f {
  border: 2rpx solid #CCCCCC;
  border-radius: 10rpx;
  padding: 10rpx;
  color: #999999;
  margin: 5rpx 10rpx;
  font-size: 13px;
}
.labels .label.data-v-d4db0a4f:hover {
  background: #FF9F18;
  color: #FFFFFF;
}
.list.data-v-d4db0a4f {
  display: flex;
  justify-content: left;
  align-items: center;
  background: #FFFFFF;
  margin: 20rpx;
  border-bottom: solid 1px #EEEEEE;
  color: #333333;
}
.list .info.data-v-d4db0a4f {
  padding-left: 25rpx;
}
.list .info > view.data-v-d4db0a4f {
  padding: 10rpx 0;
}
.list .tag.data-v-d4db0a4f {
  color: #FF9F18;
}
.list .grey.data-v-d4db0a4f {
  color: #999999;
}