"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_store = require("../../../common/api/store.js");
const common_utils_share = require("../../../common/utils/share.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/utils/common.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_parse2 = common_vendor.resolveComponent("uv-parse");
  (_easycom_uv_swiper2 + _easycom_uv_parse2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_parse = () => "../../../uni_modules/uv-parse/components/uv-parse/uv-parse.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_parse)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "details",
  setup(__props) {
    stores_store.useAreaStore();
    stores_store.useUserStore();
    const list = common_vendor.ref([]);
    const id = common_vendor.ref("");
    const data = common_vendor.ref({});
    const { setShare } = common_utils_share.useShare();
    let share = {};
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    common_vendor.onLoad((query) => {
      if (query && query.id) {
        id.value = decodeURIComponent(query.id);
        common_api_store.getDetails({
          id: id.value
        }).then((res) => {
          if (res && res.data) {
            data.value = res.data;
            if (data.value.coverImages) {
              list.value = [data.value.coverImages];
            }
            share = {
              title: data.value.name || "",
              desc: "",
              imageUrl: data.value.coverImages || ""
            };
            setShare({
              weapp: {
                ...share
              }
            });
          }
        }).catch((err) => {
          console.error("获取商品详情失败", err);
        });
      }
    });
    common_vendor.onShow(() => {
      setShare({
        weapp: {
          ...share
        }
      });
    });
    common_vendor.onUnload(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          height: "500rpx",
          width: "100%",
          list: list.value,
          radius: "0"
        }),
        b: common_vendor.t(data.value.name),
        c: common_vendor.t(data.value.address),
        d: common_vendor.o(($event) => openLocation(data.value)),
        e: common_vendor.t(data.value.contactPhone),
        f: common_vendor.t(data.value.price),
        g: common_vendor.t(data.value.brief),
        h: common_vendor.p({
          content: data.value.details
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6761abf5"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/store/details.vue"]]);
wx.createPage(MiniProgramPage);
