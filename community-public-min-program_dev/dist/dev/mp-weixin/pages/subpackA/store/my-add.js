"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_store = require("../../../common/api/store.js");
const stores_store = require("../../../stores/store.js");
const common_api_storeUser = require("../../../common/api/storeUser.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_a_popup2 = common_vendor.resolveComponent("a-popup");
  (_easycom_uv_text2 + _easycom_z_paging2 + _easycom_a_popup2)();
}
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_a_popup = () => "../../../components/a-popup/a-popup.js";
if (!Math) {
  (_easycom_uv_text + _easycom_z_paging + _easycom_a_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "my-add",
  setup(__props) {
    const deleteModal = common_vendor.ref();
    const userStore = stores_store.useUserStore();
    function apply(type) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        return;
      }
      if (type == 0) {
        const url = "/pages/subpackA/store/add";
        common_vendor.index.navigateTo({
          url
        });
      } else if (type == 1) {
        const url = "/pages/subpackA/storeUser/my-apply";
        common_vendor.index.navigateTo({
          url
        });
      } else if (type == 2) {
        const url = "/pages/subpackA/storeUser/apply";
        common_vendor.index.navigateTo({
          url
        });
      }
    }
    function edit(item) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        return;
      }
      const url = "/pages/subpackA/store/add?id=" + item.id;
      common_vendor.index.navigateTo({
        url
      });
    }
    const deleteTitle = common_vendor.ref();
    const deleteId = common_vendor.ref();
    function deleteMy(item) {
      if (!userStore.userInfo) {
        common_vendor.index.showToast({ title: `请先登录`, icon: "none" });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }, 600);
      }
      if (userStore.userInfo.authentication == 0) {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还未进行实名认证，是否去认证？",
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/my/auth/auth"
              });
            }
          }
        });
        return;
      }
      deleteTitle.value = "您确定是否要删除'" + item.name + "'的数据？";
      deleteId.value = item.id;
      deleteModal.value.open();
    }
    function deleteConfirm() {
      common_api_store.myApplyDelete(deleteId.value).then((res) => {
        reload();
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    function getDataList(offset, count) {
      common_api_store.myApplyList({
        pageNum: offset,
        pageSize: count
        // districtCode:'',//全部
        // sortType: 1//最新
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    function reload() {
      paging.value.reload();
    }
    const applyStatus = common_vendor.ref(-1);
    common_vendor.onLoad(() => {
      common_api_storeUser.checkAuth().then((res) => {
        applyStatus.value = res.data;
      });
      common_vendor.index.$on("refreshList", reload);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("refreshList", reload);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: applyStatus.value == 1
      }, applyStatus.value == 1 ? {
        b: common_vendor.o(($event) => apply(0))
      } : {}, {
        c: applyStatus.value == 0
      }, applyStatus.value == 0 ? {
        d: common_vendor.o(($event) => apply(1))
      } : {}, {
        e: applyStatus.value == -1
      }, applyStatus.value == -1 ? {
        f: common_vendor.o(($event) => apply(2))
      } : {}, {
        g: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: "d4db0a4f-1-" + i0 + ",d4db0a4f-0",
            e: common_vendor.p({
              lines: 2,
              size: "14",
              text: item.brief
            }),
            f: "d4db0a4f-2-" + i0 + ",d4db0a4f-0",
            g: common_vendor.p({
              size: "14",
              color: "#999999",
              text: item.address
            }),
            h: "d4db0a4f-3-" + i0 + ",d4db0a4f-0",
            i: common_vendor.p({
              size: "14",
              color: "#999999",
              mode: "phone",
              text: item.contactPhone
            }),
            j: item.applyStatus == "0"
          }, item.applyStatus == "0" ? {
            k: "d4db0a4f-4-" + i0 + ",d4db0a4f-0",
            l: common_vendor.p({
              type: "primary",
              text: "审核中"
            })
          } : {}, {
            m: item.applyStatus == "1"
          }, item.applyStatus == "1" ? {
            n: "d4db0a4f-5-" + i0 + ",d4db0a4f-0",
            o: common_vendor.p({
              type: "success",
              text: "通过"
            })
          } : {}, {
            p: item.applyStatus == "2"
          }, item.applyStatus == "2" ? {
            q: "d4db0a4f-6-" + i0 + ",d4db0a4f-0",
            r: common_vendor.p({
              type: "error",
              text: "拒绝"
            })
          } : {}, {
            s: item.applyStatus == "2"
          }, item.applyStatus == "2" ? {
            t: "d4db0a4f-7-" + i0 + ",d4db0a4f-0",
            v: common_vendor.p({
              text: item.remark
            })
          } : {}, {
            w: common_vendor.o(($event) => edit(item), index),
            x: common_vendor.o(($event) => deleteMy(item), index),
            y: index
          });
        }),
        h: common_vendor.sr(paging, "d4db0a4f-0", {
          "k": "paging"
        }),
        i: common_vendor.o(queryList),
        j: common_vendor.o(($event) => dataList.value = $event),
        k: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        l: common_vendor.sr(deleteModal, "d4db0a4f-8", {
          "k": "deleteModal"
        }),
        m: common_vendor.o(deleteConfirm),
        n: common_vendor.p({
          content: deleteTitle.value,
          cancelText: "取消",
          confimText: "确认"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d4db0a4f"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/store/my-add.vue"]]);
wx.createPage(MiniProgramPage);
