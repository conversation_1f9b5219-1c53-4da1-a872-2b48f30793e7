<z-paging wx:if="{{k}}" class="r data-v-d4db0a4f" u-s="{{['top','d']}}" u-r="paging" bindquery="{{i}}" u-i="d4db0a4f-0" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"><view class="data-v-d4db0a4f" style="background:linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);padding:0 1rem 0 1rem" slot="top"><view class="data-v-d4db0a4f" color="#2BBC4A" style="padding:20rpx 0"><view class="data-v-d4db0a4f" style="justify-content:left;display:flex;flex-wrap:wrap"><view wx:if="{{a}}" class="btn normal data-v-d4db0a4f" bindtap="{{b}}">发布好物</view><view wx:if="{{c}}" class="btn normal data-v-d4db0a4f" bindtap="{{d}}">我的入住</view><view wx:if="{{e}}" class="btn normal data-v-d4db0a4f" bindtap="{{f}}">申请入住</view></view></view></view><view wx:for="{{g}}" wx:for-item="item" wx:key="y" class="data-v-d4db0a4f" style="border-bottom:solid 1px #EEEEEE;padding:20rpx"><view class="list data-v-d4db0a4f" style="border-bottom:none;margin:0"><view class="data-v-d4db0a4f"><image class="data-v-d4db0a4f" mode="aspectFill" style="height:300rpx;width:28vw" src="{{item.a}}"></image></view><view class="info data-v-d4db0a4f"><view class="data-v-d4db0a4f" style="display:flex;align-items:center;padding:0;flex-wrap:wrap;column-gap:10rpx"><view class="title data-v-d4db0a4f" style="margin-right:28rpx">{{item.b}}</view><view class="data-v-d4db0a4f" style="color:#FF9F18;background:rgb(255 159 24 / 10%);padding:8rpx">{{item.c}}</view></view><view class="desc data-v-d4db0a4f"><uv-text wx:if="{{item.e}}" class="data-v-d4db0a4f" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></uv-text></view><view class="grey data-v-d4db0a4f" style="align-items:center;display:flex"><image class="icon data-v-d4db0a4f" src="/static/tourLine/location.png" mode=""></image><uv-text wx:if="{{item.g}}" class="data-v-d4db0a4f" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"></uv-text></view><view class="grey data-v-d4db0a4f" style="align-items:center;display:flex"><image class="icon data-v-d4db0a4f" src="/static/tourLine/person.png" mode=""></image><uv-text wx:if="{{item.i}}" class="data-v-d4db0a4f" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"></uv-text></view><view class="grey data-v-d4db0a4f" style="align-items:center;display:flex"><view class="data-v-d4db0a4f">状态：</view><uv-text wx:if="{{item.j}}" class="data-v-d4db0a4f" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"></uv-text><uv-text wx:if="{{item.m}}" class="data-v-d4db0a4f" u-i="{{item.n}}" bind:__l="__l" u-p="{{item.o}}"></uv-text><uv-text wx:if="{{item.p}}" class="data-v-d4db0a4f" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"></uv-text></view><view wx:if="{{item.s}}" class="grey data-v-d4db0a4f" style="align-items:center;display:flex"><view class="data-v-d4db0a4f">原因：</view><uv-text wx:if="{{item.v}}" class="data-v-d4db0a4f" u-i="{{item.t}}" bind:__l="__l" u-p="{{item.v}}"></uv-text></view></view></view><view class="data-v-d4db0a4f" style="justify-content:left;display:flex"><view class="btn normal data-v-d4db0a4f" style="background:#FF9F18" bindtap="{{item.w}}">修改</view><view class="btn normal data-v-d4db0a4f" style="background:#FFCD35" bindtap="{{item.x}}">删除</view></view></view></z-paging><a-popup wx:if="{{n}}" class="r data-v-d4db0a4f" u-r="deleteModal" bindemitEvent="{{m}}" u-i="d4db0a4f-8" bind:__l="__l" u-p="{{n}}"></a-popup>