<view class="container"><view class="top"><view class="top-left" bindtap="{{a}}"><image class="menu-icon" src="/static/ai/menu.png"></image><text class="title">AI智能服务</text></view></view><view class="ai_view"><view class="tag"> 智能服务<image src="/static/ai/arrow.png" mode=""></image></view></view><view class="content_body" style="{{'height:' + k}}"><scroll-view scroll-y="true" style="height:100%" scroll-with-animation="{{true}}" enhanced="{{true}}" show-scrollbar="{{true}}" id="{{h}}" scroll-top="{{i}}" bindscroll="{{j}}"><block wx:if="{{b}}"><block wx:for="{{c}}" wx:for-item="item" wx:key="i"><view class="flex_row_end" id="{{item.d}}" style="align-items:center"><view wx:if="{{item.a}}" class="self">{{item.b}}</view><image wx:if="{{item.c}}" class="icon_svg home-icon ai-icon" src="/static/ai/ai-icon.png"></image></view><view wx:if="{{item.e}}" class="answer flex_row_start" id="{{item.h}}"><image class="icon_svg home-icon ai-icon" src="/static/ai/ai-icon.png"></image><view wx:if="{{item.f}}" class="text"><text class="ai-response" space="nbsp" decode="{{true}}">{{item.g}}</text></view></view></block></block><view wx:if="{{d}}" class="site-search-container"><view wx:if="{{e}}" class="site-search-results"><view wx:for="{{f}}" wx:for-item="result" wx:key="g" class="list" bindtap="{{result.h}}"><view class="info"><view wx:if="{{result.a}}" class="info-left"><image class="image-icon" src="{{result.b}}"></image></view><view class="info-right"><view class="title">{{result.c}} <text wx:if="{{result.d}}" class="area-name">{{result.e}}</text></view><view class="desc"><view class="content-text">{{result.f}}</view></view></view></view></view></view><view wx:else class="no-results"><text>没有找到相关结果，请尝试其他关键词</text></view><view class="back-to-chat" bindtap="{{g}}"><text>返回对话</text></view></view></scroll-view></view><view class="search_input"><view class="ai-i-box"><view class="{{['ai-i-item', l && 'ai-i-item-active']}}" bindtap="{{m}}" disabled="{{n}}"><text>深度思考</text></view><view class="{{['ai-i-item', o && 'ai-i-item-active']}}" bindtap="{{p}}" disabled="{{q}}"><text>联网查询</text></view><view class="{{['ai-i-item', r && 'ai-i-item-active']}}" bindtap="{{s}}"><text>站点搜索</text></view></view><view class="custom-input-container"><input type="text" class="custom-input" placeholder-class="custom-placeholder" placeholder="请输入问题..." confirm-type="search" bindconfirm="{{t}}" value="{{v}}" bindinput="{{w}}"/></view><image wx:if="{{x}}" class="icon_svg home-icon" src="/static/ai/send.svg" bindtap="{{y}}"></image><image wx:else class="icon_svg home-icon" src="{{z}}" bindtap="{{A}}"></image></view><view wx:if="{{B}}" class="{{['custom-popup', I && 'popup-show']}}"><view class="popup-overlay" bindtap="{{C}}"></view><view class="{{['popup-container', H && 'slide-in']}}"><view class="popup-content"><view class="popup-header"><text class="popup-title">历史对话记录</text><view class="popup-close" bindtap="{{D}}">×</view></view><view class="popup-body"><view wx:if="{{E}}" class="chat-history"><view class="history-list"><view wx:for="{{F}}" wx:for-item="item" wx:key="b" class="{{['history-item', G && 'fade-in']}}" style="{{'animation-delay:' + item.c}}" bindtap="{{item.d}}"><view class="history-content">{{item.a}}</view></view></view></view></view></view></view></view></view>