/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}
.top {
  padding: 8px 0 6px 16px; /* 减小顶部区域的上下内边距 */
  text-align: left;
  color: #ffffff;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}
.top .top-left {
  display: flex;
  align-items: center;
}
.top .menu-icon {
  width: 42rpx; /* 减小图标尺寸 */
  height: 42rpx; /* 减小图标尺寸 */
  margin-right: 8rpx;
}
.top .title {
  font-size: 22px; /* 减小标题字体大小 */
  cursor: pointer;
}
.top .top-location-box {
  display: flex;
  align-items: center;
  color: #fff;
}
.top .top-location-box image:first-of-type {
  width: 20rpx; /* 减小图标尺寸 */
  height: 24rpx; /* 减小图标尺寸 */
  margin-right: 10rpx;
}
.top .top-location-box text {
  font-weight: 400;
  font-size: 22rpx; /* 减小字体大小 */
  color: #FFFFFF;
  line-height: 36rpx; /* 减小行高 */
}
.debug-panel {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.85);
  z-index: 999;
  padding: 10px;
}
.debug-panel .debug-title {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  text-align: center;
}
.debug-panel .debug-item {
  padding: 2px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.debug-panel .debug-item .debug-text {
  color: #ddd;
  font-size: 12px;
  word-break: break-all;
}
.debug-panel .debug-actions {
  display: flex;
  margin-top: 10px;
}
.debug-panel .debug-actions .debug-button {
  flex: 1;
  text-align: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 0;
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  margin: 0 5px;
}
.ai_view {
  width: 100%;
  padding: 22rpx 30rpx; /* 减小ai_view区域的内边距 */
  margin-top: -5px; /* 保持负上边距 */
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}
.ai_view .tag {
  padding: 20rpx 12px; /* 减小tag的内边距 */
  background: #F5F5F5;
  font-size: 13px; /* 减小字体大小 */
  color: #222222;
}
.ai_view .tag image {
  width: 12px; /* 减小图标尺寸 */
  height: 12px; /* 减小图标尺寸 */
}
.content_body {
  background-color: #F5F5F5;
  flex: 1;
  position: relative;
  overflow: hidden;
  margin-bottom: 140rpx; /* 减小底部边距 */
  padding: 20rpx;
  box-sizing: border-box;
}
.content_body scroll-view {
  background-color: #F5F5F5;
  padding: 2px 0;
  height: 100%; /* 确保滚动视图占满整个容器 */
  box-sizing: border-box;
}
.content_body .flex-center {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.content_body .flex_row_end {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24rpx;
}
.content_body .flex_row_start {
  display: flex;
  justify-content: flex-start;
  margin: 1px 0; /* 减小消息间距 */
}
.content_body .self {
  max-width: 85%;
  padding: 16rpx 24px; /* 减小内边距 */
  box-sizing: border-box;
  background: rgba(255, 159, 24, 0.2);
  font-weight: 400;
  font-size: 26rpx; /* 减小字体大小 */
  color: #333333;
  display: flex;
  align-items: center;
  border-radius: 16rpx 16rpx 0rpx 16rpx;
}
.content_body .answer {
  padding: 4px 14rpx 4rpx 4rpx; /* 减小内边距 */
  box-sizing: border-box;
}
.content_body .answer .ai-icon {
  width: 77rpx; /* 减小图标大小 */
  height: 77rpx; /* 减小图标大小 */
  margin-right: 8rpx;
}
.content_body .answer .title {
  color: gainsboro;
  font-size: 11px; /* 减小字体大小 */
}
.content_body .answer .reason {
  margin-top: 3px; /* 减小边距 */
  font-size: 11px; /* 减小字体大小 */
  color: gray;
}
.content_body .answer .text {
  font-weight: 400;
  font-size: 26rpx; /* 减小字体大小 */
  color: #333333;
  padding: 16rpx 24rpx; /* 减小内边距 */
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 0rpx;
  max-width: 80%;
  display: flex;
  align-items: center;
  word-break: break-all;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
.search_input {
  position: fixed;
  bottom: 1px; /* 减小底部距离 */
  left: 1%;
  width: 98%;
  padding: 1px 4px; /* 减小内边距 */
  border-radius: 28rpx; /* 减小圆角 */
  background: #f5f5f5;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  z-index: 10; /* 确保输入框在上层 */
  height: 40px; /* 减小固定高度 */
}
.search_input .custom-input-container {
  flex: 1;
  height: 32px; /* 减小高度 */
  display: flex;
  align-items: center;
}
.search_input .custom-input {
  width: 100%;
  height: 30px; /* 减小高度 */
  font-size: 14px;
  color: #333;
  border: none;
  background: transparent;
  padding: 0 10px;
  background: #FFFFFF;
  border-radius: 8rpx;
}
.search_input .custom-placeholder {
  color: #999;
  font-size: 14px;
}
.search_input .icon_svg {
  width: 40px; /* 减小发送按钮宽度 */
  height: 25px; /* 减小发送按钮高度 */
  cursor: pointer;
  z-index: 999;
}
.search_input .ai-i-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: absolute;
  background: #f5f5f5;
  top: -65rpx; /* 调整位置 */
  left: 0;
  width: 100%;
  height: 80%; /* 减小高度 */
}
.search_input .ai-i-box .ai-i-item {
  background-color: #fff;
  border-radius: 18rpx; /* 减小圆角 */
  padding: 6rpx 14rpx; /* 减小内边距 */
  margin-right: 14rpx;
  box-sizing: border-box;
  font-size: 24rpx; /* 减小字体大小 */
  color: #000;
  transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.search_input .ai-i-box .ai-i-item-active {
  background-color: #FFCD35;
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.search_input .ai-i-box .ai-i-item[disabled=true] {
  opacity: 0.5;
  pointer-events: none;
}
.custom-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
}
.popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.35s cubic-bezier(0.22, 1, 0.36, 1);
}
.popup-show .popup-overlay {
  opacity: 1;
}
.popup-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 75%;
  height: 100%;
  background-color: #fff;
  transform: translateX(-100%);
  transition: transform 0.35s cubic-bezier(0.22, 1, 0.36, 1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}
.popup-container.slide-in {
  transform: translateX(0);
}
.popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  overflow: hidden; /* 防止内容溢出 */
}
.popup-header {
  padding: 20px 15px;
  border-bottom: 1px solid #f2f2f2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fcfcfc;
}
.popup-close {
  font-size: 28px;
  font-weight: 200;
  color: #909399;
  padding: 0 5px;
  line-height: 1;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}
.popup-close:active {
  background-color: #f0f0f0;
}
.popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.popup-body {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加惯性滚动 */
}
.info-item {
  margin-bottom: 24px;
  opacity: 0;
  transform: translateY(10px);
}
.info-item.fade-in {
  animation: fadeInUp 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}
@keyframes fadeInUp {
from {
    opacity: 0;
    transform: translateY(10px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  display: block;
}
.info-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: block;
}
.question-list {
  margin-top: 10px;
}
.question-item {
  background-color: #f8f8f8;
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #333;
  transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
  position: relative;
  overflow: hidden;
}
.question-active {
  background-color: #f0f0f0;
  transform: scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.question-item::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.question-active::after {
  opacity: 1;
}
.site-search-results {
  width: 100%;
  box-sizing: border-box;
}
.site-search-results .search-title {
  font-size: 16px;
  font-weight: bold;
  color: #FF9F18;
  margin: 10rpx 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1px solid #f0f0f0;
}
.list {
  display: flex;
  justify-content: left;
  align-items: center;
  background: #FFFFFF;
  border-bottom: solid 1px #EEEEEE;
  color: #333333;
  padding: 20rpx 0;
  border-radius: 12rpx;
}
.list .info {
  display: flex;
}
.list .info .info-right .area-name {
  padding: 2rpx 12rpx;
  background: rgba(255, 159, 24, 0.1);
  border-radius: 4rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #FF9F18;
  line-height: 40rpx;
  box-sizing: border-box;
}
.list .info .info-right .title {
  display: flex;
  align-items: center;
}
.list .image-icon {
  width: 180rpx;
  height: 240rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
}
.list:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
}
.list .info {
  flex: 1;
}
.list .info > view {
  padding: 10rpx 0;
}
.list .title {
  font-weight: bold;
  color: #333333;
  font-size: 17px;
}
.list .tag {
  color: #FF9F18;
}
.list .grey {
  color: #999999;
  display: flex;
  align-items: center;
  font-size: 14px;
}
.list .desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 10rpx 0;
}
.list .desc .content-text {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  word-break: break-all;
  /* 限制内容显示两行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list .icon {
  margin-right: 10rpx;
  width: 40rpx;
  min-width: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 14px;
}
.chat-history {
  margin-bottom: 30px;
}
.history-list {
  margin-top: 10px;
}
.history-item {
  background-color: #f8f8f8;
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 12px;
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.history-item.fade-in {
  animation: fadeInUp 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}
.history-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}
.history-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
  /* 限制内容显示两行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.site-search-container {
  width: 100%;
  padding: 0 32rpx 30rpx;
  box-sizing: border-box;
  background: #fff;
}
.site-search-container .search-query {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.site-search-container .search-query .query-label {
  font-size: 14px;
  color: #666;
  margin-right: 10rpx;
}
.site-search-container .search-query .query-text {
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.site-search-container .no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;
  color: #999;
  font-size: 14px;
}
.site-search-container .back-to-chat {
  margin-top: 30rpx;
  padding: 15rpx 0;
  text-align: center;
  background-color: #FF9F18;
  color: #fff;
  border-radius: 10rpx;
  font-size: 15px;
}
.code-block {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 10rpx 0;
  margin: 10rpx 0;
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  line-height: 1.4;
  overflow-x: auto;
  border-left: 3px solid #FF9F18;
}
.ai-response {
  white-space: pre-wrap;
  word-break: break-word;
  word-wrap: break-all;
  font-size: 26rpx;
  line-height: 1.6;
  display: block;
  width: 100%;
}