"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const common_api_ai = require("../../../common/api/ai.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ai",
  setup(__props) {
    var _a;
    const areaStore = stores_store.useAreaStore();
    let loading = common_vendor.ref(false), scrollHeight = common_vendor.ref(), isGenerating = common_vendor.ref(false);
    let searchObj = common_vendor.reactive({
      keyWord: "",
      list: [],
      lastSearchKeyword: ""
      // 保存最后一次搜索关键词
    });
    const scrollViewId = common_vendor.ref("aiChatScrollView");
    const currentImid = common_vendor.ref("");
    common_vendor.ref("");
    const searchParams = common_vendor.reactive({
      deepThinking: false,
      // 深度思考
      netSearch: false,
      // 联网查询
      sitesearch: false
      // 站点搜索
    });
    const showSiteSearchResults = common_vendor.ref(false);
    const siteSearchResults = common_vendor.ref([]);
    common_vendor.ref(((_a = { "VITE_USER_NODE_ENV": "development", "VITE_ROOT_DIR": "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev", "BASE_URL": "/", "MODE": "development", "DEV": true, "PROD": false }) == null ? void 0 : _a.MODE) === "development");
    common_vendor.ref(false);
    const debugLogs = common_vendor.ref([]);
    const addDebugLog = (message) => {
      const timestamp = new Date().toLocaleTimeString();
      debugLogs.value.push(`[${timestamp}] ${message}`);
      if (debugLogs.value.length > 50) {
        debugLogs.value = debugLogs.value.slice(-50);
      }
    };
    const openLink = (url) => {
      common_vendor.index.navigateTo({
        url: `/pages/common/webview?url=${encodeURIComponent(url)}`
      });
    };
    const toggleSearchOption = (option) => {
      if (searchParams.sitesearch)
        return;
      searchParams[option] = !searchParams[option];
      if (currentImid.value) {
        console.log(`更改${option}选项为${searchParams[option]}，当前会话ID: ${currentImid.value}`);
      }
    };
    const toggleSiteSearch = () => {
      const willEnableSiteSearch = !searchParams.sitesearch;
      const previousImid = currentImid.value;
      searchParams.sitesearch = willEnableSiteSearch;
      if (willEnableSiteSearch) {
        searchParams.deepThinking = false;
        searchParams.netSearch = false;
        if (currentImid.value) {
          console.log(`启用站点搜索，清除之前的会话ID: ${currentImid.value}`);
          currentImid.value = "";
          common_vendor.index.removeStorageSync("ai_chat_imid");
        }
      } else {
        console.log("关闭站点搜索，准备新的对话");
        if (previousImid) {
          console.log(`注意：之前有会话ID ${previousImid}，但为避免上下文混乱没有自动恢复`);
        }
      }
    };
    const isPopupShow = common_vendor.ref(false);
    const isPopupVisible = common_vendor.ref(false);
    const chatHistoryList = common_vendor.ref([]);
    const pageParams = common_vendor.reactive({
      pageSize: 10,
      // 分页大小
      pageNum: 1,
      // 当前页数
      orderByColumn: "createTime",
      // 排序列
      isAsc: "desc"
      // 排序方向
    });
    const handleSiteSearchResult = async (result) => {
      if (result.code === 200 && result.rows && result.rows.length > 0) {
        searchObj.lastSearchKeyword = searchObj.keyWord;
        siteSearchResults.value = result.rows.map((item) => {
          if (!item.title) {
            item.title = item.name || "搜索结果";
          }
          if (!item.description) {
            item.description = item.content || item.summary || `与"${searchObj.keyWord}"相关的结果`;
          }
          if (!item.avatar) {
            switch (item.type) {
              case 1:
                item.avatar = "../../static/index/icon-shop.png";
                break;
              case 2:
              case 5:
                item.avatar = "../../static/index/icon-market.png";
                break;
              case 3:
                item.avatar = "../../static/index/icon-space.png";
                break;
              case 4:
                item.avatar = "../../static/index/icon-tour.png";
                break;
              case 6:
                item.avatar = "../../static/index/icon-craftsman.png";
                break;
              default:
                item.avatar = "../../static/index/placeholder.png";
                break;
            }
          }
          return item;
        });
        showSiteSearchResults.value = true;
        const resultCount = result.rows.length;
        searchObj.list.push({
          role: "assistant",
          content: `为您找到${resultCount}个相关结果，已为您展示。`
        });
        searchObj.keyWord = "";
        loading.value = false;
        common_vendor.nextTick$1(() => {
          scrollToBottom();
        });
      } else {
        searchObj.lastSearchKeyword = searchObj.keyWord;
        siteSearchResults.value = [];
        showSiteSearchResults.value = true;
        searchObj.list.push({
          role: "assistant",
          content: `抱歉，未找到与"${searchObj.keyWord}"相关的结果，您可以尝试其他关键词。`
        });
        searchObj.keyWord = "";
        loading.value = false;
        common_vendor.nextTick$1(() => {
          scrollToBottom();
        });
      }
    };
    const openSiteResult = (result) => {
      console.log("openSiteResult", result.id, result.type);
      if (result.link) {
        openLink(result.link);
      } else if (result.id) {
        let url = "";
        switch (result.type) {
          case 1:
            url = "/pages/subpackA/store/details?id=" + result.id;
            break;
          case 2:
            url = "/pages/subpackA/market/details?id=" + result.id;
            break;
          case 3:
            url = "/pages/subpackA/space/details?id=" + result.id;
            break;
          case 4:
            url = `/pages/map/guide/guide?toDetails=1&tourLineId=${result.pid}&communityName=${result.title}`;
            break;
          case 5:
            url = "/pages/subpackA/market/details?id=" + result.id;
            break;
          case 6:
            url = "/pages/subpackA/craftsman/details?id=" + result.id;
            break;
          case 7:
            url = `/pages/subpackA/governmentAffairs/index?itemName=${encodeURIComponent(result.description || result.title)}`;
            break;
          default:
            url = `/pages/subpackA/history/details?id=${result.activityId}`;
            break;
        }
        common_vendor.index.navigateTo({
          url
        });
      } else {
        console.log("站点搜索结果无法打开，没有有效链接:", result);
      }
    };
    const showPopup = async () => {
      console.log("showPopup");
      try {
        const result = await common_api_ai.getChatList({
          pageSize: pageParams.pageSize,
          pageNum: pageParams.pageNum,
          orderByColumn: pageParams.orderByColumn,
          isAsc: pageParams.isAsc
        });
        console.log("getChatList返回结果:", result);
        if (result.code === 200 && result.rows) {
          chatHistoryList.value = result.rows || [];
        }
      } catch (error) {
        console.error("获取聊天列表失败:", error);
      }
      isPopupShow.value = true;
      common_vendor.nextTick$1(() => {
        setTimeout(() => {
          isPopupVisible.value = true;
          getScreenHeight();
        }, 10);
      });
    };
    const closePopup = () => {
      isPopupVisible.value = false;
      setTimeout(() => {
        isPopupShow.value = false;
      }, 350);
    };
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      let contentRatio = showSiteSearchResults.value ? 0.58 : 0.52;
      const availableHeight = systemInfo.windowHeight * contentRatio;
      const minHeight = 280;
      const maxHeight = systemInfo.windowHeight * 0.55;
      const paddingHeight = 20;
      scrollHeight.value = Math.min(Math.max(availableHeight, minHeight), maxHeight) - paddingHeight;
      console.log("屏幕高度:", systemInfo.windowHeight, "内容区域高度:", scrollHeight.value, "是否显示搜索结果:", showSiteSearchResults.value);
    };
    const currentAiMessageIndex = common_vendor.ref(-1);
    const eventSource = common_vendor.ref(null);
    const handleSearch = async () => {
      if (!searchObj.keyWord.trim())
        return;
      loading.value = true;
      addDebugLog(`开始搜索: ${searchObj.keyWord}`);
      const isNewConversation = searchObj.list.length <= 1;
      if (isNewConversation) {
        console.log("开始新对话，清除之前的imid");
        currentImid.value = "";
        common_vendor.index.removeStorageSync("ai_chat_imid");
      } else {
        console.log("继续已有对话，当前imid:", currentImid.value);
      }
      if (searchObj.list.length === 1 && searchObj.list[0].role === "assistant" && searchObj.list[0].content.includes("你好，我是智能服务助手")) {
        searchObj.list = [];
      }
      searchObj.list.push({
        role: "user",
        content: searchObj.keyWord
      });
      scrollToBottom();
      if (searchParams.sitesearch) {
        try {
          const result = await common_api_ai.getSiteSearch({
            content: searchObj.keyWord,
            pageSize: pageParams.pageSize,
            pageNum: pageParams.pageNum,
            orderByColumn: pageParams.orderByColumn,
            isAsc: pageParams.isAsc
          });
          console.log("站点搜索结果:", result);
          handleSiteSearchResult(result);
        } catch (error) {
          console.error("站点搜索失败:", error);
          searchObj.list.push({
            role: "assistant",
            content: "站点搜索请求失败，请稍后重试。"
          });
          scrollToBottom();
          searchObj.keyWord = "";
          loading.value = false;
        }
      } else {
        await getAIResponse(searchObj.keyWord);
      }
    };
    const getAIResponse = async (content) => {
      loading.value = true;
      isGenerating.value = true;
      const aiMessage = {
        role: "assistant",
        content: "正在思考中..."
      };
      searchObj.list.push(aiMessage);
      currentAiMessageIndex.value = searchObj.list.length - 1;
      scrollToBottom();
      if (eventSource.value) {
        try {
          eventSource.value.abort();
        } catch (e) {
          console.error("关闭上一个连接失败:", e);
        }
      }
      try {
        console.log("发送请求前状态检查 - currentImid:", currentImid.value);
        console.log("本地存储中的imid:", common_vendor.index.getStorageSync("ai_chat_imid"));
        const requestData = {
          content,
          imid: currentImid.value || null,
          // 第一次为null，后续使用服务器返回的id
          deepThinking: searchParams.deepThinking ? 1 : 0,
          netSearch: searchParams.netSearch ? 1 : 0
        };
        console.log("AI请求参数:", JSON.stringify(requestData));
        const task = common_vendor.index.request({
          url: "https://qlzhsq.qlzhsq.cn:30200/prod-api/ai/chat/completions",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json",
            Authorization: common_vendor.index.getStorageSync("token"),
            "clientid": "428a8310cd442757ae699df5d894f051",
            "Accept": "text/event-stream"
          },
          responseType: "arraybuffer",
          // 统一使用arraybuffer
          enableChunked: true,
          success: (res) => {
            console.log("SSE连接成功", res);
            if (res.header) {
              console.log("响应头:", JSON.stringify(res.header));
              if (res.header["x-session-id"] || res.header["x-conversation-id"]) {
                const headerImid = res.header["x-session-id"] || res.header["x-conversation-id"];
                console.log("从响应头提取到会话ID:", headerImid);
                if (!currentImid.value) {
                  currentImid.value = String(headerImid);
                  common_vendor.index.setStorageSync("ai_chat_imid", String(headerImid));
                }
              }
            }
            if (res.data) {
              let dataStr = "";
              if (typeof res.data === "string") {
                dataStr = res.data;
              } else if (res.data instanceof ArrayBuffer) {
                try {
                  const decoder = new TextDecoder("utf-8");
                  dataStr = decoder.decode(new Uint8Array(res.data));
                } catch (e) {
                  console.error("解码失败:", e);
                }
              } else if (typeof res.data === "object") {
                try {
                  dataStr = JSON.stringify(res.data);
                } catch (e) {
                  console.error("JSON序列化失败:", e);
                }
              }
              const numericIdRegex = /^\s*(\d{18,20})\s*$/;
              if (dataStr && numericIdRegex.test(dataStr.trim())) {
                const mainId = dataStr.trim().match(numericIdRegex);
                if (mainId && mainId[1]) {
                  console.log("从响应数据中提取到主ID:", mainId[1]);
                  currentImid.value = String(mainId[1]);
                  common_vendor.index.setStorageSync("ai_chat_imid", String(mainId[1]));
                  console.log("成功保存主ID:", currentImid.value);
                }
              }
            }
          },
          fail: (err) => {
            console.error("SSE连接失败", err);
            loading.value = false;
            isGenerating.value = false;
            searchObj.list[currentAiMessageIndex.value].content = "连接AI服务失败，请重试";
          }
        });
        eventSource.value = task;
        let buffer = "";
        task.onChunkReceived((res) => {
          try {
            console.log("收到数据块:", {
              type: typeof res.data,
              byteLength: res.data instanceof ArrayBuffer ? res.data.byteLength : "not an ArrayBuffer"
            });
            const mainIdPattern = /^\s*(\d{18,})\s*$/;
            if (typeof res.data === "string" && mainIdPattern.test(res.data.trim())) {
              const mainId = res.data.trim().match(mainIdPattern)[1];
              console.log("直接从响应提取到主ID:", mainId);
              if (!currentImid.value) {
                console.log("保存主ID到currentImid");
                currentImid.value = String(mainId);
                common_vendor.index.setStorageSync("ai_chat_imid", String(mainId));
                console.log("成功保存主ID:", currentImid.value);
              }
            }
            let chunkStr = "";
            if (typeof res.data === "string") {
              chunkStr = res.data;
              console.log("字符串数据处理成功", chunkStr.substring(0, 30));
            } else if (res.data instanceof ArrayBuffer) {
              try {
                const decoder = new TextDecoder("utf-8");
                chunkStr = decoder.decode(new Uint8Array(res.data));
                console.log("TextDecoder处理成功", chunkStr.substring(0, 30));
              } catch (error1) {
                console.error("TextDecoder失败:", error1);
                chunkStr = manualUtf8Decode(new Uint8Array(res.data));
                console.log("手动UTF-8解码处理成功", chunkStr.substring(0, 30));
              }
            } else if (res.data && typeof res.data === "object") {
              try {
                chunkStr = JSON.stringify(res.data);
                console.log("JSON对象序列化成功", chunkStr.substring(0, 30));
              } catch (jsonError) {
                console.error("JSON序列化失败:", jsonError);
                chunkStr = `[无法解析的对象数据]`;
              }
            } else {
              console.error("未知的数据类型:", typeof res.data);
              return;
            }
            console.log(
              "处理后数据:",
              "内容前30字符:",
              chunkStr.substring(0, 30).replace(/\n/g, "\\n")
            );
            const numericIdRegex = /\b(\d{18,20})\b/;
            const numericMatch = numericIdRegex.exec(chunkStr);
            if (numericMatch && numericMatch[1]) {
              console.log("从数据块中提取到数字ID:", numericMatch[1]);
              if (!currentImid.value) {
                console.log("保存数字ID到currentImid");
                currentImid.value = String(numericMatch[1]);
                common_vendor.index.setStorageSync("ai_chat_imid", String(numericMatch[1]));
                console.log("保存后的currentImid:", currentImid.value);
              }
            }
            if (chunkStr.includes('"id"') || chunkStr.includes('"imid"')) {
              try {
                const idPatterns = [
                  /"id"\s*:\s*"?([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})"?/,
                  // UUID格式
                  /"id"\s*:\s*"?(\d+)"?/,
                  // 纯数字ID
                  /"imid"\s*:\s*"?([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})"?/,
                  // UUID格式(imid)
                  /"imid"\s*:\s*"?(\d+)"?/
                  // 纯数字imid
                ];
                for (const pattern of idPatterns) {
                  const match = pattern.exec(chunkStr);
                  if (match && match[1]) {
                    console.log("从数据块直接提取到ID:", match[1]);
                    if (!currentImid.value) {
                      console.log("保存提取的ID到currentImid");
                      currentImid.value = String(match[1]);
                      common_vendor.index.setStorageSync("ai_chat_imid", String(match[1]));
                      console.log("保存后的currentImid:", currentImid.value);
                    }
                    break;
                  }
                }
              } catch (e) {
                console.error("从数据块提取ID失败:", e);
              }
            }
            buffer += chunkStr;
            buffer = processSSEBuffer(buffer, (newData, remainingBuffer) => {
              var _a2, _b, _c;
              if (newData === "[DONE]") {
                console.log("SSE流结束");
                loading.value = false;
                isGenerating.value = false;
                searchObj.keyWord = "";
                return;
              }
              try {
                console.log("处理前数据:", newData.substring(0, 50));
                let eventData;
                try {
                  eventData = JSON.parse(newData);
                  if ((_c = (_b = (_a2 = eventData.choices) == null ? void 0 : _a2[0]) == null ? void 0 : _b.delta) == null ? void 0 : _c.content) {
                    const newContent = eventData.choices[0].delta.content;
                    if (searchObj.list[currentAiMessageIndex.value].content === "正在思考中...") {
                      searchObj.list[currentAiMessageIndex.value].content = newContent;
                    } else {
                      const currentContent = searchObj.list[currentAiMessageIndex.value].content;
                      searchObj.list[currentAiMessageIndex.value].content = currentContent + newContent;
                    }
                    scrollToBottom();
                  }
                } catch (jsonError) {
                  console.error("JSON解析失败，尝试修复", jsonError);
                  if (newData.includes('"choices"') && newData.includes('"delta"')) {
                    const match = newData.match(/"delta":\s*{\s*"content":\s*"([^"]*)"}/);
                    if (match && match[1]) {
                      console.log("提取到delta内容:", match[1]);
                      const content2 = match[1].replace(/\\n/g, "\n").replace(/\\"/g, '"').replace(/\\\\/g, "\\");
                      if (searchObj.list[currentAiMessageIndex.value].content === "正在思考中...") {
                        searchObj.list[currentAiMessageIndex.value].content = content2;
                      } else {
                        const currentContent = searchObj.list[currentAiMessageIndex.value].content;
                        searchObj.list[currentAiMessageIndex.value].content = currentContent + content2;
                      }
                      scrollToBottom();
                    }
                  }
                }
              } catch (e) {
                console.error("解析JSON失败:", e, "原始数据:", newData);
              }
            });
          } catch (error) {
            console.error("处理数据块时出错:", (error == null ? void 0 : error.message) || error);
          }
        });
      } catch (error) {
        console.error("创建SSE连接失败:", error);
        loading.value = false;
        isGenerating.value = false;
        searchObj.list[currentAiMessageIndex.value].content = "创建AI连接失败: " + (error.message || "未知错误");
      }
    };
    const processSSEBuffer = (buffer, callback) => {
      const numericIdRegex = /^\s*(\d{18,20})\s*$/;
      const numericIdMatch = buffer.trim().match(numericIdRegex);
      if (numericIdMatch && numericIdMatch[1]) {
        console.log("processSSEBuffer 发现纯数字ID:", numericIdMatch[1]);
        currentImid.value = String(numericIdMatch[1]);
        common_vendor.index.setStorageSync("ai_chat_imid", String(numericIdMatch[1]));
        console.log("保存到currentImid:", currentImid.value);
        callback('{"type":"id_received","id":"' + numericIdMatch[1] + '"}', "");
        return "";
      }
      let eventEnd;
      while ((eventEnd = buffer.indexOf("\n\n")) !== -1) {
        const eventStr = buffer.substring(0, eventEnd).trim();
        buffer = buffer.substring(eventEnd + 2);
        if (!eventStr)
          continue;
        const eventParts = eventStr.split("\n");
        let data = "";
        for (const part of eventParts) {
          if (part.startsWith("data:")) {
            data = part.substring(5).trim();
          }
        }
        if (data) {
          callback(data, buffer);
        }
      }
      return buffer;
    };
    const manualUtf8Decode = (bytes) => {
      let str = "";
      let i = 0;
      try {
        while (i < bytes.length) {
          const byte1 = bytes[i++];
          if (byte1 < 128) {
            str += String.fromCharCode(byte1);
          } else if (byte1 >= 192 && byte1 < 224) {
            const byte2 = bytes[i++] & 63;
            str += String.fromCharCode((byte1 & 31) << 6 | byte2);
          } else if (byte1 >= 224 && byte1 < 240) {
            const byte2 = bytes[i++] & 63;
            const byte3 = bytes[i++] & 63;
            str += String.fromCharCode((byte1 & 15) << 12 | byte2 << 6 | byte3);
          } else if (byte1 >= 240) {
            const byte2 = bytes[i++] & 63;
            const byte3 = bytes[i++] & 63;
            const byte4 = bytes[i++] & 63;
            const codepoint = (byte1 & 7) << 18 | byte2 << 12 | byte3 << 6 | byte4;
            str += String.fromCharCode(
              55296 + (codepoint - 65536 >> 10),
              56320 + (codepoint - 65536 & 1023)
            );
          }
        }
      } catch (e) {
        console.error("手动UTF-8解码失败:", e);
        try {
          const chunkSize = 512;
          let result = "";
          for (let i2 = 0; i2 < bytes.length; i2 += chunkSize) {
            const chunk = bytes.slice(i2, Math.min(i2 + chunkSize, bytes.length));
            result += String.fromCharCode.apply(null, Array.from(chunk));
          }
          str = result;
        } catch (e2) {
          console.error("备用解码方法也失败:", e2);
          str = Array.from(bytes).map((b) => String.fromCharCode(b)).join("");
        }
      }
      return str;
    };
    const backToChat = () => {
      showSiteSearchResults.value = false;
      setTimeout(() => {
        getScreenHeight();
        common_vendor.nextTick$1(() => {
          scrollToBottom();
          setTimeout(() => {
            scrollToBottom();
          }, 300);
        });
      }, 50);
    };
    common_vendor.onLoad(() => {
      console.log("AI页面加载");
      getScreenHeight();
      setTimeout(() => {
        getScreenHeight();
      }, 200);
      setTimeout(() => {
        getScreenHeight();
      }, 500);
      currentImid.value = "";
      common_vendor.index.removeStorageSync("ai_chat_imid");
      console.log("页面加载时清除imid，准备开始新对话");
      searchParams.deepThinking = false;
      searchParams.netSearch = false;
      searchParams.sitesearch = true;
      searchObj.keyWord = "";
      common_api_ai.getChatList({
        pageSize: pageParams.pageSize,
        pageNum: pageParams.pageNum,
        orderByColumn: pageParams.orderByColumn,
        isAsc: pageParams.isAsc
      }).then((result) => {
        if (result.code === 200 && result.rows) {
          chatHistoryList.value = result.rows || [];
          console.log("静默加载聊天历史记录列表:", chatHistoryList.value.length);
          searchObj.list = [];
          searchObj.list.push({
            role: "assistant",
            content: "你好，我是智能服务助手小智，请问有什么可以帮助您？可以点击顶部菜单查看历史对话记录。当前处于站点搜索模式。"
          });
          common_vendor.nextTick$1(() => {
            getScreenHeight();
            setTimeout(() => {
              scrollToBottom();
              setTimeout(() => {
                scrollToBottom();
              }, 300);
            }, 300);
          });
        }
      }).catch((error) => {
        console.error("获取聊天列表失败:", error);
        searchObj.list = [];
        searchObj.list.push({
          role: "assistant",
          content: "你好，我是智能服务助手小智，请问有什么可以帮助您？当前处于站点搜索模式。"
        });
        common_vendor.nextTick$1(() => {
          getScreenHeight();
          setTimeout(() => {
            scrollToBottom();
          }, 300);
        });
      });
      common_vendor.index.onWindowResize(() => {
        console.log("窗口大小变化，重新计算高度");
        getScreenHeight();
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      });
    });
    const scrollToBottom = () => {
      common_vendor.nextTick$1(() => {
        try {
          scrollTopValue.value = 999999;
          setTimeout(() => {
            const query = common_vendor.index.createSelectorQuery();
            query.select(`#${scrollViewId.value}`).boundingClientRect();
            query.selectAll(".flex_row_end, .answer").boundingClientRect();
            query.exec((res) => {
              if (res && res[0] && res[1] && res[1].length > 0) {
                let totalHeight = 0;
                res[1].forEach((item) => {
                  totalHeight += item.height + 5;
                });
                scrollTopValue.value = totalHeight + 2e3;
                common_vendor.index.pageScrollTo({
                  scrollTop: 999999,
                  duration: 100
                });
              }
            });
          }, 50);
          const scrollTimings = [200, 400, 600, 1e3];
          scrollTimings.forEach((timing) => {
            setTimeout(() => {
              scrollTopValue.value = 9999999;
            }, timing);
          });
        } catch (e) {
          console.error("滚动操作失败:", e);
          scrollTopValue.value = 999999;
        }
      });
    };
    const loadHistoryMessages = async (item) => {
      console.log("加载历史消息:", item);
      if (!item || !item.id) {
        console.error("历史记录项缺少ID:", item);
        common_vendor.index.showToast({
          title: "无法加载对话记录",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "加载历史对话中"
        });
        const result = await common_api_ai.getMessageList(item.id);
        console.log("获取到的历史消息数据:", result);
        if (result.code === 200 && (result.rows || result.data)) {
          const messages = result.rows || result.data || [];
          console.log("历史消息列表:", messages);
          searchObj.list = [];
          currentImid.value = item.id;
          common_vendor.index.setStorageSync("ai_chat_imid", item.id);
          console.log("已保存会话ID到本地存储:", item.id);
          if (Array.isArray(messages) && messages.length > 0) {
            messages.sort((a, b) => {
              if (a.createTime && b.createTime) {
                return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
              }
              if (a.orderNum !== void 0 && b.orderNum !== void 0) {
                return a.orderNum - b.orderNum;
              }
              return 0;
            });
            let userCount = 0;
            let assistantCount = 0;
            messages.forEach((msg, index) => {
              let role = msg.role || msg.sender || msg.type;
              if (!role) {
                if (msg.fromUser || msg.isUser || msg.userId) {
                  role = "user";
                } else if (msg.fromAssistant || msg.isAssistant || msg.assistantId) {
                  role = "assistant";
                } else if (msg.content && typeof msg.content === "string") {
                  if (msg.content.includes("你好，我是") || msg.content.includes("感谢您的提问") || msg.content.includes("很高兴为您服务")) {
                    role = "assistant";
                  } else {
                    role = "user";
                  }
                } else {
                  role = index % 2 === 0 ? "user" : "assistant";
                }
              }
              if (role === "ai" || role === "assistant" || role === "system" || role === "ASSISTANT") {
                role = "assistant";
                assistantCount++;
              } else {
                role = "user";
                userCount++;
              }
              let content = msg.content || msg.message || msg.text || "";
              if (typeof content === "string" && (content.startsWith("{") || content.startsWith("data:"))) {
                try {
                  let jsonContent = content;
                  if (content.startsWith("data:")) {
                    jsonContent = content.substring(5).trim();
                  }
                  if (jsonContent.startsWith("{") && jsonContent.endsWith("}")) {
                    const parsed = JSON.parse(jsonContent);
                    if (parsed.content) {
                      content = parsed.content;
                    } else if (parsed.message && parsed.message.content) {
                      content = parsed.message.content;
                    } else if (parsed.choices && parsed.choices.length > 0) {
                      const choice = parsed.choices[0];
                      if (choice.text) {
                        content = choice.text;
                      } else if (choice.message && choice.message.content) {
                        content = choice.message.content;
                      }
                    }
                  }
                } catch (e) {
                  console.log("JSON解析失败，使用原始内容:", e);
                  content = sanitizeResponse(content);
                }
              }
              if (content && typeof content === "string" && content.trim()) {
                searchObj.list.push({
                  role,
                  content: content.trim()
                });
              }
            });
            if (assistantCount > 0 && userCount === 0) {
              searchObj.list.unshift({
                role: "user",
                content: item.title || "历史对话"
              });
            }
            if (searchObj.list.length === 0) {
              searchObj.list.push({
                role: "assistant",
                content: "无法加载历史对话内容，您可以继续发送新消息。"
              });
            }
          } else {
            searchObj.list.push({
              role: "assistant",
              content: "没有找到历史对话内容，您可以开始新的对话。"
            });
          }
          isPopupVisible.value = false;
          setTimeout(() => {
            isPopupShow.value = false;
            setTimeout(() => {
              scrollToBottom();
              setTimeout(() => {
                scrollToBottom();
              }, 300);
            }, 200);
          }, 350);
        } else {
          common_vendor.index.showToast({
            title: "加载历史对话失败",
            icon: "none"
          });
          console.error("加载历史对话失败:", result.message || "未知错误");
          if (currentImid.value) {
            console.log("加载失败，维持当前会话ID:", currentImid.value);
          } else {
            searchObj.list.push({
              role: "assistant",
              content: "加载历史对话失败，请重试或开始新的对话。"
            });
          }
          closePopup();
        }
      } catch (error) {
        console.error("加载历史对话出错:", error);
        common_vendor.index.showToast({
          title: "加载历史对话出错",
          icon: "none"
        });
        if (currentImid.value) {
          console.log("加载出错，维持当前会话ID:", currentImid.value);
        } else {
          searchObj.list.push({
            role: "assistant",
            content: "加载历史对话出错，请重试或开始新的对话。"
          });
        }
        closePopup();
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const scrollTopValue = common_vendor.ref(0);
    const onScrollEvent = (e) => {
    };
    const sanitizeResponse = (text) => {
      var _a2, _b, _c, _d;
      if (!text)
        return "";
      if (typeof text === "object" && text !== null) {
        if (text.content)
          return String(text.content);
        if (text.text)
          return String(text.text);
        if (text.choices && text.choices.length > 0) {
          const choice = text.choices[0];
          if ((_a2 = choice.delta) == null ? void 0 : _a2.content)
            return String(choice.delta.content);
          if ((_b = choice.message) == null ? void 0 : _b.content)
            return String(choice.message.content);
          if (choice.text)
            return String(choice.text);
        }
        try {
          text = JSON.stringify(text);
        } catch (e) {
          return String(text || "");
        }
      }
      text = String(text);
      if (text.startsWith("data:")) {
        const content = text.substring(5).trim();
        if (content === "[DONE]")
          return "";
        return sanitizeResponse(content);
      }
      if (text.trim().startsWith("{") && text.trim().endsWith("}")) {
        try {
          const json = JSON.parse(text);
          if (json.content)
            return json.content;
          if (json.choices && json.choices.length > 0) {
            const choice = json.choices[0];
            if ((_c = choice.delta) == null ? void 0 : _c.content)
              return choice.delta.content;
            if ((_d = choice.message) == null ? void 0 : _d.content)
              return choice.message.content;
            if (choice.text)
              return choice.text;
          }
        } catch (e) {
          const contentMatch2 = /"content"\s*:\s*"([^"]+)"/.exec(text);
          if (contentMatch2 == null ? void 0 : contentMatch2[1]) {
            return contentMatch2[1].replace(/\\"/g, '"').replace(/\\n/g, "\n");
          }
          const deltaMatch = /"delta"\s*:\s*{\s*"content"\s*:\s*"([^"]+)"/.exec(text);
          if (deltaMatch == null ? void 0 : deltaMatch[1]) {
            return deltaMatch[1].replace(/\\"/g, '"').replace(/\\n/g, "\n");
          }
        }
      }
      const contentMatch = /"content"\s*:\s*"([^"]+)"/.exec(text);
      if (contentMatch == null ? void 0 : contentMatch[1]) {
        return contentMatch[1].replace(/\\"/g, '"').replace(/\\n/g, "\n");
      }
      return text.replace(/\\"/g, '"').replace(/\\n/g, "\n").replace(/\\t/g, "	").replace(/\\r/g, "").replace(/\\\\/g, "\\").replace(/^data:/, "").replace(/\*\*202\d/g, "2023");
    };
    const formatMarkdown = (content) => {
      if (!content)
        return "";
      try {
        content = content.replace(/```([\s\S]*?)```/g, (match, codeBlock) => {
          let firstLine = codeBlock.trim().split("\n")[0];
          let language = firstLine.trim();
          let code = codeBlock;
          if (language && !language.includes(" ") && language.length < 20) {
            code = codeBlock.replace(firstLine, "").trim();
          } else {
            code = codeBlock.trim();
            language = "代码";
          }
          let formattedCode = "";
          const lines = code.split("\n");
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const formattedLine = line.replace(/  /g, "　　");
            formattedCode += formattedLine;
            if (i < lines.length - 1) {
              formattedCode += "\n";
            }
          }
          return `
【${language}】
${formattedCode}
`;
        });
        content = content.replace(/\|(.+)\|[\s\n]*\|([\s-:]+)\|[\s\n]*((?:\|.+\|[\s\n]*)+)/g, (match) => {
          return `
【表格内容】
${match}
`;
        });
        content = content.replace(/^(\d+)\.\s+(.+)$/gm, "$1. $2");
        content = content.replace(/^[*\-+]\s+(.+)$/gm, "• $1");
        content = content.replace(/\*\*([^*]+)\*\*/g, "$1");
        content = content.replace(/\*([^*]+)\*/g, "$1");
        content = content.replace(/\n\n/g, "\n \n");
        return content;
      } catch (e) {
        console.error("格式化Markdown失败:", e);
        return content;
      }
    };
    const stopGeneration = () => {
      console.log("用户停止生成");
      if (eventSource.value) {
        try {
          eventSource.value.abort();
          console.log("成功中断请求");
          eventSource.value = null;
        } catch (e) {
          console.error("中断请求失败:", e);
        }
      }
      loading.value = false;
      isGenerating.value = false;
      if (searchObj.list.length > 0 && currentAiMessageIndex.value >= 0) {
        const currentContent = searchObj.list[currentAiMessageIndex.value].content;
        if (currentContent === "正在思考中..." || currentContent === "") {
          searchObj.list[currentAiMessageIndex.value].content = "生成已被终止。";
        } else {
          searchObj.list[currentAiMessageIndex.value].content += "\n\n[生成已被用户终止]";
        }
      }
      scrollToBottom();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(showPopup),
        b: !showSiteSearchResults.value
      }, !showSiteSearchResults.value ? {
        c: common_vendor.f(common_vendor.unref(searchObj).list, (item, i, i0) => {
          return common_vendor.e({
            a: item.role === "user"
          }, item.role === "user" ? {
            b: common_vendor.t(item.content)
          } : {}, {
            c: item.role === "user"
          }, item.role === "user" ? {} : {}, {
            d: `msg-${i}`,
            e: item.role === "assistant"
          }, item.role === "assistant" ? common_vendor.e({
            f: item.content
          }, item.content ? {
            g: common_vendor.t(formatMarkdown(item.content))
          } : {}, {
            h: `msg-${i}`
          }) : {}, {
            i
          });
        })
      } : {}, {
        d: showSiteSearchResults.value
      }, showSiteSearchResults.value ? common_vendor.e({
        e: siteSearchResults.value && siteSearchResults.value.length > 0
      }, siteSearchResults.value && siteSearchResults.value.length > 0 ? {
        f: common_vendor.f(siteSearchResults.value, (result, resultIndex, i0) => {
          return common_vendor.e({
            a: result.type != "7"
          }, result.type != "7" ? {
            b: result.avatar
          } : {}, {
            c: common_vendor.t(result.title || "搜索结果"),
            d: result.type != "7"
          }, result.type != "7" ? {
            e: common_vendor.t(common_vendor.unref(areaStore).areaName)
          } : {}, {
            f: common_vendor.t(result.description),
            g: resultIndex,
            h: common_vendor.o(($event) => openSiteResult(result), resultIndex)
          });
        })
      } : {}, {
        g: common_vendor.o(backToChat)
      }) : {}, {
        h: scrollViewId.value,
        i: scrollTopValue.value,
        j: common_vendor.o(onScrollEvent),
        k: common_vendor.unref(scrollHeight) + "px",
        l: searchParams.deepThinking ? 1 : "",
        m: common_vendor.o(($event) => toggleSearchOption("deepThinking")),
        n: searchParams.sitesearch,
        o: searchParams.netSearch ? 1 : "",
        p: common_vendor.o(($event) => toggleSearchOption("netSearch")),
        q: searchParams.sitesearch,
        r: searchParams.sitesearch ? 1 : "",
        s: common_vendor.o(toggleSiteSearch),
        t: common_vendor.o(handleSearch),
        v: common_vendor.unref(searchObj).keyWord,
        w: common_vendor.o(($event) => common_vendor.unref(searchObj).keyWord = $event.detail.value),
        x: !common_vendor.unref(isGenerating)
      }, !common_vendor.unref(isGenerating) ? {
        y: common_vendor.o(($event) => handleSearch())
      } : {
        z: common_assets._imports_0,
        A: common_vendor.o(($event) => stopGeneration())
      }, {
        B: isPopupShow.value
      }, isPopupShow.value ? common_vendor.e({
        C: common_vendor.o(closePopup),
        D: common_vendor.o(closePopup),
        E: chatHistoryList.value.length > 0
      }, chatHistoryList.value.length > 0 ? {
        F: common_vendor.f(chatHistoryList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: index,
            c: `${0.1 + index * 0.05}s`,
            d: common_vendor.o(($event) => loadHistoryMessages(item), index)
          };
        }),
        G: isPopupVisible.value ? 1 : ""
      } : {}, {
        H: isPopupVisible.value ? 1 : "",
        I: isPopupVisible.value ? 1 : ""
      }) : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/ai/ai.vue"]]);
wx.createPage(MiniProgramPage);
