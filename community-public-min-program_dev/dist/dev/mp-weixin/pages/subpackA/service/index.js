"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_index = require("../../../common/api/index.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  (_easycom_uv_swiper2 + _easycom_uv_icon2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_icon)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const current1 = common_vendor.ref(0);
    common_vendor.ref(["/static/index/sw1.png", "/static/index/sw2.png", "/static/index/sw3.png"]);
    common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const bannerList = common_vendor.ref([]);
    const bannerDesc = common_vendor.ref();
    const getBanner = async () => {
      bannerList.value = [];
      const res = await common_api_index.getBannerList();
      if ((res == null ? void 0 : res.code) === 200) {
        bannerList.value = res.data;
      } else {
        throw console.log(res);
      }
    };
    const getConfigSetting = async () => {
      const res = await common_api_index.getConfig({ code: "applet_home_label" });
      if ((res == null ? void 0 : res.code) === 200) {
        bannerDesc.value = res.data.configUrl;
      } else {
        throw console.log(res);
      }
    };
    common_vendor.onLoad(() => {
      getBanner();
      getConfigSetting();
      getList();
    });
    const toGuide = () => {
      common_vendor.index.navigateTo({
        url: "/pages/subpackA/governmentAffairs/index"
      });
    };
    function getList() {
      common_api_index.getEasyServiceList().then((res) => {
        dataList.value = res.data;
      });
    }
    function toPage(link) {
      if (link == null || Object.keys(link).length == 1)
        return;
      if (link.url && (link.url.indexOf("https") != -1 || link.url.indexOf("http") != -1)) {
        const h5Url = `/pages/index/h5/h5?url=${encodeURIComponent(link.url)}`;
        common_vendor.index.navigateTo({
          url: h5Url
        });
      } else if (link.appid) {
        common_vendor.index.navigateToMiniProgram({
          appId: link.appid,
          path: link.page,
          envVersion: "release",
          // 开发版、体验版或正式版
          success(res) {
            console.log("跳转到其他小程序成功！", res);
          },
          fail(err) {
            console.error("跳转到其他小程序失败！", err);
          }
        });
      } else if (link.mobile) {
        common_vendor.index.makePhoneCall({
          phoneNumber: link.mobile,
          success: (res) => {
          },
          fail: (res) => {
          }
        });
      }
    }
    return (_ctx, _cache) => {
      return {
        a: bannerDesc.value,
        b: common_vendor.f(bannerList.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.n(index === current1.value && "indicator__dot--active")
          };
        }),
        c: common_vendor.o((e) => current1.value = e.current),
        d: common_vendor.p({
          height: "156",
          list: bannerList.value,
          keyName: "bannerImg",
          autoplay: false
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.remark),
            d: "d87944a2-1-" + i0,
            e: index,
            f: common_vendor.o(($event) => toPage(item), index)
          };
        }),
        f: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        g: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        h: common_vendor.o(toGuide)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/service/index.vue"]]);
wx.createPage(MiniProgramPage);
