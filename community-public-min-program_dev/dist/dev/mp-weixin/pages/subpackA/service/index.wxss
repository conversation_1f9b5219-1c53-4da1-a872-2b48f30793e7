/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.indicator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: #000000;
  opacity: 0.7;
  padding: 2px;
  border-radius: 6px;
}
.indicator__dot {
  height: 6px;
  width: 6px;
  border-radius: 100px;
  background-color: rgba(255, 255, 255, 0.35);
  margin: 0 3px;
  transition: background-color 0.3s;
}
.indicator__dot--active {
  background-color: #ffffff;
}
.indicator-num {
  padding: 2px 0;
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.indicator-num__text {
  color: #FFFFFF;
  font-size: 12px;
}
 .uv-swiper {
  background-color: rgba(255, 0, 0, 0) !important;
}
.bg {
  background: #fbf7ef;
}
.float_img {
  position: absolute;
  top: 75rpx;
  left: 20rpx;
  width: 461rpx;
  height: 130rpx;
  z-index: 1;
}
.menu {
  background: white;
  border-radius: 50rpx 50rpx 0 0;
}
.menu .menu-item {
  display: flex;
  justify-content: space-between;
  margin: 0 10rpx;
  padding: 30rpx 10rpx;
  border-bottom: 2rpx solid #eee;
  color: #333;
  position: relative;
}
.menu .menu-item:last-of-type {
  border-bottom: none;
}
.menu .menu-item > view {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.menu .menu-item > view .remark {
  margin-left: 20rpx;
  font-size: 25rpx;
  color: #999999;
  padding-top: 5rpx;
}
.menu .menu-item > view text {
  margin-left: 20rpx;
}
.menu .menu-item > view image {
  width: 80rpx;
  height: 80rpx;
}