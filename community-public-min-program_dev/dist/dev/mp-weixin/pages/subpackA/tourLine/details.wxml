<view class="page-container data-v-9e7bd850"><view class="gps-map-container data-v-9e7bd850"><map class="data-v-9e7bd850" id="gps-track-map" latitude="{{a}}" longitude="{{b}}" scale="{{c}}" markers="{{d}}" polyline="{{e}}" show-location="{{true}}" enable-3D="false" show-compass="true" enable-overlooking="false" enable-zoom="true" enable-scroll="true" enable-rotate="false" style="width:100%;height:600rpx" bindmarkertap="{{f}}" bindtap="{{g}}" bindregionchange="{{h}}"></map><view class="location-btn data-v-9e7bd850" bindtap="{{i}}"><image src="/static/tourLine/person_map.png" class="location-icon data-v-9e7bd850"></image></view></view><view wx:if="{{j}}" class="photo-detail-modal data-v-9e7bd850" bindtap="{{s}}"><view class="photo-modal-content data-v-9e7bd850" catchtap="{{r}}"><view class="photo-modal-header data-v-9e7bd850"><text class="photo-modal-title data-v-9e7bd850">位置详情</text><view class="photo-close-btn data-v-9e7bd850" bindtap="{{k}}">×</view></view><view wx:if="{{l}}" class="photo-modal-body data-v-9e7bd850"><view wx:if="{{m}}" class="photo-container data-v-9e7bd850"><image src="{{n}}" mode="aspectFit" class="main-photo data-v-9e7bd850" bindtap="{{o}}" binderror="{{p}}" bindload="{{q}}"></image><view class="photo-overlay data-v-9e7bd850"><text class="photo-tip data-v-9e7bd850">点击查看大图</text></view></view></view></view></view><view class="header-section data-v-9e7bd850"><view class="title-area data-v-9e7bd850"><text class="tour-title data-v-9e7bd850">{{t}}</text></view><view class="trip-info data-v-9e7bd850"><text class="trip-text data-v-9e7bd850">总行程{{v}}天</text><text class="spots-text data-v-9e7bd850">推荐地{{w}}处</text><text class="area-text data-v-9e7bd850">{{x}}</text></view></view><view class="attractions-section data-v-9e7bd850"><view class="attraction-list data-v-9e7bd850"><text class="intro-text data-v-9e7bd850">简介</text><view class="attraction-list-content data-v-9e7bd850"><view wx:for="{{y}}" wx:for-item="attraction" wx:key="c" bindtap="{{attraction.d}}" class="{{['attraction-item', 'data-v-9e7bd850', attraction.e && 'selected']}}"><view class="attraction-image data-v-9e7bd850"><image class="data-v-9e7bd850" src="{{attraction.a}}" mode="aspectFill"></image></view><view class="attraction-info data-v-9e7bd850"><text class="attraction-name data-v-9e7bd850">{{attraction.b}}</text></view></view></view></view><view class="attraction-text data-v-9e7bd850"><text class="data-v-9e7bd850">{{z}}</text><button wx:if="{{A}}" class="guide-btn data-v-9e7bd850" bindtap="{{B}}">查看路线</button></view></view></view>