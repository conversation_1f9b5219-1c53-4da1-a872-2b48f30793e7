/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #f8f8f8;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-9e7bd850 {
  min-height: 100vh;
  background: #f8f8f8;
}
.guide-btn.data-v-9e7bd850 {
  background: #2bbc4a;
  color: #fff;
  border-radius: 8rpx;
  padding: 8rpx 14rpx;
  margin-top: 30rpx;
  font-size: 28rpx;
}
.map-container.data-v-9e7bd850 {
  position: relative;
  width: 100%;
  height: 700rpx;
  background: #f0f0f0;
}
.gps-map-container.data-v-9e7bd850 {
  position: relative;
  width: 100%;
  background: #ffffff;
  overflow: hidden;
}
.gps-map-container .map-title.data-v-9e7bd850 {
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  text-align: center;
}
.gps-map-container .map-title .title-text.data-v-9e7bd850 {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}
.gps-map-container .map-title .subtitle-text.data-v-9e7bd850 {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}
.gps-map-container .map-info.data-v-9e7bd850 {
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 2rpx solid #e9ecef;
}
.gps-map-container .map-info .info-item.data-v-9e7bd850 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.gps-map-container .map-info .info-item.data-v-9e7bd850:last-child {
  margin-bottom: 0;
}
.gps-map-container .map-info .info-item .info-label.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.gps-map-container .map-info .info-item .info-value.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.map-empty.data-v-9e7bd850 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 400rpx;
  background: #f8f8f8;
}
.map-empty .empty-text.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #999;
}

/* 拍摄点图片弹窗样式 */
.photo-detail-modal.data-v-9e7bd850 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.photo-modal-content.data-v-9e7bd850 {
  background-color: #fff;
  border-radius: 20rpx;
  width: 95%;
  max-width: 650rpx;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25);
  animation: photoModalFadeIn-9e7bd850 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}
@keyframes photoModalFadeIn-9e7bd850 {
from {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
}
to {
    opacity: 1;
    transform: scale(1) translateY(0);
}
}
.photo-modal-header.data-v-9e7bd850 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  background: #FFCC35;
  color: white;
}
.photo-modal-title.data-v-9e7bd850 {
  font-size: 32rpx;
  font-weight: bold;
}
.photo-close-btn.data-v-9e7bd850 {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: background-color 0.3s;
}
.photo-close-btn.data-v-9e7bd850:active {
  background-color: rgba(255, 255, 255, 0.3);
}
.photo-modal-body.data-v-9e7bd850 {
  max-height: 80vh;
  overflow-y: auto;
}
.photo-container.data-v-9e7bd850 {
  position: relative;
  width: 100%;
  background: #f8f9fa;
}
.main-photo.data-v-9e7bd850 {
  width: 100%;
  height: 400rpx;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
}
.photo-overlay.data-v-9e7bd850 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 20rpx 20rpx;
  text-align: center;
}
.photo-tip.data-v-9e7bd850 {
  color: white;
  font-size: 24rpx;
  opacity: 0.9;
}
.gps-info-card.data-v-9e7bd850 {
  padding: 30rpx;
  background: white;
}
.attraction-info-card.data-v-9e7bd850 {
  padding: 30rpx;
  background: white;
  border-top: 1px solid #f0f0f0;
}
.info-row.data-v-9e7bd850 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.info-row.data-v-9e7bd850:last-child {
  border-bottom: none;
}
.info-icon.data-v-9e7bd850 {
  width: 60rpx;
  height: 60rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}
.info-content.data-v-9e7bd850 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.info-label.data-v-9e7bd850 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.info-value.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}
.header-section.data-v-9e7bd850 {
  background: #ffffff;
  padding: 30rpx 30rpx 20rpx 30rpx;
  margin-bottom: 20rpx;
}
.title-area.data-v-9e7bd850 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.title-area .tour-title.data-v-9e7bd850 {
  font-size: 36rpx;
  text-align: center;
  color: #333;
  flex: 1;
}
.share-btn.data-v-9e7bd850 {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}
.share-btn.data-v-9e7bd850::after {
  border: none;
}
.share-btn .icon.data-v-9e7bd850 {
  width: 40rpx;
  height: 40rpx;
}
.trip-info.data-v-9e7bd850 {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.trip-info .trip-text.data-v-9e7bd850, .trip-info .spots-text.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #666;
}
.trip-info .area-text.data-v-9e7bd850 {
  font-size: 24rpx;
  color: #999;
}
.intro-section .intro-btn.data-v-9e7bd850 {
  display: inline-block;
  background: #e8f4e8;
  border: 2rpx solid #4caf50;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
}
.intro-section .intro-content.data-v-9e7bd850 {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}
.intro-section .intro-content .intro-desc.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.attraction-list-content.data-v-9e7bd850 {
  height: 500rpx;
  width: 200rpx;
  overflow-y: auto;
}
.attractions-section.data-v-9e7bd850 {
  padding: 0 30rpx;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
}
.attractions-section .attraction-list .intro-text.data-v-9e7bd850 {
  color: #2bbc4a;
  border: 2rpx solid #2bbc4a;
  font-size: 28rpx;
  width: 100%;
  color: #4caf50;
  width: 195rpx;
  height: 60rpx;
  text-align: center;
  display: block;
  line-height: 60rpx;
  margin-bottom: 10rpx;
}
.attractions-section .attraction-text.data-v-9e7bd850 {
  margin-left: 24rpx;
  flex: 1;
}
.attractions-section .attraction-text text.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #333;
}
.attraction-item.data-v-9e7bd850 {
  display: flex;
  width: 200rpx;
  align-items: center;
  position: relative;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  cursor: pointer;
}
.attraction-item.selected.data-v-9e7bd850 {
  border: 3rpx solid #ffffff;
}
.attraction-item.selected .attraction-name.data-v-9e7bd850 {
  background: #19993E !important;
  border: 2rpx solid #ffffff !important;
}
.attraction-item .attraction-image.data-v-9e7bd850 {
  width: 200rpx;
}
.attraction-item .attraction-image image.data-v-9e7bd850 {
  width: 100%;
  height: 160rpx;
  border-radius: 8rpx;
}
.attraction-item .attraction-info.data-v-9e7bd850 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 100%;
  text-align: center;
  transform: translate(-50%, -50%);
}
.attraction-item .attraction-info .attraction-name.data-v-9e7bd850 {
  font-size: 28rpx;
  color: #fff;
  text-align: center;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 地图标记动画样式 */
@keyframes markerBounce-9e7bd850 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-10rpx);
}
}
@keyframes markerPulse-9e7bd850 {
0% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.2);
    opacity: 0.8;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.marker-animation.data-v-9e7bd850 {
  animation: markerBounce-9e7bd850 0.6s ease-in-out infinite,markerPulse-9e7bd850 1.2s ease-in-out infinite;
}
.attraction-info .attraction-name.data-v-9e7bd850 {
  color: #ffffff !important;
  font-weight: bold;
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
}