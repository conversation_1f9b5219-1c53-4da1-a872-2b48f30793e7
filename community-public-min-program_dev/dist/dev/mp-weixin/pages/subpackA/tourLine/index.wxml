<z-paging wx:if="{{i}}" class="r data-v-c6745cf4" u-s="{{['top','d']}}" u-r="paging" bindquery="{{g}}" u-i="c6745cf4-0" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"><view slot="top"><view class="data-v-c6745cf4" style="position:relative"><uv-swiper wx:if="{{a}}" class="data-v-c6745cf4" u-i="c6745cf4-1,c6745cf4-0" bind:__l="__l" u-p="{{a}}"></uv-swiper><view class="district-selector data-v-c6745cf4"><uv-button wx:if="{{d}}" class="data-v-c6745cf4" u-s="{{['d']}}" bindclick="{{c}}" u-i="c6745cf4-2,c6745cf4-0" bind:__l="__l" u-p="{{d}}">{{b}}</uv-button></view></view></view><view wx:for="{{e}}" wx:for-item="item" wx:key="e" class="list data-v-c6745cf4" bindtap="{{item.f}}"><view class="imgList data-v-c6745cf4"><image wx:for="{{item.a}}" wx:for-item="attraction" wx:key="a" class="data-v-c6745cf4" mode="aspectFill" src="{{attraction.b}}"></image></view><view class="info data-v-c6745cf4"><view class="title data-v-c6745cf4">{{item.b}}</view><view class="desc data-v-c6745cf4" style="display:flex"><text class="data-v-c6745cf4">总行程{{item.c}}天 推荐地{{item.d}}处</text></view></view></view></z-paging><uv-picker wx:if="{{m}}" class="r data-v-c6745cf4" u-r="districtPickerRef" bindconfirm="{{k}}" bindcancel="{{l}}" u-i="c6745cf4-3" bind:__l="__l" u-p="{{m}}"></uv-picker>