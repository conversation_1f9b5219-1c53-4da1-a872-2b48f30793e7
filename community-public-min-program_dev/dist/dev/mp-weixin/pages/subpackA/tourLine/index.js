"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_tourLine = require("../../../common/api/tourLine.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_swiper2 + _easycom_uv_button2 + _easycom_z_paging2 + _easycom_uv_picker2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_uv_button + _easycom_z_paging + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const list = common_vendor.ref([]);
    const districtPickerRef = common_vendor.ref();
    const districtColumns = common_vendor.ref([]);
    const selectedDistrictName = common_vendor.ref("邛崃市");
    const selectedDistrictCode = common_vendor.ref("");
    function itemClick(item) {
      const url = "/pages/subpackA/tourLine/details?id=" + item.tourLineId;
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const attractionName = common_vendor.ref("");
    function getDataList(offset, count) {
      const districtCode = selectedDistrictCode.value || common_vendor.index.getStorageSync("areaCode");
      common_api_tourLine.getPageList({
        pageNum: offset,
        pageSize: count,
        districtCode,
        // 使用选中的辖区代码
        sortType: 1,
        // 1-最新, 可根据需要调整
        attractionName: attractionName.value
        // 添加搜索关键词
      }).then((res) => {
        console.log("getPageList返回数据:", res);
        if (res.data && res.data.records && Array.isArray(res.data.records)) {
          const tourLines = res.data.records;
          if (list.value.length === 0 && tourLines.length > 0) {
            const firstTourLine = tourLines[0];
            if (firstTourLine.attractionsList && firstTourLine.attractionsList.length > 0) {
              const firstAttraction = firstTourLine.attractionsList[0];
              if (firstAttraction.attractionImages) {
                list.value.push(firstAttraction.attractionImages);
              }
            }
          }
          if (res.data.total > offset * count || offset < 2) {
            paging.value.complete(tourLines);
          } else {
            paging.value.complete([]);
          }
        } else {
          console.log("数据结构不符合预期，请检查接口返回格式");
          paging.value.complete([]);
        }
      }).catch((error) => {
        console.error("获取景点列表失败:", error);
        paging.value.complete([]);
      });
    }
    function showDistrictPicker() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      common_api_tourLine.getDistrictData({}).then((res) => {
        common_vendor.index.hideLoading();
        console.log("获取辖区数据:", res);
        if (res && res.data) {
          const communities = [];
          communities.push({
            text: "全部",
            value: "",
            code: ""
          });
          if (res.data.district && Array.isArray(res.data.district)) {
            res.data.district.forEach((item) => {
              if (item.name && item.name.includes("社区")) {
                communities.push({
                  text: item.name,
                  value: item.code,
                  code: item.code
                });
              }
            });
          }
          if (res.data.hotCommunity && Array.isArray(res.data.hotCommunity)) {
            res.data.hotCommunity.forEach((item) => {
              if (item.districtName && item.districtName.includes("社区")) {
                communities.push({
                  text: item.districtName,
                  value: item.districtCode,
                  code: item.districtCode
                });
              }
            });
          }
          districtColumns.value = [communities];
          districtPickerRef.value.open();
        } else {
          common_vendor.index.showToast({
            title: "获取辖区数据失败",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.hideLoading();
        console.error("获取辖区数据失败:", error);
        common_vendor.index.showToast({
          title: "获取辖区数据失败",
          icon: "none"
        });
      });
    }
    function onDistrictConfirm(event) {
      console.log("选择的辖区:", event);
      const selectedItem = event.value[0];
      selectedDistrictName.value = selectedItem.text;
      selectedDistrictCode.value = selectedItem.code;
      paging.value.reload();
    }
    function onDistrictCancel() {
      console.log("取消选择辖区");
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          height: "500rpx",
          list: list.value
        }),
        b: common_vendor.t(selectedDistrictName.value || "切换辖区"),
        c: common_vendor.o(showDistrictPicker),
        d: common_vendor.p({
          type: "primary",
          size: "small",
          customStyle: {
            backgroundColor: "#FF9F18",
            borderRadius: "20rpx",
            fontSize: "28rpx",
            padding: "10rpx 20rpx"
          }
        }),
        e: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.f((item.attractionsList || []).slice(0, 3), (attraction, idx, i1) => {
              return {
                a: idx,
                b: attraction == null ? void 0 : attraction.attractionImages
              };
            }),
            b: common_vendor.t(item.tourLineName),
            c: common_vendor.t(item.travelDays),
            d: common_vendor.t(item.attractionsList ? item.attractionsList.length : 0),
            e: index,
            f: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        f: common_vendor.sr(paging, "c6745cf4-0", {
          "k": "paging"
        }),
        g: common_vendor.o(queryList),
        h: common_vendor.o(($event) => dataList.value = $event),
        i: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        }),
        j: common_vendor.sr(districtPickerRef, "c6745cf4-3", {
          "k": "districtPickerRef"
        }),
        k: common_vendor.o(onDistrictConfirm),
        l: common_vendor.o(onDistrictCancel),
        m: common_vendor.p({
          columns: districtColumns.value,
          confirmColor: "#FF9F18",
          closeOnClickOverlay: true
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c6745cf4"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/tourLine/index.vue"]]);
wx.createPage(MiniProgramPage);
