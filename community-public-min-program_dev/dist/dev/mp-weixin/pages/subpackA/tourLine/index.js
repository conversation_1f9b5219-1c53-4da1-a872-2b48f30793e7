"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../common/config.js");
const common_api_tourLine = require("../../../common/api/tourLine.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_swiper2 + _easycom_z_paging2)();
}
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_swiper + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const list = common_vendor.ref([]);
    function itemClick(item) {
      const url = "/pages/subpackA/tourLine/details?id=" + item.tourLineId;
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const attractionName = common_vendor.ref("");
    function getDataList(offset, count) {
      common_api_tourLine.getPageList({
        pageNum: offset,
        pageSize: count,
        districtCode: common_vendor.index.getStorageSync("areaCode"),
        // 当前区域码
        sortType: 1,
        // 1-最新, 可根据需要调整
        attractionName: attractionName.value
        // 添加搜索关键词
      }).then((res) => {
        console.log("getPageList返回数据:", res);
        if (res.data && res.data.records && Array.isArray(res.data.records)) {
          const tourLines = res.data.records;
          if (list.value.length === 0 && tourLines.length > 0) {
            const firstTourLine = tourLines[0];
            if (firstTourLine.attractionsList && firstTourLine.attractionsList.length > 0) {
              const firstAttraction = firstTourLine.attractionsList[0];
              if (firstAttraction.attractionImages) {
                list.value.push(firstAttraction.attractionImages);
              }
            }
          }
          if (res.data.total > offset * count || offset < 2) {
            paging.value.complete(tourLines);
          } else {
            paging.value.complete([]);
          }
        } else {
          console.log("数据结构不符合预期，请检查接口返回格式");
          paging.value.complete([]);
        }
      }).catch((error) => {
        console.error("获取景点列表失败:", error);
        paging.value.complete([]);
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          height: "500rpx",
          list: list.value
        }),
        b: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.f((item.attractionsList || []).slice(0, 3), (attraction, idx, i1) => {
              return {
                a: idx,
                b: attraction == null ? void 0 : attraction.attractionImages
              };
            }),
            b: common_vendor.t(item.tourLineName),
            c: common_vendor.t(item.travelDays),
            d: common_vendor.t(item.attractionsList ? item.attractionsList.length : 0),
            e: index,
            f: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        c: common_vendor.sr(paging, "c6745cf4-0", {
          "k": "paging"
        }),
        d: common_vendor.o(queryList),
        e: common_vendor.o(($event) => dataList.value = $event),
        f: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c6745cf4"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/tourLine/index.vue"]]);
wx.createPage(MiniProgramPage);
