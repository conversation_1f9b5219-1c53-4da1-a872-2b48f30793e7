"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_tourLine = require("../../../common/api/tourLine.js");
const common_utils_share = require("../../../common/utils/share.js");
require("../../../common/config.js");
require("../../../common/request.js");
require("../../../common/utils/common.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "details",
  setup(__props) {
    const id = common_vendor.ref("");
    const data = common_vendor.ref({});
    common_vendor.ref(false);
    const selectedAttraction = common_vendor.ref(null);
    const gpsMapCenter = common_vendor.ref({
      latitude: 30.3262018,
      longitude: 103.2816216
    });
    const gpsMarkers = common_vendor.ref([]);
    const gpsPolyline = common_vendor.ref([]);
    const selectedGPSPoint = common_vendor.ref(null);
    const showPointDetail = common_vendor.ref(false);
    const currentLocation = common_vendor.ref(null);
    const showCurrentLocation = common_vendor.ref(false);
    const locationOffset = common_vendor.ref("");
    const locationStats = common_vendor.ref({
      distanceToStart: "0.00",
      distanceToEnd: "0.00",
      distanceToMe: "0.00",
      altitude: "0",
      accuracy: "0",
      signalStrength: "0"
    });
    const isFullScreen = common_vendor.ref(false);
    const mapStyle = common_vendor.computed(() => {
      if (isFullScreen.value) {
        return "width: 100%; height: 100vh; position: fixed; top: 0; left: 0; z-index: 9999;";
      } else {
        return "width: 100%; height: 600rpx;";
      }
    });
    const selectedAttractionId = common_vendor.ref(null);
    const markerAnimationState = common_vendor.ref({});
    const mapScale = common_vendor.ref(14);
    const { setShare } = common_utils_share.useShare();
    let share = {};
    const filteredAttractionsList = common_vendor.computed(() => {
      if (!data.value.attractionsList)
        return [];
      return data.value.attractionsList.filter((attraction) => {
        const pointFlag = attraction.pointFlag;
        return pointFlag !== 0 && pointFlag !== "0" && pointFlag !== 2 && pointFlag !== "2";
      });
    });
    const dynamicGPSMarkers = common_vendor.computed(() => {
      const markers = gpsMarkers.value.map((marker, index) => {
        var _a, _b, _c;
        const isSelected = selectedAttractionId.value == ((_a = marker.attractionData) == null ? void 0 : _a.attractionId);
        const isAnimating = isSelected && markerAnimationState.value[(_b = marker.attractionData) == null ? void 0 : _b.attractionId];
        let iconPath = marker.iconPath;
        let width = isSelected ? 30 : 20;
        let height = isSelected ? 30 : 20;
        return {
          id: marker.id,
          latitude: marker.latitude,
          longitude: marker.longitude,
          iconPath,
          width,
          height,
          alpha: 1,
          // 图标透明度保持不变
          title: marker.title,
          attractionData: marker.attractionData,
          // 图标保持不变，不需要特殊标识
          callout: {
            content: ((_c = marker.callout) == null ? void 0 : _c.content) || "",
            display: isSelected ? "ALWAYS" : "BYCLICK",
            // 动画时保持绿色，只改变提示框大小
            bgColor: isSelected ? "#19993E" : "#ffffff",
            color: isSelected ? "#ffffff" : "#003F03",
            fontSize: isSelected && isAnimating ? 18 : 14,
            // 动画时字体更大
            borderRadius: isSelected && isAnimating ? 15 : 8,
            borderWidth: isSelected && isAnimating ? 4 : 2,
            // 动画时边框更粗
            borderColor: isSelected ? "#ffffff" : "#ffffff",
            padding: isSelected && isAnimating ? 16 : 8,
            // 动画时内边距更大
            textAlign: "center"
          }
        };
      });
      if (currentLocation.value && showCurrentLocation.value) {
        markers.push({
          id: 99999,
          // 使用特殊ID避免冲突
          latitude: currentLocation.value.latitude,
          longitude: currentLocation.value.longitude,
          iconPath: "/static/tourLine/person_map.png",
          width: 25,
          height: 25,
          alpha: 1,
          title: "我的位置",
          attractionData: null,
          // 当前位置不是景点数据
          callout: {
            content: "我的位置",
            color: "#ffffff",
            bgColor: "#006633",
            borderRadius: 8,
            borderWidth: 2,
            borderColor: "#ffffff",
            padding: 8,
            fontSize: 14,
            display: "ALWAYS",
            textAlign: "center"
          }
        });
      }
      return markers;
    });
    const mapCenter = common_vendor.ref({
      latitude: 30.411349,
      // 默认邛崃市中心
      longitude: 103.461507
    });
    const mapMarkers = common_vendor.ref([]);
    const mapPolyline = common_vendor.ref([]);
    const newMapCenter = common_vendor.ref({
      latitude: 30.411349,
      // 默认邛崃市中心
      longitude: 103.461507
    });
    const newMapMarkers = common_vendor.ref([]);
    const newMapPolyline = common_vendor.ref([]);
    const toGuide = () => {
      if (!selectedAttraction.value) {
        common_vendor.index.showToast({
          title: "请先选择景点",
          icon: "none"
        });
        return;
      }
      if (!selectedAttraction.value.latitude || !selectedAttraction.value.longitude) {
        common_vendor.index.showToast({
          title: "该景点暂无位置信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.openLocation({
        latitude: parseFloat(selectedAttraction.value.latitude),
        longitude: parseFloat(selectedAttraction.value.longitude),
        name: selectedAttraction.value.attractionName,
        address: selectedAttraction.value.attractionAddress || "",
        scale: 18,
        success: function() {
          console.log("打开地图成功");
        },
        fail: function(err) {
          console.error("打开地图失败:", err);
          common_vendor.index.showToast({
            title: "打开地图失败",
            icon: "none"
          });
        }
      });
    };
    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const R = 6371e3;
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLng = (lng2 - lng1) * Math.PI / 180;
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    };
    const calculateLocationOffset = (currentLat, currentLng) => {
      if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
        return "暂无参考点";
      }
      let minDistance = Infinity;
      let nearestAttraction = null;
      data.value.attractionsList.forEach((attraction) => {
        if (attraction.latitude && attraction.longitude) {
          const distance = calculateDistance(
            currentLat,
            currentLng,
            parseFloat(attraction.latitude),
            parseFloat(attraction.longitude)
          );
          if (distance < minDistance) {
            minDistance = distance;
            nearestAttraction = attraction;
          }
        }
      });
      if (nearestAttraction) {
        if (minDistance < 1e3) {
          return `距离${nearestAttraction.attractionName} ${minDistance.toFixed(0)}米`;
        } else {
          return `距离${nearestAttraction.attractionName} ${(minDistance / 1e3).toFixed(1)}公里`;
        }
      }
      return "暂无参考点";
    };
    const calculateLocationStats = (currentLat, currentLng, altitude = 0, accuracy = 0) => {
      const stats = {
        distanceToStart: "0.00",
        distanceToEnd: "0.00",
        distanceToMe: "0.00",
        altitude: altitude.toFixed(0),
        accuracy: accuracy.toFixed(0),
        signalStrength: "38"
        // 模拟GPS信号强度
      };
      if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
        return stats;
      }
      const validAttractions = data.value.attractionsList.filter(
        (attraction) => attraction.latitude && attraction.longitude && !isNaN(parseFloat(attraction.latitude)) && !isNaN(parseFloat(attraction.longitude))
      );
      if (validAttractions.length === 0) {
        return stats;
      }
      const startPoint = validAttractions[0];
      const endPoint = validAttractions[validAttractions.length - 1];
      console.log("起点:", startPoint.attractionName, startPoint.latitude, startPoint.longitude);
      console.log("终点:", endPoint.attractionName, endPoint.latitude, endPoint.longitude);
      if (startPoint) {
        const distanceToStart = calculateDistance(
          currentLat,
          currentLng,
          parseFloat(startPoint.latitude),
          parseFloat(startPoint.longitude)
        );
        stats.distanceToStart = (distanceToStart / 1e3).toFixed(2);
        console.log("距起点距离:", stats.distanceToStart, "km");
      }
      if (endPoint) {
        const distanceToEnd = calculateDistance(
          currentLat,
          currentLng,
          parseFloat(endPoint.latitude),
          parseFloat(endPoint.longitude)
        );
        stats.distanceToEnd = (distanceToEnd / 1e3).toFixed(2);
        console.log("距终点距离:", stats.distanceToEnd, "km");
      }
      let minDistanceToMe = Infinity;
      validAttractions.forEach((attraction) => {
        const distance = calculateDistance(
          currentLat,
          currentLng,
          parseFloat(attraction.latitude),
          parseFloat(attraction.longitude)
        );
        if (distance < minDistanceToMe) {
          minDistanceToMe = distance;
        }
      });
      stats.distanceToMe = (minDistanceToMe / 1e3).toFixed(2);
      console.log("距离我:", stats.distanceToMe, "km");
      return stats;
    };
    const getCurrentLocation = () => {
      common_vendor.index.showLoading({
        title: "定位中..."
      });
      common_vendor.index.getLocation({
        type: "gcj02",
        // 使用gcj02坐标系，与地图坐标系一致
        altitude: true,
        // 获取海拔信息
        success: function(res) {
          console.log("获取当前位置成功:", res);
          currentLocation.value = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          locationOffset.value = calculateLocationOffset(res.latitude, res.longitude);
          locationStats.value = calculateLocationStats(
            res.latitude,
            res.longitude,
            res.altitude || 0,
            res.accuracy || 0
          );
          showCurrentLocation.value = true;
          gpsMapCenter.value = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          mapScale.value = 16;
          if (gpsMapContext) {
            gpsMapContext.moveToLocation({
              latitude: res.latitude,
              longitude: res.longitude,
              success: () => {
                console.log("地图移动到当前位置成功");
              },
              fail: (err) => {
                console.error("地图移动失败:", err);
              }
            });
          }
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "定位成功",
            icon: "success",
            duration: 1500
          });
        },
        fail: function(err) {
          console.error("获取位置失败:", err);
          common_vendor.index.hideLoading();
          let errorMsg = "定位失败";
          if (err.errMsg && err.errMsg.includes("auth deny")) {
            errorMsg = "请开启定位权限";
          } else if (err.errMsg && err.errMsg.includes("timeout")) {
            errorMsg = "定位超时，请重试";
          }
          common_vendor.index.showModal({
            title: "定位失败",
            content: errorMsg + "，是否前往设置开启定位权限？",
            confirmText: "去设置",
            cancelText: "取消",
            success: (modalRes) => {
              if (modalRes.confirm) {
                common_vendor.index.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting["scope.userLocation"]) {
                      getCurrentLocation();
                    }
                  }
                });
              }
            }
          });
        }
      });
    };
    const toggleFullScreen = () => {
      isFullScreen.value = !isFullScreen.value;
      console.log("切换全屏状态:", isFullScreen.value);
    };
    let mapContext = null;
    let newMapContext = null;
    let gpsMapContext = null;
    function selectAttraction(attraction, index) {
      selectedAttraction.value = attraction;
      selectedAttractionId.value = attraction.attractionId || index;
      console.log("选中景点:", {
        attractionId: attraction.attractionId,
        selectedAttractionId: selectedAttractionId.value,
        attractionName: attraction.attractionName,
        latitude: attraction.latitude,
        longitude: attraction.longitude
      });
      if (attraction.latitude && attraction.longitude) {
        const lat = parseFloat(attraction.latitude);
        const lng = parseFloat(attraction.longitude);
        const [gcjLng, gcjLat] = common_vendor.coordtransform.wgs84togcj02(lng, lat);
        const newCenter = {
          latitude: gcjLat,
          longitude: gcjLng
        };
        mapCenter.value = newCenter;
        newMapCenter.value = newCenter;
        gpsMapCenter.value = newCenter;
        mapScale.value = 18;
        if (mapContext) {
          mapContext.moveToLocation({
            latitude: newCenter.latitude,
            longitude: newCenter.longitude
          });
        }
        if (newMapContext) {
          newMapContext.moveToLocation({
            latitude: newCenter.latitude,
            longitude: newCenter.longitude
          });
        }
        if (!gpsMapContext) {
          gpsMapContext = common_vendor.index.createMapContext("gps-track-map");
        }
        if (gpsMapContext) {
          console.log("移动地图到:", newCenter);
          gpsMapCenter.value = {
            latitude: newCenter.latitude,
            longitude: newCenter.longitude
          };
          console.log("地图中心点已更新为:", gpsMapCenter.value);
          mapScale.value = 16;
          setTimeout(() => {
            console.log("开始执行地图居中操作，坐标:", newCenter);
            gpsMapContext.moveToLocation({
              latitude: newCenter.latitude,
              longitude: newCenter.longitude,
              success: () => {
                console.log("moveToLocation成功");
                setTimeout(() => {
                  mapScale.value = 18;
                  console.log("地图已放大到最终级别18");
                }, 200);
              },
              fail: (err) => {
                console.error("moveToLocation失败:", err);
              }
            });
          }, 100);
        } else {
          console.error("GPS地图上下文未找到");
        }
      }
      updateMarkerAnimation();
      forceMapCenter();
    }
    function updateMarkerAnimation() {
      markerAnimationState.value = {};
      if (selectedAttractionId.value !== null) {
        markerAnimationState.value[selectedAttractionId.value] = true;
        let bounceCount = 0;
        const maxBounces = 6;
        const bounceAnimation = () => {
          if (bounceCount < maxBounces && selectedAttractionId.value !== null) {
            markerAnimationState.value[selectedAttractionId.value] = !markerAnimationState.value[selectedAttractionId.value];
            bounceCount++;
            setTimeout(bounceAnimation, 400);
          } else {
            if (selectedAttractionId.value !== null) {
              markerAnimationState.value[selectedAttractionId.value] = false;
            }
          }
        };
        setTimeout(bounceAnimation, 50);
      }
    }
    function forceMapCenter() {
      if (!selectedAttraction.value)
        return;
      if (!gpsMapContext) {
        gpsMapContext = common_vendor.index.createMapContext("gps-track-map");
      }
      if (!gpsMapContext) {
        console.error("无法创建GPS地图上下文");
        return;
      }
      const lat = parseFloat(selectedAttraction.value.latitude);
      const lng = parseFloat(selectedAttraction.value.longitude);
      const [gcjLng, gcjLat] = common_vendor.coordtransform.wgs84togcj02(lng, lat);
      const center = {
        latitude: gcjLat,
        longitude: gcjLng
      };
      gpsMapCenter.value = center;
      mapScale.value = 18;
      setTimeout(() => {
        gpsMapContext.moveToLocation({
          latitude: center.latitude,
          longitude: center.longitude,
          success: () => {
          },
          fail: (err) => {
            console.error("强制居中失败:", err);
          }
        });
      }, 100);
    }
    function initMapData() {
      if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
        return;
      }
      const attractions = data.value.attractionsList;
      const validAttractions = attractions.filter(
        (attraction) => attraction.latitude && attraction.longitude && !isNaN(parseFloat(attraction.latitude)) && !isNaN(parseFloat(attraction.longitude))
      );
      if (validAttractions.length === 0) {
        console.warn("没有有效的景点坐标数据");
        return;
      }
      const markers = validAttractions.map((attraction, index) => ({
        id: index,
        latitude: parseFloat(attraction.latitude),
        longitude: parseFloat(attraction.longitude),
        title: attraction.attractionName,
        width: 1,
        height: 1,
        anchor: {
          x: 0.5,
          y: 0.5
        },
        alpha: 0,
        label: {
          content: attraction.attractionName,
          color: "#ffffff",
          bgColor: "#006633",
          borderRadius: 4,
          borderWidth: 1,
          borderColor: "#ffffff",
          padding: 6,
          fontSize: 12,
          textAlign: "center",
          anchorX: 0,
          anchorY: 0
        }
      }));
      mapMarkers.value = markers;
      if (validAttractions.length > 1) {
        drawTourRoute(validAttractions);
      }
      const firstAttraction = validAttractions[0];
      mapCenter.value = {
        latitude: parseFloat(firstAttraction.latitude),
        longitude: parseFloat(firstAttraction.longitude)
      };
      const newMarkers = validAttractions.map((attraction, index) => ({
        id: index,
        latitude: parseFloat(attraction.latitude),
        longitude: parseFloat(attraction.longitude),
        title: attraction.attractionName,
        width: 40,
        height: 40,
        iconPath: "/static/index/location.png",
        // 使用可用的图标
        anchor: {
          x: 0.5,
          y: 0.5
        },
        label: {
          content: `${index + 1}`,
          color: "#ffffff",
          bgColor: "#2bbc4a",
          borderRadius: 50,
          borderWidth: 2,
          borderColor: "#ffffff",
          padding: 4,
          fontSize: 14,
          textAlign: "center",
          anchorX: 0.5,
          anchorY: 0.5
        },
        callout: {
          content: attraction.attractionName,
          color: "#333",
          bgColor: "#ffffff",
          borderRadius: 8,
          borderWidth: 1,
          borderColor: "#2bbc4a",
          padding: 8,
          fontSize: 14,
          display: "ALWAYS"
        }
      }));
      newMapMarkers.value = newMarkers;
      if (validAttractions.length > 1) {
        const sortedAttractions = validAttractions.sort((a, b) => (a.sort || 0) - (b.sort || 0));
        const points = sortedAttractions.map((attraction) => ({
          latitude: parseFloat(attraction.latitude),
          longitude: parseFloat(attraction.longitude)
        }));
        newMapPolyline.value = [{
          points,
          color: "#2bbc4a",
          // 绿色路线
          width: 6,
          dottedLine: true,
          // 虚线样式
          arrowLine: true,
          borderColor: "#ffffff",
          borderWidth: 2
        }];
      } else {
        newMapPolyline.value = [];
      }
      newMapCenter.value = mapCenter.value;
      common_vendor.nextTick$1(() => {
        mapContext = common_vendor.index.createMapContext("tour-map");
        newMapContext = common_vendor.index.createMapContext("new-tour-map");
        gpsMapContext = common_vendor.index.createMapContext("gps-track-map");
        if (validAttractions.length > 1) {
          includeAllPoints(validAttractions);
        }
        if (newMapPolyline.value.length > 0 && newMapPolyline.value[0].points.length > 1) {
          const gpsPoints = newMapPolyline.value[0].points;
          if (newMapContext) {
            newMapContext.includePoints({
              points: gpsPoints,
              padding: [50, 50, 50, 50]
            });
          }
        }
      });
    }
    function drawTourRoute(attractions) {
      const sortedAttractions = attractions.sort((a, b) => (a.sort || 0) - (b.sort || 0));
      const points = sortedAttractions.map((attraction) => ({
        latitude: parseFloat(attraction.latitude),
        longitude: parseFloat(attraction.longitude)
      }));
      mapPolyline.value = [{
        points,
        color: "#006633",
        // 绿色主题
        width: 8,
        dottedLine: false,
        arrowLine: true,
        // 显示箭头方向
        borderColor: "#ffffff",
        borderWidth: 3
      }];
    }
    function includeAllPoints(attractions) {
      if (!mapContext || attractions.length === 0)
        return;
      const points = attractions.map((attraction) => ({
        latitude: parseFloat(attraction.latitude),
        longitude: parseFloat(attraction.longitude)
      }));
      mapContext.includePoints({
        points,
        padding: [50, 50, 50, 50]
        // 上右下左的边距
      });
    }
    function initGPSMapData() {
      try {
        const tracksData = data.value.tracks || [];
        let routePoints = [];
        if (tracksData && tracksData.length > 0) {
          routePoints = tracksData.map((track) => {
            const lat = parseFloat(track.latitude);
            const lng = parseFloat(track.longitude);
            const [gcjLng, gcjLat] = common_vendor.coordtransform.wgs84togcj02(lng, lat);
            return {
              latitude: gcjLat,
              longitude: gcjLng
            };
          });
        } else if (data.value.attractionsList && data.value.attractionsList.length > 0) {
          const validAttractions = data.value.attractionsList.filter(
            (attraction) => attraction.latitude && attraction.longitude && !isNaN(parseFloat(attraction.latitude)) && !isNaN(parseFloat(attraction.longitude))
          );
          const sortedAttractions = validAttractions.sort((a, b) => {
            if (a.sort !== void 0 && b.sort !== void 0) {
              return (a.sort || 0) - (b.sort || 0);
            }
            const aFlag = parseInt(a.pointFlag) || a.pointFlag || 1;
            const bFlag = parseInt(b.pointFlag) || b.pointFlag || 1;
            return aFlag - bFlag;
          });
          routePoints = sortedAttractions.map((attraction) => {
            const lat = parseFloat(attraction.latitude);
            const lng = parseFloat(attraction.longitude);
            const [gcjLng, gcjLat] = common_vendor.coordtransform.wgs84togcj02(lng, lat);
            return {
              latitude: gcjLat,
              longitude: gcjLng
            };
          });
        }
        if (routePoints.length > 0) {
          gpsPolyline.value = [{
            points: routePoints,
            color: "#066239",
            width: 2,
            arrowLine: false,
            // 不显示箭头，更干净
            borderColor: "#ffffff",
            borderWidth: 1,
            dottedLine: false
          }];
        } else {
          gpsPolyline.value = [];
        }
        gpsMarkers.value = [];
        if (data.value.attractionsList && data.value.attractionsList.length > 0) {
          data.value.attractionsList.forEach((attraction, index) => {
            if (attraction.latitude && attraction.longitude) {
              const lat = parseFloat(attraction.latitude);
              const lng = parseFloat(attraction.longitude);
              const [gcjLng, gcjLat] = common_vendor.coordtransform.wgs84togcj02(lng, lat);
              let iconPath = "";
              let width = 20;
              let height = 20;
              const pointFlag = parseInt(attraction.pointFlag) || attraction.pointFlag;
              const isSelected = selectedAttractionId.value === (attraction.attractionId || index);
              if (pointFlag === 0 || pointFlag === "0") {
                iconPath = "https://www.2bulu.com/images/icon/icon_start.png";
                if (isSelected) {
                  width = 30;
                  height = 30;
                }
              } else if (pointFlag === 2 || pointFlag === "2") {
                iconPath = "https://www.2bulu.com/images/icon/icon_end.png";
                if (isSelected) {
                  width = 30;
                  height = 30;
                }
              } else if (pointFlag === 1 || pointFlag === "1") {
                iconPath = "/static/tourLine/marker.png";
                if (isSelected) {
                  width = 30;
                  height = 30;
                }
              }
              const markerObj = {
                id: index + 1,
                latitude: gcjLat,
                longitude: gcjLng,
                iconPath,
                width,
                height,
                title: attraction.attractionName || "",
                attractionData: attraction,
                // 新增：动态标记样式
                callout: {
                  content: attraction.attractionName || "",
                  color: "#003F03",
                  bgColor: "#ffffff",
                  borderRadius: 8,
                  borderWidth: 2,
                  borderColor: "#ffffff",
                  padding: 8,
                  fontSize: 14,
                  display: "ALWAYS",
                  textAlign: "center"
                }
              };
              gpsMarkers.value.push(markerObj);
            }
          });
        }
        if (routePoints.length > 0) {
          const lats = routePoints.map((p) => p.latitude);
          const lngs = routePoints.map((p) => p.longitude);
          gpsMapCenter.value = {
            latitude: (Math.max(...lats) + Math.min(...lats)) / 2,
            longitude: (Math.max(...lngs) + Math.min(...lngs)) / 2
          };
        }
        if (gpsMarkers.value.length === 0) {
          console.warn("警告：没有创建任何标记点！");
        } else {
          console.log(`成功创建了 ${gpsMarkers.value.length} 个标记点`);
        }
      } catch (error) {
        console.error("初始化GPS地图数据失败:", error);
        initFallbackGPSData();
      }
    }
    function initFallbackGPSData() {
      console.log("使用备用数据初始化GPS地图");
      const fallbackRoute = [
        { latitude: 30.3262018, longitude: 103.2816216 },
        { latitude: 30.3244852, longitude: 103.2788938 },
        { latitude: 30.3239008, longitude: 103.2778191 },
        { latitude: 30.3240108, longitude: 103.2775803 }
      ];
      gpsPolyline.value = [{
        points: fallbackRoute,
        color: "#4A90E2",
        width: 6,
        arrowLine: true,
        borderColor: "#ffffff",
        borderWidth: 2
      }];
      gpsMarkers.value = [
        {
          id: 1,
          latitude: 30.3262018,
          longitude: 103.2816216,
          iconPath: "https://www.2bulu.com/images/icon/icon_start.png",
          width: 20,
          height: 20,
          title: ""
        }
      ];
      gpsMapCenter.value = {
        latitude: 30.3262018,
        longitude: 103.2816216
      };
      console.log("备用GPS地图数据初始化完成");
    }
    const onGPSMarkerTap = (e) => {
      const markerId = e.markerId;
      const marker = gpsMarkers.value.find((m) => m.id === markerId);
      console.log("标记点击:", markerId, marker);
      if (markerId === 99999) {
        common_vendor.index.showToast({
          title: "这是您的当前位置",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (marker && marker.attractionData) {
        selectAttraction(marker.attractionData, markerId - 1);
        selectedGPSPoint.value = {
          ...marker.attractionData,
          coordinates: [marker.longitude, marker.latitude],
          timestamp: new Date().toISOString()
        };
        showPointDetail.value = true;
      }
    };
    const onGPSMapTap = (e) => {
    };
    const onGPSRegionChange = (e) => {
    };
    const closePointDetail = () => {
      showPointDetail.value = false;
      selectedGPSPoint.value = null;
    };
    const previewImage = (imageUrl) => {
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        indicator: "number",
        loop: false
      });
    };
    const onImageError = (e) => {
      console.error("图片加载失败:", e);
      common_vendor.index.showToast({
        title: "图片加载失败",
        icon: "none",
        duration: 2e3
      });
    };
    const onImageLoad = (e) => {
      console.log("图片加载成功:", e);
    };
    common_vendor.onLoad((options) => {
      id.value = decodeURIComponent(options.id);
      console.log("旅游线路ID:", id.value);
      common_api_tourLine.getPageItemDetail({
        tourLineId: id.value
      }).then((res) => {
        var _a, _b;
        console.log("详情数据:", res.data);
        data.value = res.data;
        initMapData();
        initGPSMapData();
        share = {
          title: data.value.tourLineName,
          desc: data.value.tourLineIntroduction,
          imageUrl: ((_b = (_a = data.value.attractionsList) == null ? void 0 : _a[0]) == null ? void 0 : _b.attractionImages) || ""
        };
        setShare({
          weapp: {
            ...share
          }
        });
      }).catch((error) => {
        console.error("获取详情失败:", error);
      });
    });
    common_vendor.onShow(() => {
      setShare({
        weapp: {
          ...share
        }
      });
    });
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: currentLocation.value && showCurrentLocation.value
      }, currentLocation.value && showCurrentLocation.value ? {
        b: common_vendor.t(locationStats.value.distanceToStart),
        c: common_vendor.t(locationStats.value.distanceToEnd),
        d: common_vendor.t(locationStats.value.distanceToMe),
        e: common_vendor.t(locationStats.value.altitude),
        f: common_vendor.t(currentLocation.value.latitude.toFixed(7)),
        g: common_vendor.t(currentLocation.value.longitude.toFixed(7)),
        h: common_vendor.t(locationStats.value.altitude),
        i: common_vendor.t(locationStats.value.accuracy)
      } : {}, {
        j: gpsMapCenter.value.latitude,
        k: gpsMapCenter.value.longitude,
        l: mapScale.value,
        m: common_vendor.unref(dynamicGPSMarkers),
        n: gpsPolyline.value,
        o: common_vendor.s(common_vendor.unref(mapStyle)),
        p: common_vendor.o(onGPSMarkerTap),
        q: common_vendor.o(onGPSMapTap),
        r: common_vendor.o(onGPSRegionChange),
        s: isFullScreen.value ? "/static/tourLine/exitfullscreen.png" : "/static/tourLine/fullscreen.png",
        t: common_vendor.o(toggleFullScreen),
        v: common_vendor.o(getCurrentLocation),
        w: showPointDetail.value
      }, showPointDetail.value ? common_vendor.e({
        x: common_vendor.o(closePointDetail),
        y: selectedGPSPoint.value
      }, selectedGPSPoint.value ? common_vendor.e({
        z: selectedGPSPoint.value.attractionImages || selectedGPSPoint.value.image
      }, selectedGPSPoint.value.attractionImages || selectedGPSPoint.value.image ? {
        A: selectedGPSPoint.value.attractionImages || selectedGPSPoint.value.image,
        B: common_vendor.o(($event) => previewImage(selectedGPSPoint.value.attractionImages || selectedGPSPoint.value.image)),
        C: common_vendor.o(onImageError),
        D: common_vendor.o(onImageLoad)
      } : {}) : {}, {
        E: common_vendor.o(() => {
        }),
        F: common_vendor.o(closePointDetail)
      }) : {}, {
        G: common_vendor.t(data.value.tourLineName),
        H: common_vendor.t(data.value.travelDays),
        I: common_vendor.t(common_vendor.unref(filteredAttractionsList).length),
        J: common_vendor.t(data.value.districtAddress || "文君井社区"),
        K: common_vendor.f(common_vendor.unref(filteredAttractionsList), (attraction, index, i0) => {
          return {
            a: attraction.attractionImages,
            b: common_vendor.t(attraction.attractionName),
            c: attraction.attractionId,
            d: common_vendor.o(($event) => selectAttraction(attraction, index), attraction.attractionId),
            e: selectedAttractionId.value == attraction.attractionId ? 1 : ""
          };
        }),
        L: common_vendor.t(selectedAttraction.value ? (_a = selectedAttraction.value.attractionIntroduction) == null ? void 0 : _a.replace(/<[^>]*>/g, "") : data.value.tourLineIntroduction),
        M: selectedAttraction.value
      }, selectedAttraction.value ? {
        N: common_vendor.o(toGuide)
      } : {}, {
        O: !isFullScreen.value,
        P: isFullScreen.value ? 1 : ""
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9e7bd850"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/tourLine/details.vue"]]);
wx.createPage(MiniProgramPage);
