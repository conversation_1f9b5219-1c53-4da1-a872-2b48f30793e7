/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.indicator.data-v-c922e57d {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: #000000;
  opacity: 0.7;
  padding: 2px;
  border-radius: 6px;
}
.indicator__dot.data-v-c922e57d {
  height: 6px;
  width: 6px;
  border-radius: 100px;
  background-color: rgba(255, 255, 255, 0.35);
  margin: 0 3px;
  transition: background-color 0.3s;
}
.indicator__dot--active.data-v-c922e57d {
  background-color: #ffffff;
}
.indicator-num.data-v-c922e57d {
  padding: 2px 0;
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.indicator-num__text.data-v-c922e57d {
  color: #FFFFFF;
  font-size: 12px;
}
.data-v-c922e57d .uv-swiper {
  background-color: rgba(255, 0, 0, 0) !important;
}
.icon.data-v-c922e57d {
  height: 32rpx;
  width: 32rpx;
  min-width: 32rpx;
}
.address-icon.data-v-c922e57d {
  height: 32rpx;
  width: 32rpx;
  min-width: 32rpx;
  margin-top: 6rpx;
}
.price-title.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
}
.price.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #FF9F18;
  line-height: 44rpx;
}
.address.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  margin-left: 8rpx;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: block;
  width: 490rpx;
}
.phone.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
}
.btn.data-v-c922e57d {
  min-width: 192rpx;
  height: 60rpx;
  background-size: 100%;
  text-align: center;
  font-size: 32rpx;
  line-height: 65rpx;
  box-sizing: border-box;
}
.release-icon.data-v-c922e57d {
  background: url("data:image/png;base64,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");
  background-repeat: no-repeat !important;
  background-size: 100% 100%;
  color: #FF9F18 !important;
  font-weight: 500;
  font-size: 32rpx;
  width: 208rpx !important;
  height: 87rpx !important;
  text-align: center;
  line-height: 73rpx !important;
}
.btn.data-v-c922e57d:nth-of-type(n+2) {
  margin-left: 10rpx;
}
.normal.data-v-c922e57d {
  background-repeat: repeat;
  border-radius: 50rpx;
  color: #FFFFFF;
  border: 2rpx solid #FFFFFF;
  height: 65rpx;
  margin-top: 4rpx;
  padding: 0 15rpx;
}
.normal + .active.data-v-c922e57d {
  margin-left: 5rpx;
}
.active.data-v-c922e57d {
  background-image: url("data:image/png;base64,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");
  background-repeat: repeat;
  color: #FF9F18;
  font-weight: bold;
}
.active + .btn.data-v-c922e57d {
  margin-left: 5rpx;
}
.search.data-v-c922e57d {
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 15rpx;
  box-sizing: border-box;
  color: #395C16;
  background-color: rgba(255, 255, 255, 0.2);
}
.search .search-btn.data-v-c922e57d {
  width: 100rpx;
  height: 40rpx;
  background: #FF9F18;
  border-radius: 8rpx;
  color: #FFFFFF;
  font-size: 24rpx;
  line-height: 40rpx;
  text-align: center;
}
.custom-input.data-v-c922e57d {
  width: 100%;
  height: 100%;
  color: #ffffff;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0 10rpx;
}
.content.data-v-c922e57d {
  position: relative;
  /*height: calc(100vh - 500rpx);*/
  min-height: 50rpx;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -50rpx;
  padding: 32rpx 32rpx 0;
  box-sizing: border-box;
}
.hots.data-v-c922e57d {
  display: flex;
  justify-content: left;
  align-items: center;
}
.hots .title.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
}
.labels.data-v-c922e57d {
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: wrap;
}
.labels .label.data-v-c922e57d {
  border: 2rpx solid #CCCCCC;
  border-radius: 4rpx;
  padding: 2rpx 12rpx;
  box-sizing: border-box;
  color: #999999;
  margin-right: 16rpx;
  font-size: 24rpx;
  line-height: 40rpx;
}
.labels .label.data-v-c922e57d:hover {
  background: #FF9F18;
  color: #FFFFFF;
}
.filter-bar.data-v-c922e57d {
  display: flex;
  align-items: center;
  padding: 32rpx 0 20rpx 0;
}
.filter-bar .filter-item.data-v-c922e57d {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.filter-bar .filter-item .filter-text.data-v-c922e57d {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 40rpx;
  margin-right: 4rpx;
}
.filter-bar .filter-item .filter-arrow.data-v-c922e57d, .filter-bar .filter-item .filter-icon.data-v-c922e57d {
  width: 28rpx;
  height: 28rpx;
}
.list-box.data-v-c922e57d {
  padding: 0 32rpx 32rpx;
  box-sizing: border-box;
  width: 100%;
}
.list.data-v-c922e57d {
  display: flex;
  background: #FFFFFF;
  box-sizing: border-box;
  color: #333333;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #EEEEEE;
}
.list .image-icon.data-v-c922e57d {
  width: 180rpx;
  height: 240rpx;
}
.list .info.data-v-c922e57d {
  padding-left: 16rpx;
  width: 490rpx;
}
.list .info .title.data-v-c922e57d {
  font-weight: 500;
  color: #222222;
  line-height: 48rpx;
  font-size: 32rpx;
  margin-right: 8rpx;
}
.list .info .district.data-v-c922e57d {
  padding: 2rpx 12rpx;
  box-sizing: border-box;
  background: #FF9F18;
  background: rgba(255, 159, 24, 0.1);
  border-radius: 4rpx;
  color: #FF9F18;
  font-size: 24rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #FF9F18;
  line-height: 40rpx;
}
.list .info .desc.data-v-c922e57d {
  margin: 8rpx 0;
}
.list .info .desc .desc-text.data-v-c922e57d {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: block;
}
.list .tag.data-v-c922e57d {
  color: #FF9F18;
}
.list .grey.data-v-c922e57d {
  color: #999999;
  margin-top: 8rpx;
}

/* 全局样式，确保占位符文本为白色 */
.white-placeholder {
  color: #ffffff !important;
}
