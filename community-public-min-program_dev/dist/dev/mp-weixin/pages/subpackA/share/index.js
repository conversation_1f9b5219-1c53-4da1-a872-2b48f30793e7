"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils_common = require("../../../common/utils/common.js");
const common_api_market = require("../../../common/api/market.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_uv_text2 = common_vendor.resolveComponent("uv-text");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  (_easycom_uv_button2 + _easycom_uv_swiper2 + _easycom_uv_text2 + _easycom_z_paging2)();
}
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_uv_text = () => "../../../uni_modules/uv-text/components/uv-text/uv-text.js";
const _easycom_z_paging = () => "../../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_uv_button + _easycom_uv_swiper + _easycom_uv_text + _easycom_z_paging)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const current1 = common_vendor.ref(0);
    const list = common_vendor.ref([common_utils_common.img("https://qlzhsq.qlzhsq.cn:30210/images/static/03.png")]);
    common_vendor.ref();
    stores_store.useUserStore();
    function openLocation(item) {
      common_vendor.index.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        //name: item.name,
        address: item.address,
        success: function(res) {
          console.log("打开系统位置地图成功");
        },
        fail: function(error) {
          console.log(error);
        }
      });
    }
    function itemClick(item) {
      const url = "/pages/subpackA/market/details?id=" + item.id + "&type=0";
      common_vendor.index.navigateTo({
        url
      });
    }
    const queryList = (pageNo, pageSize) => {
      getDataList(pageNo, pageSize);
    };
    const paging = common_vendor.ref();
    const dataList = common_vendor.ref([]);
    const name = common_vendor.ref("");
    const type = common_vendor.ref(1);
    common_vendor.watch(type, () => {
      if (type.value == 0) {
        list.value = [common_utils_common.img("/static/banner/ershou.png")];
      }
      if (type.value == 1) {
        list.value = [common_utils_common.img("/static/banner/gongxiang.png")];
      }
    });
    function search() {
      paging.value.reload();
    }
    function getDataList(offset, count) {
      common_api_market.getList({
        pageNum: offset,
        pageSize: count,
        name: name.value,
        type: type.value
        // districtCode:'',//全部
        // sortType: 1//最新
      }).then((res) => {
        if (res.rows) {
          if (res.total > offset * count || offset < 2) {
            paging.value.complete(res.rows);
          } else {
            paging.value.complete([]);
          }
        }
      });
    }
    const popularList = common_vendor.ref([]);
    function getPopularListData() {
      common_api_market.getPopularList({
        pageNum: 1,
        pageSize: 4,
        type: type.value
      }).then((res) => {
        popularList.value = res.rows;
      });
    }
    common_vendor.onLoad(() => {
      getPopularListData();
      common_vendor.index.$on("refreshList", search);
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("refreshList", search);
    });
    return (_ctx, _cache) => {
      return {
        a: name.value,
        b: common_vendor.o(($event) => name.value = $event.detail.value),
        c: common_vendor.o(($event) => search()),
        d: common_vendor.p({
          type: "warning",
          customStyle: {
            height: "50rpx"
          }
        }),
        e: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: index,
            b: common_vendor.n(index === current1.value && "indicator__dot--active")
          };
        }),
        f: common_vendor.o((e) => current1.value = e.current),
        g: common_vendor.p({
          height: "300rpx",
          list: list.value,
          autoplay: false
        }),
        h: common_vendor.f(popularList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        i: common_vendor.f(dataList.value, (item, index, i0) => {
          return {
            a: common_vendor.unref(common_utils_common.img)(item.coverImages),
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.districtName),
            d: "c922e57d-3-" + i0 + ",c922e57d-0",
            e: common_vendor.p({
              lines: 2,
              size: "14",
              text: item.brief
            }),
            f: common_vendor.t(item.address),
            g: common_vendor.o(($event) => openLocation(item), index),
            h: common_vendor.t(item.contactPhone),
            i: common_vendor.o(() => {
            }, index),
            j: index,
            k: common_vendor.o(($event) => itemClick(item), index)
          };
        }),
        j: common_vendor.sr(paging, "c922e57d-0", {
          "k": "paging"
        }),
        k: common_vendor.o(queryList),
        l: common_vendor.o(($event) => dataList.value = $event),
        m: common_vendor.p({
          ["auto-show-back-to-top"]: true,
          modelValue: dataList.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c922e57d"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/share/index.vue"]]);
wx.createPage(MiniProgramPage);
