"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const areaStore = stores_store.useAreaStore();
    const searchKeyword = common_vendor.ref("");
    const activeCategory = common_vendor.ref(1);
    const activeTab = common_vendor.ref("category");
    const bookList = common_vendor.ref([]);
    const currentCommunityName = common_vendor.ref("请选择社区");
    const categories = common_vendor.ref([
      { id: 1, name: "热门排行" },
      { id: 2, name: "文化/科学/教育/体育" },
      { id: 3, name: "历史/地理" },
      { id: 4, name: "艺术" },
      { id: 5, name: "社会科学总论" },
      { id: 6, name: "自动化/计算机技术" },
      { id: 7, name: "工业技术" },
      { id: 8, name: "自然科学" },
      { id: 9, name: "经济" },
      { id: 10, name: "语言/文学" },
      { id: 11, name: "哲学/宗教" },
      { id: 12, name: "马克思主义/列宁主义/毛泽东思想..." }
    ]);
    const mockBooks = [
      {
        id: 1,
        categoryId: 1,
        title: "如何改变世界：马克思和马克思主义的传奇",
        author: "(英)埃里克·霍布斯鲍姆(Eric H...)",
        publisher: "中央编译出版社",
        cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mks.jpg",
        status: "available",
        canCollect: true,
        canAdd: true,
        isCollected: true
      },
      {
        id: 2,
        categoryId: 1,
        title: "新的历史条件下马克思政治经济学新探索：21...",
        author: "李济广",
        publisher: "中国经济出版社",
        cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mks2.jpeg",
        status: "borrowed",
        canCollect: false,
        canAdd: true,
        isCollected: false
      },
      {
        id: 3,
        categoryId: 1,
        title: "世界是这样知道毛泽东的：《毛泽东自传》溯...",
        author: "丁晓平著",
        publisher: "中共党史出版社",
        cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mzd.jpg",
        status: "available",
        canCollect: true,
        canAdd: true,
        isCollected: true
      },
      {
        id: 4,
        categoryId: 1,
        title: "抗战颂歌毛泽东",
        author: "徐条权著",
        publisher: "江苏人民出版社",
        cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mzd2.jpg",
        status: "available",
        canCollect: true,
        canAdd: true,
        isCollected: false
      }
    ];
    const selectCategory = (categoryId) => {
      activeCategory.value = categoryId;
      loadBooks();
    };
    const loadBooks = () => {
      let filteredBooks = mockBooks;
      if (activeCategory.value !== 0) {
        filteredBooks = mockBooks.filter((book) => book.categoryId === activeCategory.value);
      }
      if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.toLowerCase();
        filteredBooks = filteredBooks.filter(
          (book) => book.title.toLowerCase().includes(keyword) || book.author.toLowerCase().includes(keyword)
        );
      }
      bookList.value = filteredBooks;
    };
    const goToBookDetail = (book) => {
      common_vendor.index.navigateTo({
        url: `/pages/subpackA/library/detail?id=${book.id}`
      });
    };
    const onSearchInput = () => {
      loadBooks();
    };
    const switchTab = (tab) => {
      activeTab.value = tab;
      if (tab === "favorite") {
        bookList.value = mockBooks.filter((book) => book.isCollected);
      } else {
        loadBooks();
      }
    };
    const switchCommunity = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/area/area?scene=auth"
      });
    };
    common_vendor.onLoad(() => {
      currentCommunityName.value = areaStore.areaName || "请选择社区";
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data && data.name) {
          currentCommunityName.value = data.name;
          console.log("切换社区到:", data.name);
          loadBooks();
        }
      });
      loadBooks();
    });
    common_vendor.onShow(() => {
      currentCommunityName.value = areaStore.areaName || "请选择社区";
      console.log("页面显示，当前社区:", currentCommunityName.value);
    });
    common_vendor.watch(
      () => areaStore.areaName,
      (newVal, oldVal) => {
        if (newVal !== oldVal) {
          currentCommunityName.value = newVal || "请选择社区";
          console.log("区域变化，更新社区名称:", newVal);
          loadBooks();
        }
      }
    );
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    return (_ctx, _cache) => {
      return {
        a: activeTab.value === "category" ? 1 : "",
        b: common_vendor.o(($event) => switchTab("category")),
        c: activeTab.value === "favorite" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("favorite")),
        e: common_vendor.t(currentCommunityName.value),
        f: common_vendor.o(switchCommunity),
        g: common_vendor.f(categories.value, (category, k0, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: category.id,
            c: activeCategory.value === category.id ? 1 : "",
            d: common_vendor.o(($event) => selectCategory(category.id), category.id)
          };
        }),
        h: common_vendor.p({
          name: "search",
          size: "16",
          color: "#999"
        }),
        i: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, onSearchInput]),
        j: searchKeyword.value,
        k: common_vendor.f(bookList.value, (book, k0, i0) => {
          return common_vendor.e({
            a: book.cover,
            b: common_vendor.t(book.title),
            c: common_vendor.t(book.author),
            d: common_vendor.t(book.publisher),
            e: book.canCollect
          }, book.canCollect ? {} : {}, {
            f: book.status === "borrowed"
          }, book.status === "borrowed" ? {} : {}, {
            g: book.canAdd
          }, book.canAdd ? {} : {}, {
            h: book.id,
            i: common_vendor.o(($event) => goToBookDetail(book), book.id)
          });
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d4142aa4"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/library/index.vue"]]);
wx.createPage(MiniProgramPage);
