/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.library-container.data-v-d4142aa4 {
  height: 100vh;
  background-color: #f5f5f5;
}
.search-box.data-v-d4142aa4 {
  display: flex;
  align-items: center;
  background-color: #EEEEEE;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  margin-bottom: 20rpx;
}
.search-box input.data-v-d4142aa4 {
  margin-left: 10rpx;
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  outline: none;
}
.header-section.data-v-d4142aa4 {
  background: #FBA62D;
  padding: 20rpx 32rpx 0 10rpx;
}
.header-section .community-name.data-v-d4142aa4 {
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  cursor: pointer;
  margin-bottom: 20rpx;
}
.header-section .community-name.data-v-d4142aa4:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
.header-section .community-name .switch-icon.data-v-d4142aa4 {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.header-section .function-buttons.data-v-d4142aa4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-section .function-buttons .btn-item-group.data-v-d4142aa4 {
  flex: 1;
  display: flex;
  align-items: center;
}
.header-section .function-buttons .btn-item.data-v-d4142aa4 {
  color: #fff;
  font-size: 30rpx;
  border-radius: 100rpx;
  text-align: center;
  height: 65rpx;
  width: 208rpx;
}
.header-section .function-buttons .btn-item.active.data-v-d4142aa4 {
  color: #FF9F18;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGgAAAAsCAYAAACJ1f3bAAAMcElEQVR4Aeyae4xdVRXG17mvmWE6CTUZFMQA9b8hQHgmKEpNExSSmia0PqIRFRoJlEchKSDUOn+QSKk2EYkBQU0wAh1iYnikQLAtiFVooUiY2NoWLBJKh3b6mJn7PPe4fqezpqfbe+459/ZeAoHmfHftvda319rnW/c8ZqYZ6dw/T1N9AhHTQDrxr90GhZtYIZJZJJJdtEiyc+cewbnnSu7jiKgGaII2aKSN8qahprWj1QZ5FNTimd3bXxu8vVL8/iO+/+CaNcG2deuCg4qyorppU1D5OIJzV6DBwTVr6tse8WsPohFaoRnaaXtolpp0R9oGhY3hG3Lj+HtnPPxw7Xef/vyZu/L53tWZTOYSLVVX7FS8rtiieHUajNMgjm/+OBuX2/hxcdcfxzd/nHXzMEeDHSJePZPJXoJGaIVmaIeGrTQqqUF026P7859dM/Dss9W7jj/+hH9ks9mvich2xZuKPYpJRU3BwRryAsZpABe4XHzN4PJtbmtsnmTj+OaPs43yooGvH2iCNmi0XTX7Ktqh4UWqJZoqx9brsPFB4cYREa4aj46vXPnq586Zt/DpXC53pYhwpbyrlk1YgU+szLwcNNICrXaLaoeG81RLNEVbvZqMr+H/P+IaFDZnw1zJPPrQzqFTTjnrRc/zTtPlbysoxrpGsGJuLM7v8o51Hlcnzn+s9dz1cXXMj3Zve6olmqItGjdrEgVU86OOmeY8cM9LJ55w8qlPasJAGWMKKxRnyQfcOD7g+js9pwZw8+IDrr/Tc2oANy8+YP4xNB387KlPonGzJrFIdT9y0E0WLLtqxXFzTj/vIRGvICIHFXDjYIXNtsuz9a6Ny2f+tPwknhu3udWJs8Yzm4Z3QJtUmHP6OX9AazRHe9X5qINEUYc3uki8vj7Jzlt42x2a4CwN7lVY4ThLnija5UVzMI7L4/rhRuHGbR7lMDa/WXxRmD/JRtdkRGKfRy5vn+dlz5y38JY70Bztp7VWc/hgweGR6EvBCvFm75TMvatfPrXQ03O1iNAcOEkb7Fac2qBb+ZPyUhsk8Y4lvrfQ03c1mqP9Cu2B6k4+NSIUDwd8bNggmcqAZE+ec8YNOi8qeKh5ag3w06BTfMtjNk1tOJ3iWx6z5E6DVvj8DFlEc7SnB6r3zEExJh6dGxyTzHmXXNufz/d8U528xxOPwgonWVuTxLN4HN/8Zo2fZDvFtzxmk+pavFX+ZC5X+Bba0wN6ofqT68gVNDoqXv+gZL53zYoF00E6C6kTsA2b7UTOVnJYXbOtrO0E1+qadXPW9XkvaE8P6IX2IDxYICvk8LPHL0i2UOg/V0QqCmIuLLHrt3lc3Pxmjd+qTVofFze/2VbrGj9pfVzc/GYtX9RW0J4ehM8iEbjhFeTRofE54uUnJZPv6b1ARPi1DQQXltD12zwpbrx2bVL+pHi7dW1dUv6kuOVpZGtoTw/oBT3RPngkFC6pfn3+HOqTjP47SQPu7Q1eM7gFm3Gbxdw8Nm+2hpjxzOJrB7betUm5WuU3yqe/XM2cRA/oBT3RPoRXEFYmy3oFlcJ5b+iQcOzpOA3cgmnWNOK0m6fdde4e2s3T7rpofZVaevPaA3rBBJBYhsb0GVQRr1gJm5LVgC0kDmzuWmJRuPG082gOxraOMbC5a4lF4cbTzqM5GNs6xsDmriUWhRtPO7ccWXowW3tBT7QPkuGXbAzkRJGBgXBU1k+axCK3AL4okuJRbrOxlgwPy2dcm5s1v1nzmzV/qzYsrh9uHpubdfOa36wbTzvX0uIFQVAOe6C9wEFvSMB4Bkoa0wl+imKjwNcNWA03t/nNuvFOzePym99sp+q5ecifEwnQXuU/cmRghlP9C8+hQyLVakn/GihKDm93LATQWgFrmsHN1YxLzOUnzVnTDO76ZlxiLj9pzppmcNfDzVar1R30QLQX9AQSAcYyXpAgl5Xg0Pi+jergFkfcAA/Y3Cw+YPNuWWoANz8+4Po7PacGcPPiA66/nXn20Pj7G+kBvdAehAfJZXRQuN1Jf4/UR19+7jmNuA2ygvCjSPJb3LXRHIzdeNwcbhTGi/oYmz/Owokijuf6o2sYW5xxFOaPs1EuY3hZtKcHqv9MTwgyl88cL/VqUer3/PyKXX6tyn9+4DbHwmZgPYjjEANxcfPDATZPa1kD4vjEQFzc/HCAzdNa1oA4PjEQFzd/Hs3Rnh7Qi7Ap+sHiYGhIgt27JKj0SV188XftHP2NxqINgtcIVqBRDF9SHA7QcuFhfNfCaQTjNYrhS4rDAWFx/TC+a+E0gvEaxfAlxeGA7DtvqeaqPT2gF/RE9xMQFBkWGRrU5pTFzwdSW/6jC9fVquWXlUCT4FihbllqNEO36lreZrWJGa8bNovWP1584Tq0F+1B2IthVV8PitOfgOcQl1ZvILVMpVh54uFVt2mcvwfBSdoYHODy8IG0fpeXdk4N4PLxgbR+l5d2Tg3g8vGBOD/Peh+t0Rzt6QG9GNZ3btU/fJXGBkMjEnyqKH6uJL6fkeqj99/x361bXrxeg3kFRZrBNuBy4vyaMjzS8l2eO4+rE+cPi+tH2jwuz53H1Ynza2khlt265fnr0RrN0Z4e0AslhC9uFNJxeJcLr6KeE8SnkyxYvuSiF97f/dZKJXCrI2GnQF3g5sMHXH+TeXiircapAdx1+IDr7/ScGjnV9u7lSy5+Aa3RHO2jV4/qPnMFMZ65iup5qcqUVHI1Kd9w+Wm/3zG66SYlkBREN8scRH2M8XUD5I7CakR9jM3faUvuKCx/1MfY/I0st7XMjn9tuglt0Rit0dy9elTzoxok4X3vYqkXDok/W3+p0NMn5WpVyssWn792w+O/XaS/Bjqgi8ICainOZgDjKPB1A9EajK0G4yjM32kbrcHY8jOOwvyuhTOOlsuuPH8t2qIxWqO5qPZhD1RcO1hgY2wwPCyB/sGorpOqLqr090upx5fSvXdd+cbw0ssuHXv3zV9ojHijRjXaEDVcvzuHA1y/O4fTCC7P5sa1eZxtlWd8s0l50cpHu5/eeOllaImmaIvGqmcVzdFex+GzR214UCAcRD6CkRGps2C8JDUSFMtSrgVSev3va/ddu2DOg7/8yXfmbv3n35bqD1ev6To2xwbIhY2CGCDWDHBAMw4xOI1ArBGM2ygW9bXKM75ZyxU9d8YeGqGVavYVtENDtERTtEVjtEZz1fKo5uj86FscjmmETZIh8UmQ11vdcXoVBYEURbFx7R/3LF/8xbXf/nLhu3cvu/wLzzz2qwUb14388JW/PnH1Ky88sSSC63TcFWx6/vHrX3r+zzfU6z7/X9yEOsoS26QcuN3ah5N3CRqoFj945rFfL0AbNEIrNEM7NERLNEVbNI5rDr2g89hGCJs0tFBq+gekmiaszCpJSepSnFBQTBcVX1n/p7H7Vl83uvrWb2y8c+n8DXfeNP+5lUvn/6Xb+Nmyr69bdfOC9U+P3MOPAhXdC+cSbVCF2ErlwO32fsjPuaMBWty3+ppRtNF9hV/qUDPVDQ3REk3RtllzdG3sFUQMhM+kEzeLPzko1b68VPoHpDy7KsVCRqbyEzJV9mQqW5SpHHYaVZGpbkJ/MJvKaH2+JA+sWrp157bNt+hmaQ5NCvGfbVtuIQYHLmu6uSdyZ6fPP9RCNUEbNEIrNEM7NERLNB0e1t1JCN1+44OTaRw54g2GReoj+lwi6VsiNYq8855UqrOkxDdCKlKczMkUGwF6853qJiZ8Keaz+nKq38hCj5Ruv+K8pw6O77lft0yTPMa3XnH2U8S44uGyppt7IjfnDtACTdAGjdAKzdAODdFyWHRn0rw5ej6JVxAcQ9io9evFp8jg+VId65HqaF63IlLp2S/lfXsPY+KAlLqJ/fuldLAo5fJxUvLrUqpUpXzrVRetKhUnHgWM8RGDA5c13dwTue380UJFq6ANGqEVmqFd2sbo+vBIcwWFxMhH2Cj9FvgU3LxZaiNvSG1IccF2qYJZ26WWhGOJc8ITA3oF61XcOynl3j4p7d7974nFXxq4GTDGR6yqHLisOZaaadZy7gAt0ARt0AitWm2M6d1Og2wtltdCUGcDhhERv6sYkfDLMetsqZV8qXKF5MpSrlekBBjjIwbHROrqnvSc7fyxKk5dgTZAh+0dx9qg9qp2ZlX4lin6o8BJordbfXk5WJAyGNMxPmL67TWhOlP1A87yUW4QUoVN4v6ukxq3MsAY30e9OXoeLb0kwP8wgmdiwG0sCr3NcGsBH8Y9p97T/wAAAP//6j/uZQAAAAZJREFUAwCCi0rXDnMBRwAAAABJRU5ErkJggg==") no-repeat center center;
  height: 87rpx;
  width: 208rpx;
  text-align: center;
  line-height: 70rpx;
  border: none;
}
.header-section .search-container.data-v-d4142aa4 {
  display: flex;
}
.header-section .search-container .search-box.data-v-d4142aa4 {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
}
.header-section .search-container .search-box .search-icon.data-v-d4142aa4 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.header-section .search-container .search-box .search-input.data-v-d4142aa4 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  outline: none;
}
.main-content.data-v-d4142aa4 {
  display: flex;
  height: calc(100vh - 200rpx);
  padding: 20rpx 0;
  gap: 20rpx;
}
.category-sidebar.data-v-d4142aa4 {
  width: 200rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 0;
  overflow-y: auto;
}
.category-sidebar .category-item.data-v-d4142aa4 {
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  border-left: 4rpx solid transparent;
  cursor: pointer;
}
.category-sidebar .category-item.active.data-v-d4142aa4 {
  color: #FF9F18;
  background-color: #FFF5E6;
  border-left-color: #FF9F18;
  font-weight: 500;
}
.book-list.data-v-d4142aa4 {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  overflow-y: auto;
}
.book-list .book-item.data-v-d4142aa4 {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  cursor: pointer;
}
.book-list .book-item .book-cover.data-v-d4142aa4 {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.book-list .book-item .book-info.data-v-d4142aa4 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.book-list .book-item .book-info .book-title.data-v-d4142aa4 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.book-list .book-item .book-info .book-author.data-v-d4142aa4 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.book-list .book-item .book-info .book-publisher.data-v-d4142aa4 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.book-list .book-item .book-info .book-actions.data-v-d4142aa4 {
  display: flex;
  gap: 12rpx;
}
.book-list .book-item .book-info .book-actions .collect-btn.data-v-d4142aa4 {
  font-size: 24rpx;
  color: #FF9F18;
  background-color: #FFF5E6;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #FFE4B3;
}
.book-list .book-item .book-info .book-actions .borrowed-btn.data-v-d4142aa4 {
  font-size: 24rpx;
  color: #fff;
  background-color: #52C41A;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}
.book-list .book-item .add-icon.data-v-d4142aa4 {
  position: absolute;
  bottom: 20rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #FF9F18;
  color: #fff;
  border-radius: 50%;
  display: flex;
  line-height: 40rpx;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 159, 24, 0.3);
}