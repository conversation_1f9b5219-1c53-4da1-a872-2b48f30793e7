"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    const bookInfo = common_vendor.ref({
      id: 1,
      title: "世界是这样知道毛泽东的：《毛泽东自传》溯源：珍藏版",
      author: "丁小平",
      category: "哲学类",
      shelfDate: "2025-07-08",
      availableCount: 2,
      community: "前进社区",
      phone: "13879067204",
      introduction: "本书以毛泽东最早的传记《毛泽东自传》为主要研究对象，重点考察二十世纪三四十年代，毛泽东的生平事迹是如何被传播出去的。",
      publisher: "中共党史出版社",
      publishYear: "2023",
      classification: "马克思主义/列宁主义/毛泽东思想/邓小平理论",
      language: "汉语",
      isbn: "978-7-5098-5998-8",
      cover: "/static/books/mzd.jpg",
      isCollected: false,
      status: "available"
    });
    const shareBook = () => {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    };
    const toggleCollect = () => {
      bookInfo.value.isCollected = !bookInfo.value.isCollected;
      common_vendor.index.showToast({
        title: bookInfo.value.isCollected ? "收藏成功" : "取消收藏",
        icon: "success"
      });
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: bookInfo.value.phone,
        success: () => {
          console.log("拨打电话成功");
        },
        fail: (err) => {
          console.log("拨打电话失败", err);
        }
      });
    };
    common_vendor.onLoad((options) => {
      if (options.id) {
        const bookId = parseInt(options.id);
        loadBookDetail(bookId);
      }
    });
    const loadBookDetail = (bookId) => {
      console.log("加载图书详情，ID:", bookId);
      const mockBooks = {
        1: {
          id: 1,
          title: "如何改变世界：马克思和马克思主义的传奇",
          author: "(英)埃里克·霍布斯鲍姆(Eric Hobsbawm)",
          category: "马克思主义/列宁主义/毛泽东思想",
          shelfDate: "2025-01-10",
          availableCount: 3,
          community: "前进社区",
          phone: "13879067204",
          introduction: "本书是著名历史学家埃里克·霍布斯鲍姆关于马克思和马克思主义的经典著作。作者以独特的视角，深入分析了马克思思想的形成、发展和影响，以及马克思主义在20世纪世界历史进程中的重要作用。本书不仅是一部思想史著作，更是一部关于如何改变世界的深刻思考。",
          publisher: "中央编译出版社",
          publishYear: "2024",
          classification: "马克思主义/列宁主义/毛泽东思想/邓小平理论",
          language: "汉语",
          isbn: "978-7-5117-1234-5",
          cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mks.jpg",
          isCollected: true,
          status: "available"
        },
        2: {
          id: 2,
          title: "新的历史条件下马克思政治经济学新探索：21世纪马克思主义政治经济学创新研究",
          author: "李济广",
          category: "经济类",
          shelfDate: "2025-01-15",
          availableCount: 1,
          community: "前进社区",
          phone: "13879067204",
          introduction: "本书深入探讨了21世纪马克思主义政治经济学的新发展，为新时代经济理论创新提供了重要参考。作者结合当代经济实践，对马克思主义政治经济学的基本原理进行了创新性阐释，为构建中国特色社会主义政治经济学理论体系提供了有益探索。",
          publisher: "中国经济出版社",
          publishYear: "2023",
          classification: "经济",
          language: "汉语",
          isbn: "978-7-5136-5678-9",
          cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mks2.jpeg",
          isCollected: false,
          status: "borrowed"
        },
        3: {
          id: 3,
          title: "世界是这样知道毛泽东的：《毛泽东自传》溯源：珍藏版",
          author: "丁晓平著",
          category: "哲学类",
          shelfDate: "2025-07-08",
          availableCount: 2,
          community: "前进社区",
          phone: "13879067204",
          introduction: "本书以毛泽东最早的传记《毛泽东自传》为主要研究对象，重点考察二十世纪三四十年代，毛泽东的生平事迹是如何被传播出去的。作者通过大量珍贵的历史文献和档案资料，详细梳理了《毛泽东自传》的创作背景、传播过程和历史影响，为读者了解毛泽东早期革命生涯提供了重要参考。",
          publisher: "中共党史出版社",
          publishYear: "2023",
          classification: "马克思主义/列宁主义/毛泽东思想/邓小平理论",
          language: "汉语",
          isbn: "978-7-5098-5998-8",
          cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mzd.jpg",
          isCollected: true,
          status: "available"
        },
        4: {
          id: 4,
          title: "抗战颂歌毛泽东",
          author: "徐条权著",
          category: "历史类",
          shelfDate: "2025-02-20",
          availableCount: 4,
          community: "前进社区",
          phone: "13879067204",
          introduction: "本书以抗日战争为背景，全面展现了毛泽东在抗战时期的卓越领导才能和战略思想。作者通过详实的历史资料，生动描述了毛泽东如何运筹帷幄，领导中国人民取得抗日战争的伟大胜利。本书不仅是一部历史著作，更是一曲对毛泽东抗战功绩的颂歌。",
          publisher: "江苏人民出版社",
          publishYear: "2022",
          classification: "历史/地理",
          language: "汉语",
          isbn: "978-7-214-2345-6",
          cover: "https://qlzhsq.qlzhsq.cn:30210/images/books/mzd2.jpg",
          isCollected: false,
          status: "available"
        }
      };
      if (mockBooks[bookId]) {
        bookInfo.value = mockBooks[bookId];
      } else {
        common_vendor.index.showToast({
          title: "图书信息不存在",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "share",
          size: "18",
          color: "#fff"
        }),
        b: common_vendor.o(shareBook),
        c: bookInfo.value.cover,
        d: common_vendor.t(bookInfo.value.title),
        e: common_vendor.t(bookInfo.value.author),
        f: common_vendor.t(bookInfo.value.category),
        g: common_vendor.t(bookInfo.value.shelfDate),
        h: common_vendor.t(bookInfo.value.availableCount),
        i: common_vendor.t(bookInfo.value.isCollected ? "取消收藏" : "收藏书籍"),
        j: common_vendor.o(toggleCollect),
        k: common_vendor.t(bookInfo.value.community),
        l: common_vendor.t(bookInfo.value.phone),
        m: common_vendor.o(callPhone),
        n: common_vendor.t(bookInfo.value.introduction),
        o: common_vendor.t(bookInfo.value.publisher),
        p: common_vendor.t(bookInfo.value.publishYear),
        q: common_vendor.t(bookInfo.value.classification),
        r: common_vendor.t(bookInfo.value.language),
        s: common_vendor.t(bookInfo.value.isbn)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ce5e25b7"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/library/detail.vue"]]);
wx.createPage(MiniProgramPage);
