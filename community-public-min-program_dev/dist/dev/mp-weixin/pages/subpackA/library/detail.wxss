/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.book-detail.data-v-ce5e25b7 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header-section.data-v-ce5e25b7 {
  background: #FBA62D;
  padding: 20rpx 32rpx;
}
.header-section .header-content.data-v-ce5e25b7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-section .header-content .header-left.data-v-ce5e25b7 {
  display: flex;
  align-items: center;
}
.header-section .header-content .header-left .back-btn.data-v-ce5e25b7 {
  margin-right: 20rpx;
  padding: 8rpx;
}
.header-section .header-content .header-left .header-title.data-v-ce5e25b7 {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}
.header-section .header-content .share-btn.data-v-ce5e25b7 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
}
.header-section .header-content .share-btn .share-text.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #fff;
  margin-left: 8rpx;
}
.book-info-card.data-v-ce5e25b7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}
.book-info-card .book-info-content.data-v-ce5e25b7 {
  display: flex;
  gap: 24rpx;
}
.book-info-card .book-info-content .book-cover-section .book-cover.data-v-ce5e25b7 {
  width: 200rpx;
  height: 280rpx;
  border-radius: 12rpx;
}
.book-info-card .book-info-content .book-details.data-v-ce5e25b7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}
.book-info-card .book-info-content .book-details .book-title.data-v-ce5e25b7 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.book-info-card .book-info-content .book-details .book-meta .meta-item.data-v-ce5e25b7 {
  display: flex;
  margin-bottom: 10rpx;
}
.book-info-card .book-info-content .book-details .book-meta .meta-item .meta-label.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}
.book-info-card .book-info-content .book-details .book-meta .meta-item .meta-value.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.book-info-card .book-info-content .book-details .book-meta .meta-item .meta-value.borrowed.data-v-ce5e25b7 {
  color: #ff6b6b;
  font-weight: 500;
}
.book-info-card .book-info-content .book-details .action-buttons.data-v-ce5e25b7 {
  position: absolute;
  bottom: 10rpx;
  right: 0;
  display: flex;
  gap: 20rpx;
}
.book-info-card .book-info-content .book-details .action-buttons .collect-btn.data-v-ce5e25b7 {
  text-decoration: underline;
}
.book-info-card .book-info-content .book-details .action-buttons .collect-btn .collect-text.data-v-ce5e25b7 {
  font-size: 26rpx;
  color: #666;
}
.book-info-card .book-info-content .book-details .action-buttons .borrow-btn.data-v-ce5e25b7 {
  background-color: #FBA62D;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}
.book-info-card .book-info-content .book-details .action-buttons .borrow-btn .borrow-text.data-v-ce5e25b7 {
  font-size: 24rpx;
  color: #fff;
}
.card-content .card-title.data-v-ce5e25b7 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.card-content .info-row.data-v-ce5e25b7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.card-content .info-row.data-v-ce5e25b7:last-child {
  border-bottom: none;
}
.card-content .info-row .info-label.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #666;
}
.card-content .info-row .info-value.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #333;
  text-align: right;
}
.card-content .info-row .info-value.phone.data-v-ce5e25b7 {
  color: #FBA62D;
  text-decoration: underline;
}
.community-card.data-v-ce5e25b7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}
.intro-card.data-v-ce5e25b7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}
.intro-card .intro-text.data-v-ce5e25b7 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.publication-card.data-v-ce5e25b7 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
}
.publication-card .publication-info .info-row .info-value.data-v-ce5e25b7 {
  max-width: 400rpx;
  word-break: break-all;
}