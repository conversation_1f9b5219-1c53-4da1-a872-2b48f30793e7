"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_activity = require("../../../common/api/activity.js");
require("../../../common/config.js");
const pages_activity_common = require("../../activity/common.js");
require("../../../common/request.js");
require("../../../common/api/system.js");
require("../../../common/md5.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_avatar2 = common_vendor.resolveComponent("uv-avatar");
  const _easycom_a_popup2 = common_vendor.resolveComponent("a-popup");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  (_easycom_uv_icon2 + _easycom_uv_avatar2 + _easycom_a_popup2 + _easycom_uv_button2)();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_avatar = () => "../../../uni_modules/uv-avatar/components/uv-avatar/uv-avatar.js";
const _easycom_a_popup = () => "../../../components/a-popup/a-popup.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_uv_avatar + _easycom_a_popup + _easycom_uv_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "activity-details",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const pageData = common_vendor.ref(null), contentCall = common_vendor.ref("已报名成功。"), popup = common_vendor.ref();
    const toNav = () => {
      const { longitude, latitude, location } = pageData.value;
      common_vendor.index.navigateTo({
        url: `/pages/map/navigation/navigation?longitude=${longitude}&latitude=${latitude}&name=${location}&address=${location}`
      });
    };
    const disabledBtn = () => {
      var _a, _b, _c;
      if (!pageData.value) {
        return false;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0) {
        return true;
      }
      return ((_b = pageData.value) == null ? void 0 : _b.signUp) === 1 || ((_c = pageData.value) == null ? void 0 : _c.status) !== "0";
    };
    const callPhone = () => {
      var _a, _b;
      if (!((_a = pageData.value) == null ? void 0 : _a.contactPhone)) {
        contentCall.value = "当前无配置联系方式。";
        popup.value.open();
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: (_b = pageData.value) == null ? void 0 : _b.contactPhone
      });
    };
    const getPageList = async (id) => {
      const res = await common_api_activity.getActivityInfo(id);
      if ((res == null ? void 0 : res.code) === 200) {
        console.log(res.data);
        const labelList = await pages_activity_common.getSysLabelList();
        const audience = await pages_activity_common.getSysAudience();
        pageData.value = res.data;
        pageData.value.labelText = labelList[res.data.label];
        pageData.value.audienceText = audience[res.data.targetAudience];
      } else {
        throw console.log(res);
      }
    };
    const submit = async () => {
      common_vendor.index.showLoading({
        title: "提交中",
        mask: true
      });
      const res = await common_api_activity.addActivity({ activityId: pageData.value.activityId });
      if ((res == null ? void 0 : res.code) === 200) {
        contentCall.value = "已报名成功。";
        popup.value.open();
        common_vendor.index.hideLoading();
        getPageList(pageData.value.activityId);
      } else {
        common_vendor.index.hideLoading();
        throw console.log(res);
      }
    };
    common_vendor.onLoad(async (option) => {
      getPageList(option.id);
    });
    common_vendor.onHide(() => {
      common_vendor.index.setStorageSync("isBack", true);
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t;
      return {
        a: common_vendor.t((_a = pageData.value) == null ? void 0 : _a.labelText),
        b: common_vendor.t((_c = common_vendor.unref(pages_activity_common.handleStatus)((_b = pageData.value) == null ? void 0 : _b.status)) == null ? void 0 : _c.text),
        c: (_e = common_vendor.unref(pages_activity_common.handleStatus)((_d = pageData.value) == null ? void 0 : _d.status)) == null ? void 0 : _e.color,
        d: common_vendor.t((_f = pageData.value) == null ? void 0 : _f.title),
        e: common_vendor.t((_g = pageData.value) == null ? void 0 : _g.subtitle),
        f: common_vendor.f((_h = pageData.value) == null ? void 0 : _h.registrationVos, (item, i, i0) => {
          return common_vendor.e({
            a: i < 4
          }, i < 4 ? {
            b: 26 * i + "px",
            c: i + 1,
            d: item.avatar
          } : {}, {
            e: i
          });
        }),
        g: common_vendor.t(((_i = pageData.value) == null ? void 0 : _i.participate) ? (_j = pageData.value) == null ? void 0 : _j.participate : 0),
        h: 37 * ((_l = (_k = pageData.value) == null ? void 0 : _k.registrationVos) == null ? void 0 : _l.length) + "px",
        i: common_vendor.p({
          name: "clock-fill",
          color: "#999999",
          size: "14"
        }),
        j: common_vendor.t(((_m = pageData.value) == null ? void 0 : _m.activityStartTime) + "~" + ((_n = pageData.value) == null ? void 0 : _n.activityEndTime)),
        k: common_vendor.p({
          name: "map-fill",
          color: "#999999",
          size: "14"
        }),
        l: common_vendor.t((_o = pageData.value) == null ? void 0 : _o.location),
        m: common_vendor.o(toNav),
        n: common_vendor.p({
          src: (_p = pageData.value) == null ? void 0 : _p.avatar
        }),
        o: common_vendor.t((_q = pageData.value) == null ? void 0 : _q.contactName),
        p: common_vendor.t((_r = pageData.value) == null ? void 0 : _r.contactPhone),
        q: common_vendor.o(callPhone),
        r: common_vendor.t((_s = pageData.value) == null ? void 0 : _s.audienceText),
        s: (_t = pageData.value) == null ? void 0 : _t.content,
        t: common_vendor.sr(popup, "acdee82a-3", {
          "k": "popup"
        }),
        v: common_vendor.p({
          content: contentCall.value,
          confimText: "已知晓",
          cancelText: ""
        }),
        w: common_vendor.o(submit),
        x: common_vendor.p({
          disabled: disabledBtn(),
          type: disabledBtn() ? "primary" : "warning",
          text: "立即参与",
          customStyle: "margin-top: 10px;margin-bottom: 10px;"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-acdee82a"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/release/activity-details.vue"]]);
wx.createPage(MiniProgramPage);
