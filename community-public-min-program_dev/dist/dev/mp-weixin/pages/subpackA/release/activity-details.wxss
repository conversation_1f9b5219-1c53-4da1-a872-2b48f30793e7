/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.flex_row.data-v-acdee82a {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.flex_warp.data-v-acdee82a {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
view.data-v-acdee82a .uv-button--primary {
  background-color: #999999 !important;
}
.mg.data-v-acdee82a {
  margin-bottom: 16rpx;
}
.top.data-v-acdee82a {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #FFFFFF;
  background: linear-gradient(180deg, #FFCD35 0%, #FF9F18 100%);
  padding: 32rpx 0 52rpx 32rpx;
}
.content_Body.data-v-acdee82a {
  background: linear-gradient(180deg, #F5F7FF 0%, #F5F5F5 100%, #FFFFFF 100%);
  padding-top: 32rpx;
  margin-top: -30rpx;
  z-index: 999 !important;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  box-sizing: border-box;
}
.content.data-v-acdee82a {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
}
.content_view.data-v-acdee82a {
  padding: 0 32rpx 20rpx 32rpx;
  background: #F5F7FF;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content_view .title_flex.data-v-acdee82a {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.content_view .title.data-v-acdee82a {
  font-weight: 500;
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
}
.content_view .label.data-v-acdee82a {
  width: 86rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: #FFFFFF;
  line-height: 32rpx;
  padding: 4rpx 8rpx;
  box-sizing: border-box;
  text-align: center;
  border-radius: 4rpx;
  margin-right: 8rpx;
}
.content_view .content.data-v-acdee82a {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
}
.content_view .count.data-v-acdee82a {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #CCCCCC;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}
.name.data-v-acdee82a {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  margin-left: 20rpx;
}
.tip.data-v-acdee82a {
  background: #FFFFFF;
  padding: 28rpx 32rpx 0;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  text-align: justify;
  font-style: normal;
}
.tip .tip_title.data-v-acdee82a {
  font-weight: 500;
  font-size: 28rpx;
  color: #222222;
  line-height: 44rpx;
  margin-bottom: 16rpx;
}
.tip .tip_title .time.data-v-acdee82a, .tip .tip_title .location.data-v-acdee82a {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
  margin-left: 6rpx;
}
.tip .tip_title.data-v-acdee82a::before {
  content: "";
  display: inline-block;
  background: #ffffff;
  width: 4px;
  height: 4px;
  border: 4px solid #FF9F18;
  border-radius: 100%;
  margin-right: 8rpx;
}
.tip .contact_tip.data-v-acdee82a {
  background: #F5F5F5;
  border-radius: 16rpx;
  padding: 24rpx 24rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tip .contact_tip image.data-v-acdee82a {
  width: 40px;
  height: 40px;
}
.tip .contact_tip .contact_name.data-v-acdee82a {
  margin-left: 8rpx;
}