/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.flex_row.data-v-7192fda9 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.flex_warp.data-v-7192fda9 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
view.data-v-7192fda9 .uv-button--primary {
  background-color: #999999 !important;
}
.mg.data-v-7192fda9 {
  margin: 8rpx 0;
}
.top.data-v-7192fda9 {
  width: 100%;
  height: 550rpx;
  background: url("https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/2c3406a9754a4a75871fa17923f9f635.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.content_Body.data-v-7192fda9 {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%, #FFFFFF 100%);
  padding-top: 28rpx;
  margin-top: -26rpx;
  z-index: 999 !important;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.content_view.data-v-7192fda9 {
  padding: 0 32rpx 20rpx 32rpx;
  background: #ffffff;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content_view .time_backg.data-v-7192fda9 {
  background: rgba(255, 159, 24, 0.2);
  border-radius: 8rpx;
}
.content_view .time_bg.data-v-7192fda9 {
  width: 180rpx;
  height: 72rpx;
  background: url("https://qlzhsq.qlzhsq.cn:30204/wisdom-community/2025/02/28/40a43b210ab84935ad4a30c1a0842dc4.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.content_view .time_bg.data-v-7192fda9::after {
  content: "活动时间";
  line-height: 72rpx;
  font-family: YouSheBiaoTiHei;
  font-size: 36rpx;
  font-weight: 450;
  color: #FFFFFF;
  text-align: center;
  font-style: italic;
}
.content_view .time_desc.data-v-7192fda9 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  padding: 10rpx 14rpx;
}
.content_view .title_flex.data-v-7192fda9 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.content_view .title.data-v-7192fda9 {
  font-family: AppleColorEmoji;
  font-size: 34rpx;
  font-weight: 550;
  color: #222222;
  text-align: left;
  font-style: normal;
}
.content_view .label.data-v-7192fda9 {
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF;
  text-align: center;
  background: #04B578;
  border-radius: 4rpx;
  line-height: 32rpx;
  padding: 4rpx 8rpx;
}
.content_view .content.data-v-7192fda9 {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
}
.content_view .name.data-v-7192fda9 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}
.content_view .count.data-v-7192fda9 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #CCCCCC;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}
.tip.data-v-7192fda9 {
  background: #FFFFFF;
  padding: 28rpx 32rpx;
  margin: 14rpx 0;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  text-align: justify;
  font-style: normal;
}
.tip .tip_title.data-v-7192fda9 {
  font-weight: 550;
  font-size: 28rpx;
  color: #222222;
}