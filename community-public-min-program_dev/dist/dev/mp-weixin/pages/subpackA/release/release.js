"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_config = require("../../../common/config.js");
const common_api_assistance = require("../../../common/api/assistance.js");
const stores_store = require("../../../stores/store.js");
const common_utils_common = require("../../../common/utils/common.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_radio2 = common_vendor.resolveComponent("uv-radio");
  const _easycom_uv_radio_group2 = common_vendor.resolveComponent("uv-radio-group");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_a_record2 = common_vendor.resolveComponent("a-record");
  const _easycom_uv_upload2 = common_vendor.resolveComponent("uv-upload");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_datetime_picker2 = common_vendor.resolveComponent("uv-datetime-picker");
  (_easycom_uv_radio2 + _easycom_uv_radio_group2 + _easycom_uv_form_item2 + _easycom_uv_input2 + _easycom_uv_textarea2 + _easycom_a_audio2 + _easycom_a_record2 + _easycom_uv_upload2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_uv_datetime_picker2)();
}
const _easycom_uv_radio = () => "../../../uni_modules/uv-radio/components/uv-radio/uv-radio.js";
const _easycom_uv_radio_group = () => "../../../uni_modules/uv-radio/components/uv-radio-group/uv-radio-group.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_a_record = () => "../../../components/a-record/a-record.js";
const _easycom_uv_upload = () => "../../../uni_modules/uv-upload/components/uv-upload/uv-upload.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_datetime_picker = () => "../../../uni_modules/uv-datetime-picker/components/uv-datetime-picker/uv-datetime-picker.js";
if (!Math) {
  (_easycom_uv_radio + _easycom_uv_radio_group + _easycom_uv_form_item + _easycom_uv_input + _easycom_uv_textarea + _easycom_a_audio + _easycom_a_record + _easycom_uv_upload + _easycom_uv_button + _easycom_uv_form + _easycom_uv_datetime_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "release",
  setup(__props) {
    stores_store.useUserStore();
    stores_store.useAreaStore();
    const rules = Object.freeze({
      wishType: {
        type: "string",
        required: true,
        message: "请选择心愿类型",
        trigger: ["blur", "change"]
      },
      title: {
        type: "string",
        required: true,
        message: "请填写主题名称",
        trigger: ["blur", "change"]
      },
      content: {
        type: "string",
        required: true,
        message: "请填写需求/服务详情",
        trigger: ["blur", "change"]
      },
      locationInfo: {
        type: "string",
        required: true,
        message: "请填写具体位置",
        trigger: ["blur", "change"]
      },
      registrationDeadline: {
        type: "string",
        required: true,
        message: "请选择报名截至时间",
        trigger: ["blur", "change"]
      },
      activityStartTime: {
        type: "string",
        required: true,
        message: "请选择报名开始时间",
        trigger: ["blur", "change"]
      },
      activityEndTime: {
        type: "string",
        required: true,
        message: "请选择报名结束时间",
        trigger: ["blur", "change"]
      },
      contactName: {
        type: "string",
        required: true,
        message: "请输入联系人",
        trigger: ["blur", "change"]
      },
      contactPhone: {
        type: "string",
        required: true,
        message: "请输入正确的联系电话",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      }
      // registrationCount: {
      // 	type: 'string',
      // 	required: true,
      // 	message: '请输入人数',
      // 	pattern: /^[0-9]+$/,
      // 	trigger: ['blur', 'change']
      // },
    });
    const fileList = common_vendor.ref([]), audioFile = common_vendor.ref({}), videoList = common_vendor.ref([]);
    let form = common_vendor.reactive({
      wishType: "",
      title: "",
      content: "",
      voiceId: null,
      videoId: null,
      attachmentIds: [],
      location: "",
      locationInfo: "",
      registrationDeadline: "",
      activityStartTime: "",
      activityEndTime: "",
      contactName: "",
      contactPhone: ""
      // registrationCount: null
    });
    const formRef = common_vendor.ref(), datetimePickerRef = common_vendor.ref();
    let timeType = common_vendor.ref(), minTime = common_vendor.ref(Date.now());
    const open = (type) => {
      timeType.value = type;
      switch (timeType.value) {
        case 1:
          minTime.value = form.activityStartTime ? new Date(form.activityStartTime).valueOf() : Date.now();
          break;
        case 2:
          minTime.value = Date.now();
          break;
        case 3:
          minTime.value = form.registrationDeadline ? new Date(form.registrationDeadline).valueOf() : Date.now();
          break;
      }
      datetimePickerRef.value.open();
    };
    const confirm = (e) => {
      switch (timeType.value) {
        case 1:
          form.registrationDeadline = common_utils_common.formatDate(new Date(e.value));
          break;
        case 2:
          form.activityStartTime = common_utils_common.formatDate(new Date(e.value));
          break;
        case 3:
          form.activityEndTime = common_utils_common.formatDate(new Date(e.value));
          break;
      }
    };
    const afterRead = (e, type) => {
      const listReq = [];
      e.file.map((item) => {
        listReq.push(uploadFile(item.url));
      });
      Promise.all(listReq).then((result) => {
        let resFile = [];
        result.map((item) => {
          resFile.push({ url: item.url, message: item.ossId });
        });
        if (type === 1) {
          fileList.value = fileList.value.concat(resFile);
        } else {
          videoList.value = resFile;
        }
      });
    };
    const deletePic = (e, type) => {
      if (type === 1) {
        fileList.value.splice(e.index, 1);
      } else {
        videoList.value.splice(e.index, 1);
      }
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    const getAudio = (res) => {
      form.voiceId = res.ossId;
      audioFile.value = res;
    };
    const getCurrentLocation = () => {
      common_vendor.index.chooseLocation({
        success: function(res) {
          console.log("选择位置成功:", res);
          if (res.name && res.address) {
            form.location = JSON.stringify({ longitude: res.longitude, latitude: res.latitude });
            form.locationInfo = res.name + " " + res.address;
          }
        },
        fail: function(err) {
          console.error("选择位置失败:", err);
          common_vendor.index.showToast({
            title: "请打开定位权限",
            icon: "none"
          });
        }
      });
    };
    const submit = async () => {
      form.attachmentIds = [];
      fileList.value.length && fileList.value.map((item) => {
        form.attachmentIds.push(item.message);
      });
      form.videoId = videoList.value.length ? videoList.value.at(-1).message : null;
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "提交中",
          mask: true
        });
        const res = await common_api_assistance.addWishlist(form);
        if ((res == null ? void 0 : res.code) === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "您发布的正在审核中，请耐心等待。",
            icon: "none"
          });
          common_vendor.index.setStorageSync("isBack", true);
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.hideLoading();
        }
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      var _a;
      return {
        a: common_vendor.f([{
          name: "心愿需求",
          value: "0"
        }, {
          name: "服务提供",
          value: "1"
        }], (item, index, i0) => {
          return {
            a: index,
            b: "4a91b9ce-3-" + i0 + ",4a91b9ce-2",
            c: common_vendor.p({
              customStyle: {
                margin: "8px"
              },
              label: item.name,
              name: item.value
            })
          };
        }),
        b: common_vendor.o(($event) => common_vendor.unref(form).wishType = $event),
        c: common_vendor.p({
          activeColor: "#FF9F18",
          modelValue: common_vendor.unref(form).wishType
        }),
        d: common_vendor.p({
          label: "类型选择",
          prop: "wishType",
          required: true
        }),
        e: common_vendor.o(($event) => common_vendor.unref(form).title = $event),
        f: common_vendor.p({
          placeholder: "用简短语句表达自己的心愿需求/服务提供",
          modelValue: common_vendor.unref(form).title
        }),
        g: common_vendor.p({
          label: "主题名称",
          prop: "title",
          required: true
        }),
        h: common_vendor.o(($event) => common_vendor.unref(form).content = $event),
        i: common_vendor.p({
          maxlength: "100",
          showConfirmBar: false,
          placeholder: "请输入需求/服务详情",
          modelValue: common_vendor.unref(form).content
        }),
        j: common_vendor.p({
          label: "需求/服务详情",
          prop: "content",
          required: true
        }),
        k: common_vendor.p({
          voiceUrl: (_a = audioFile.value) == null ? void 0 : _a.url
        }),
        l: common_vendor.o(getAudio),
        m: common_vendor.p({
          label: ""
        }),
        n: common_vendor.o((e) => {
          afterRead(e, 1);
        }),
        o: common_vendor.o((e) => {
          deletePic(e, 1);
        }),
        p: common_vendor.p({
          fileList: fileList.value,
          name: "1",
          multiple: true,
          maxCount: 5,
          previewFullImage: true
        }),
        q: common_vendor.t(fileList.value.length),
        r: common_vendor.p({
          label: "图片"
        }),
        s: common_vendor.o((e) => {
          afterRead(e, 2);
        }),
        t: common_vendor.o((e) => {
          deletePic(e, 2);
        }),
        v: common_vendor.p({
          fileList: videoList.value,
          accept: "video",
          name: "1",
          multiple: true,
          maxCount: 1,
          previewFullImage: true
        }),
        w: common_vendor.p({
          label: "视频"
        }),
        x: common_vendor.o(getCurrentLocation),
        y: common_vendor.o(($event) => common_vendor.unref(form).locationInfo = $event),
        z: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择具体位置",
          suffixIcon: "map-fill",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(form).locationInfo
        }),
        A: common_vendor.p({
          label: "具体位置",
          prop: "locationInfo",
          required: true
        }),
        B: common_vendor.o(($event) => open(2)),
        C: common_vendor.o(($event) => common_vendor.unref(form).activityStartTime = $event),
        D: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择报名开始时间",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(form).activityStartTime
        }),
        E: common_vendor.p({
          label: "活动开始时间",
          prop: "activityStartTime",
          required: true
        }),
        F: common_vendor.o(($event) => open(1)),
        G: common_vendor.o(($event) => common_vendor.unref(form).registrationDeadline = $event),
        H: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择报名截至时间",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(form).registrationDeadline
        }),
        I: common_vendor.p({
          label: "报名截至时间",
          prop: "registrationDeadline",
          required: true
        }),
        J: common_vendor.o(($event) => open(3)),
        K: common_vendor.o(($event) => common_vendor.unref(form).activityEndTime = $event),
        L: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择报名结束时间",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: common_vendor.unref(form).activityEndTime
        }),
        M: common_vendor.p({
          label: "活动结束时间",
          prop: "activityEndTime",
          required: true
        }),
        N: common_vendor.o(($event) => common_vendor.unref(form).contactName = $event),
        O: common_vendor.p({
          placeholder: "请输入联系人",
          modelValue: common_vendor.unref(form).contactName
        }),
        P: common_vendor.p({
          label: "联系人",
          prop: "contactName",
          required: true
        }),
        Q: common_vendor.o(($event) => common_vendor.unref(form).contactPhone = $event),
        R: common_vendor.p({
          type: "number",
          placeholder: "请输入联系电话",
          modelValue: common_vendor.unref(form).contactPhone
        }),
        S: common_vendor.p({
          label: "联系电话",
          prop: "contactPhone",
          required: true
        }),
        T: common_vendor.o(submit),
        U: common_vendor.p({
          type: "warning",
          text: "发布心愿",
          customStyle: "margin-top: 10px"
        }),
        V: common_vendor.sr(formRef, "4a91b9ce-0", {
          "k": "formRef"
        }),
        W: common_vendor.p({
          labelPosition: "left",
          model: common_vendor.unref(form),
          rules: common_vendor.unref(rules)
        }),
        X: common_vendor.sr(datetimePickerRef, "4a91b9ce-28", {
          "k": "datetimePickerRef"
        }),
        Y: common_vendor.o(confirm),
        Z: common_vendor.p({
          ["min-date"]: common_vendor.unref(minTime),
          mode: "datetime"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4a91b9ce"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/release/release.vue"]]);
wx.createPage(MiniProgramPage);
