"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_api_assistance = require("../../../common/api/assistance.js");
const common_utils_common = require("../../../common/utils/common.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_a_audio2 = common_vendor.resolveComponent("a-audio");
  const _easycom_uv_swiper2 = common_vendor.resolveComponent("uv-swiper");
  const _easycom_a_popup2 = common_vendor.resolveComponent("a-popup");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  (_easycom_uv_icon2 + _easycom_a_audio2 + _easycom_uv_swiper2 + _easycom_a_popup2 + _easycom_uv_button2)();
}
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_a_audio = () => "../../../components/a-audio/a-audio.js";
const _easycom_uv_swiper = () => "../../../uni_modules/uv-swiper/components/uv-swiper/uv-swiper.js";
const _easycom_a_popup = () => "../../../components/a-popup/a-popup.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_a_audio + _easycom_uv_swiper + _easycom_a_popup + _easycom_uv_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "assistance-details",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    const pageData = common_vendor.ref(null), personList = common_vendor.ref([]), contentCall = common_vendor.ref("已报名成功。请各位爱心人士在报名成功当天及时联系发布人,电话咨询具体对接事宜。"), popup = common_vendor.ref();
    let currentId = common_vendor.ref("");
    const formatDateYMD = (dateString) => {
      if (!dateString)
        return "";
      return dateString.split(" ")[0];
    };
    const toNav = () => {
      const { location, locationInfo } = pageData.value;
      common_vendor.index.navigateTo({
        url: `/pages/map/navigation/navigation?longitude=${JSON.parse(location).longitude}&latitude=${JSON.parse(location).latitude}&name=${locationInfo}&address=${locationInfo}`
      });
    };
    const disabledBtn = () => {
      var _a;
      if (!pageData.value) {
        return false;
      }
      if (((_a = userStore == null ? void 0 : userStore.userInfo) == null ? void 0 : _a.authentication) == 0) {
        return true;
      }
      return (pageData == null ? void 0 : pageData.value.signUp) === 1 || (pageData == null ? void 0 : pageData.value.status) !== "0" || userStore.userInfo.userId == (pageData == null ? void 0 : pageData.value.userId);
    };
    const loadAllData = async (id) => {
      currentId.value = id;
      await Promise.all([
        getPageData(id),
        getSignUpList(id)
      ]);
    };
    const getPageData = async (id) => {
      const res = await common_api_assistance.getWishlistsInfo(id);
      if ((res == null ? void 0 : res.code) === 200) {
        pageData.value = res.data;
        const date = new Date(pageData.value.registrationDeadline).getTime();
        pageData.value.registrationDeadline = common_utils_common.formatDate(new Date(date), 1);
      } else {
        console.log(res);
      }
    };
    const getSignUpList = async (id) => {
      const res = await common_api_assistance.getSingUp(id);
      if ((res == null ? void 0 : res.code) === 200) {
        personList.value = res.data;
      } else {
        console.log(res);
      }
    };
    const submit = async () => {
      common_vendor.index.showLoading({
        title: "提交中",
        mask: true
      });
      const res = await common_api_assistance.addSingUp({ wishlistId: pageData.value.id });
      if ((res == null ? void 0 : res.code) === 200) {
        popup.value.open();
        common_vendor.index.hideLoading();
        await loadAllData(currentId.value);
      } else {
        common_vendor.index.hideLoading();
        console.log(res);
      }
    };
    common_vendor.onLoad((option) => {
      if (option && option.id) {
        loadAllData(option.id);
      }
    });
    common_vendor.onHide(() => {
      common_vendor.index.setStorageSync("isBack", true);
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x;
      return common_vendor.e({
        a: common_vendor.t(formatDateYMD((_a = pageData.value) == null ? void 0 : _a.activityStartTime) + "至" + formatDateYMD((_b = pageData.value) == null ? void 0 : _b.activityEndTime)),
        b: common_vendor.t((_c = pageData.value) == null ? void 0 : _c.title),
        c: ((_d = pageData.value) == null ? void 0 : _d.status) === "-1"
      }, ((_e = pageData.value) == null ? void 0 : _e.status) === "-1" ? {} : {}, {
        d: ((_f = pageData.value) == null ? void 0 : _f.status) === "0"
      }, ((_g = pageData.value) == null ? void 0 : _g.status) === "0" ? {} : {}, {
        e: ((_h = pageData.value) == null ? void 0 : _h.status) === "1"
      }, ((_i = pageData.value) == null ? void 0 : _i.status) === "1" ? {} : {}, {
        f: common_vendor.p({
          name: "clock-fill",
          color: "#999999",
          size: "14"
        }),
        g: common_vendor.t((_j = pageData.value) == null ? void 0 : _j.registrationDeadline),
        h: common_vendor.p({
          name: "map-fill",
          color: "#999999",
          size: "14"
        }),
        i: common_vendor.t((_k = pageData.value) == null ? void 0 : _k.locationInfo),
        j: common_vendor.o(toNav),
        k: common_vendor.f(personList.value, (item, i, i0) => {
          return common_vendor.e({
            a: i < 4
          }, i < 4 ? {
            b: 26 * i - 2 + "px",
            c: i + 1,
            d: item.avatar
          } : {}, {
            e: i
          });
        }),
        l: common_vendor.t((_l = pageData.value) == null ? void 0 : _l.participate),
        m: 37 * ((_m = personList.value) == null ? void 0 : _m.length) + "px",
        n: (_n = pageData.value) == null ? void 0 : _n.voiceUrl
      }, ((_o = pageData.value) == null ? void 0 : _o.voiceUrl) ? {
        o: common_vendor.p({
          voiceUrl: (_p = pageData.value) == null ? void 0 : _p.voiceUrl
        })
      } : {}, {
        p: (_q = pageData.value) == null ? void 0 : _q.imageOssVos.length
      }, ((_r = pageData.value) == null ? void 0 : _r.imageOssVos.length) ? {
        q: common_vendor.p({
          height: "200",
          list: (_s = pageData.value) == null ? void 0 : _s.imageOssVos,
          keyName: "url",
          autoplay: true,
          interval: 3e3,
          duration: 500,
          indicatorStyle: "right: 20px; bottom: 20px;",
          imgMode: "aspectFit",
          showTitle: false
        })
      } : {}, {
        r: common_vendor.t((_t = pageData.value) == null ? void 0 : _t.content),
        s: (_u = pageData.value) == null ? void 0 : _u.videoUrl
      }, ((_v = pageData.value) == null ? void 0 : _v.videoUrl) ? {
        t: (_w = pageData.value) == null ? void 0 : _w.videoUrl
      } : {}, {
        v: common_vendor.t((_x = personList.value) == null ? void 0 : _x.length),
        w: common_vendor.f(personList.value, (item, i, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.nickname.slice(0, 1) + "*"),
            c: i
          };
        }),
        x: common_vendor.sr(popup, "7192fda9-4", {
          "k": "popup"
        }),
        y: common_vendor.p({
          content: contentCall.value
        }),
        z: common_vendor.o(submit),
        A: common_vendor.p({
          disabled: disabledBtn(),
          type: disabledBtn() ? "primary" : "warning",
          text: "立即参与",
          customStyle: "margin-top: 10px;margin-bottom: 10px;"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7192fda9"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/subpackA/release/assistance-details.vue"]]);
wx.createPage(MiniProgramPage);
