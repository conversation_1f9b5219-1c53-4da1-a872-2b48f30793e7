/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cart-container.data-v-d2f51ca0 {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-state.data-v-d2f51ca0 {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-list.data-v-d2f51ca0 {
  padding: 20rpx;
}
.cart-list .cart-item.data-v-d2f51ca0 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.cart-list .cart-item .item-info.data-v-d2f51ca0 {
  display: flex;
  align-items: center;
}
.cart-list .cart-item .item-info .item-image.data-v-d2f51ca0 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.cart-list .cart-item .item-info .item-details.data-v-d2f51ca0 {
  flex: 1;
}
.cart-list .cart-item .item-info .item-details .item-name.data-v-d2f51ca0 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}
.cart-list .cart-item .item-info .item-details .item-price.data-v-d2f51ca0 {
  font-size: 28rpx;
  color: #ff9f18;
  font-weight: 600;
}
.cart-list .cart-item .item-info .item-actions.data-v-d2f51ca0 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.cart-list .cart-item .item-info .item-actions .quantity-control.data-v-d2f51ca0 {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}
.cart-list .cart-item .item-info .item-actions .quantity-control .quantity-btn.data-v-d2f51ca0 {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666666;
}
.cart-list .cart-item .item-info .item-actions .quantity-control .quantity-input.data-v-d2f51ca0 {
  width: 80rpx;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-left: none;
  border-right: none;
  text-align: center;
  font-size: 28rpx;
}
.cart-footer.data-v-d2f51ca0 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.cart-footer .footer-content.data-v-d2f51ca0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cart-footer .footer-content .total-info .total-text.data-v-d2f51ca0 {
  font-size: 28rpx;
  color: #666666;
}
.cart-footer .footer-content .total-info .total-price.data-v-d2f51ca0 {
  font-size: 36rpx;
  color: #ff9f18;
  font-weight: 600;
}