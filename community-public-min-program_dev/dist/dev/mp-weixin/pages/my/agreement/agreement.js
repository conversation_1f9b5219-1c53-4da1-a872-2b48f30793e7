"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_enum = require("../../../common/enum.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "agreement",
  setup(__props) {
    const url = common_vendor.ref("");
    common_vendor.onLoad((options) => {
      console.log(options);
      switch (options == null ? void 0 : options.index) {
        case common_enum.AgreementType.USER_AGREEMENT:
          url.value = "https://qlzhsq.qlzhsq.cn:30200/user-agreement.html";
          break;
        case common_enum.AgreementType.PRIVACY_AGREEMENT:
          url.value = "https://qlzhsq.qlzhsq.cn:30200/privacy-agreement.html";
          break;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: url.value
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/agreement/agreement.vue"]]);
wx.createPage(MiniProgramPage);
