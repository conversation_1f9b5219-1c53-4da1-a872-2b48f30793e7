"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_empty2 = common_vendor.resolveComponent("uv-empty");
  _easycom_uv_empty2();
}
const _easycom_uv_empty = () => "../../../uni_modules/uv-empty/components/uv-empty/uv-empty.js";
if (!Math) {
  _easycom_uv_empty();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "borrow",
  setup(__props) {
    const activeTab = common_vendor.ref(0);
    const borrowList = common_vendor.ref([]);
    common_vendor.onLoad(() => {
      loadBorrowData();
    });
    const switchTab = (index) => {
      activeTab.value = index;
      loadBorrowData();
    };
    const loadBorrowData = () => {
      borrowList.value = [];
    };
    return (_ctx, _cache) => {
      return {
        a: activeTab.value === 0 ? 1 : "",
        b: common_vendor.o(($event) => switchTab(0)),
        c: activeTab.value === 1 ? 1 : "",
        d: common_vendor.o(($event) => switchTab(1)),
        e: activeTab.value === 2 ? 1 : "",
        f: common_vendor.o(($event) => switchTab(2)),
        g: common_vendor.p({
          mode: "data",
          text: "暂无借阅",
          show: borrowList.value.length === 0,
          marginTop: "100"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-22e407a0"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/borrow/borrow.vue"]]);
wx.createPage(MiniProgramPage);
