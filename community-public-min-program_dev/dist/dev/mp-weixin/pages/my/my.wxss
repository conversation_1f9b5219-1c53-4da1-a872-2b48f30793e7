/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-d3687551 {
  background: #f6f6f6;
  height: 100vh;
}
.popup_auth .font-size.data-v-d3687551 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 40rpx;
  text-align: center;
  width: 100%;
}
.popup_auth button.data-v-d3687551 {
  padding: 0;
  border: none;
  width: 100% !important;
  display: flex;
  justify-content: center;
}
.popup_auth.data-v-d3687551 .uv-input {
  width: 100% !important;
}
.popup_auth.data-v-d3687551 .uv-form-item__body {
  display: flex;
  flex-direction: row !important;
}
.popup_auth.data-v-d3687551 .uv-form-item__body__left {
  width: 50px !important;
  margin-bottom: 6rpx !important;
}
.popup_auth.data-v-d3687551 .uv-form-item__body__left__content__label {
  font-weight: 550;
  font-size: 28rpx;
  color: #333333 !important;
}
.popup_auth.data-v-d3687551 .uv-form-item__body__right__message {
  margin-left: 0 !important;
  line-height: none !important;
}
.mg.data-v-d3687551 {
  margin: 8rpx 0;
}
.menu.data-v-d3687551 {
  background: linear-gradient(180deg, #ffffff 2%, #f5f5f5 10%, #f5f5f5 100%);
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  margin-top: -45rpx;
  padding-bottom: 100rpx;
}
.menu .tip-first.data-v-d3687551 {
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}
.menu .tip.data-v-d3687551 {
  background: #ffffff;
  padding: 32rpx;
  box-sizing: border-box;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  font-style: normal;
}
.menu .scroll-view-item_H.data-v-d3687551 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.menu .scroll-view-item_H text.data-v-d3687551 {
  width: 100%;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 44rpx;
  text-align: center;
}
.menu .scroll-view-item_H image.data-v-d3687551 {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 4rpx;
}
.menu .menu-item.data-v-d3687551 {
  display: flex;
  justify-content: space-between;
  padding: 34rpx 22rpx;
  border-bottom: 2rpx solid #eee;
  color: #333;
  background: #ffffff;
  position: relative;
}
.menu .menu-item .mark.data-v-d3687551 {
  padding: 0 10rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  transform: translateY(-50%);
  background: #f24747;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #fff;
  top: 50%;
  right: 80rpx;
}
.menu .menu-item.data-v-d3687551:last-of-type {
  border-bottom: none;
}
.menu .menu-item > view.data-v-d3687551 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.menu .menu-item > view > text.data-v-d3687551 {
  margin-left: 4rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 44rpx;
}
.menu .menu-item > view image.data-v-d3687551 {
  width: 46rpx;
  height: 46rpx;
}
.content.data-v-d3687551 {
  padding: 18rpx 0 50rpx 0;
  background: linear-gradient(180deg, #ffcd35 0%, #ff9f18 100%);
}
.info.data-v-d3687551 {
  border-radius: 10rpx;
}
.info .info-detail.data-v-d3687551 {
  display: flex;
  align-items: center;
  padding: 14rpx 32rpx;
  box-sizing: border-box;
}
.info .info-detail .avatar.data-v-d3687551 {
  width: 120rpx;
  height: 120rpx;
  position: relative;
}
.info .info-detail .avatar > image.data-v-d3687551 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  flex-shrink: 0;
}
.info .info-detail > view.data-v-d3687551 {
  display: flex;
}
.info .info-detail > text.data-v-d3687551 {
  color: #999;
  font-size: 24rpx;
}
.info .info-detail .user.data-v-d3687551 {
  color: #fcfcfc;
  font-size: 38rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin-left: 30rpx;
  width: 100%;
}
.info .info-detail .user .username.data-v-d3687551 {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.info .info-detail .user .username image.data-v-d3687551 {
  width: 50rpx;
  height: 50rpx;
}
.info .info-detail .location.data-v-d3687551 {
  color: #fcfcfc;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}
.info .info-detail .location > text.data-v-d3687551 {
  margin-left: 10rpx;
}
.info .info-detail .tag.data-v-d3687551 {
  padding: 10rpx 16rpx;
  background: #ffffff;
  border-radius: 4rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #ff9f18;
  display: flex;
  align-items: center;
  position: relative;
}
.info .info-detail .tag .icon2.data-v-d3687551 {
  position: absolute;
  top: 10rpx;
  left: 36rpx;
  width: 20rpx;
  height: 20rpx;
}
.info .data.data-v-d3687551 {
  display: flex;
  justify-content: space-evenly;
}
.info .data .center_item.data-v-d3687551 {
  position: relative;
}
.info .data .center_item.data-v-d3687551::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -50rpx;
  width: 2rpx;
  height: 48rpx;
  background: #ffffff;
  border-radius: 1rpx;
  opacity: 0.5;
}
.info .data .center_item.data-v-d3687551::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -50rpx;
  width: 2rpx;
  height: 48rpx;
  background: #ffffff;
  border-radius: 1rpx;
  opacity: 0.5;
}
.info .data > view.data-v-d3687551 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}
.info .data > view > text.data-v-d3687551:first-of-type {
  font-size: 44rpx;
  font-weight: bold;
  color: #ffffff;
}
.info .data > view > text.data-v-d3687551:last-of-type {
  margin-top: 10rpx;
  font-size: 28rpx;
  color: #ffffff;
}
.list.data-v-d3687551 {
  display: flex;
  justify-content: space-evenly;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx 0;
  margin-top: 18rpx;
}
.list .icon-box.data-v-d3687551 {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 28rpx;
}
.list .icon-box image.data-v-d3687551 {
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 12rpx;
}
.wx-btn.data-v-d3687551 {
  width: 100rpx;
  height: 100rpx;
  line-height: 100rpx;
  border-radius: 50%;
  top: 0;
  right: 0;
  background: #fff;
  position: absolute;
  font-size: 30rpx;
  text-align: center;
}
.logout.data-v-d3687551 {
  background: #fff;
  border-radius: 10rpx;
  color: #f63f34;
  margin: 0 18rpx 18rpx 18rpx;
  padding: 28rpx;
  display: flex;
  justify-content: center;
  font-size: 28rpx;
}
.tip_title.data-v-d3687551 {
  font-weight: 500;
  font-size: 32rpx;
  color: #222222;
  line-height: 48rpx;
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.tip_title.data-v-d3687551::before {
  content: "";
  width: 8rpx;
  display: block;
  height: 28rpx;
  background: #eeb947;
  border-radius: 2rpx;
  margin-right: 8rpx;
}