"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_api_user = require("../../../../common/api/user.js");
require("../../../../common/config.js");
require("../../../../common/request.js");
if (!Array) {
  const _easycom_uv_loading_icon2 = common_vendor.resolveComponent("uv-loading-icon");
  _easycom_uv_loading_icon2();
}
const _easycom_uv_loading_icon = () => "../../../../uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon.js";
if (!Math) {
  _easycom_uv_loading_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    const title = common_vendor.ref("");
    const content = common_vendor.ref("");
    const createTime = common_vendor.ref("");
    const loading = common_vendor.ref(true);
    const formatDate = (dateString) => {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    common_vendor.onLoad((options) => {
      const messageId = (options == null ? void 0 : options.noticeId) || (options == null ? void 0 : options.id);
      if (!messageId) {
        common_vendor.index.showToast({
          title: "消息ID无效",
          icon: "none"
        });
        loading.value = false;
        return;
      }
      common_api_user.getDetails({
        noticeId: messageId
      }).then((res) => {
        loading.value = false;
        const response = res;
        if (response.code === 200 && response.data) {
          const {
            noticeTitle,
            noticeContent,
            title: newTitle,
            content: newContent,
            createTime: time
          } = response.data;
          title.value = newTitle || noticeTitle || "无标题";
          content.value = (newContent || noticeContent || "无内容").replace(/\<img/gi, '<img style="max-width:100%;height:auto" ');
          createTime.value = time || "";
        } else {
          common_vendor.index.showToast({
            title: response.message || "获取消息详情失败",
            icon: "none"
          });
        }
      }).catch((error) => {
        loading.value = false;
        console.error("获取消息详情出错:", error);
        common_vendor.index.showToast({
          title: "获取消息详情失败",
          icon: "none"
        });
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(title.value),
        b: createTime.value
      }, createTime.value ? {
        c: common_vendor.t(formatDate(createTime.value))
      } : {}, {
        d: loading.value
      }, loading.value ? {} : {
        e: content.value
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0860419b"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/message/detail/detail.vue"]]);
wx.createPage(MiniProgramPage);
