"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_user = require("../../../common/api/user.js");
const common_api_store = require("../../../common/api/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_loading_icon2 = common_vendor.resolveComponent("uv-loading-icon");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_uv_loading_icon2 + _easycom_uv_icon2 + _easycom_uv_popup2)();
}
const _easycom_uv_loading_icon = () => "../../../uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon.js";
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_popup = () => "../../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_uv_loading_icon + _easycom_uv_icon + _easycom_uv_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "message",
  setup(__props) {
    const pageParams = common_vendor.ref({
      pageNum: 1,
      pageSize: 10,
      total: 0
    });
    const activeTab = common_vendor.ref("system");
    const messageList = common_vendor.ref([]);
    const systemMessageList = common_vendor.ref([]);
    const marketMessageList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const isLoadingMore = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const scrollHeight = common_vendor.ref(500);
    const showPopup = common_vendor.ref(false);
    const currentMessage = common_vendor.ref({});
    const isLoadingDetail = common_vendor.ref(false);
    const hasMoreData = common_vendor.computed(() => {
      return messageList.value.length < pageParams.value.total;
    });
    const currentMessageList = common_vendor.computed(() => {
      return activeTab.value === "system" ? systemMessageList.value : marketMessageList.value;
    });
    const switchTab = (tab) => {
      if (activeTab.value === tab)
        return;
      activeTab.value = tab;
      pageParams.value.pageNum = 1;
      pageParams.value.total = 0;
      messageList.value = currentMessageList.value;
      if (currentMessageList.value.length === 0) {
        loadMessageList(true);
      }
    };
    const initPageHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight;
    };
    const formatDate = (dateString, showTime = false) => {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      if (showTime) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      }
      return `${year}-${month}-${day}`;
    };
    const getOtherUserAvatar = (item) => {
      var _a;
      const currentUserId = ((_a = common_vendor.index.getStorageSync("userInfo")) == null ? void 0 : _a.userId) || "";
      if (currentUserId === item.initiateUserId) {
        return item.receiverUserAvatar || "";
      } else if (currentUserId === item.receiverUserId) {
        return item.initiateUserAvatar || "";
      }
      return "";
    };
    const getOtherUserName = (item) => {
      var _a;
      const currentUserId = ((_a = common_vendor.index.getStorageSync("userInfo")) == null ? void 0 : _a.userId) || "";
      if (currentUserId === item.initiateUserId) {
        return item.receiverUserName || "未知用户";
      } else if (currentUserId === item.receiverUserId) {
        return item.initiateUserName || "未知用户";
      }
      return "未知用户";
    };
    const formatTimeForList = (dateString) => {
      if (!dateString)
        return "";
      const now = new Date();
      const date = new Date(dateString);
      const diffTime = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffTime / (1e3 * 60 * 60 * 24));
      if (diffDays === 0) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } else if (diffDays === 1) {
        return "昨天";
      } else if (diffDays <= 7) {
        const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        return weekdays[date.getDay()];
      } else {
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        return `${month}-${day}`;
      }
    };
    const loadMessageList = async (isRefresh = false) => {
      try {
        if (isRefresh) {
          pageParams.value.pageNum = 1;
          isLoading.value = true;
        } else {
          isLoadingMore.value = true;
        }
        let res;
        if (activeTab.value === "market") {
          const params = {
            pageNum: pageParams.value.pageNum,
            pageSize: pageParams.value.pageSize,
            orderByColumn: "createTime",
            // 按创建时间排序
            isAsc: "desc"
            // 降序排列，最新的在前面
          };
          res = await common_api_store.getConversationsList(params);
        } else {
          const params = {
            pageNum: pageParams.value.pageNum,
            pageSize: pageParams.value.pageSize,
            messageType: "system"
          };
          res = await common_api_user.getMessageList(params);
        }
        if (res.code === 200) {
          if (res.total !== void 0) {
            pageParams.value.total = res.total;
          }
          const newData = res.rows || [];
          if (activeTab.value === "system") {
            if (isRefresh) {
              systemMessageList.value = newData;
            } else {
              systemMessageList.value = [...systemMessageList.value, ...newData];
            }
          } else {
            if (isRefresh) {
              marketMessageList.value = newData;
            } else {
              marketMessageList.value = [...marketMessageList.value, ...newData];
            }
          }
          messageList.value = currentMessageList.value;
          hasMore.value = hasMoreData.value;
          if (pageParams.value.pageNum === 1 && (!res.rows || res.rows.length === 0)) {
            hasMore.value = false;
          }
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取消息列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取消息列表出错:", error);
        common_vendor.index.showToast({
          title: "获取消息列表失败",
          icon: "none"
        });
      } finally {
        isLoading.value = false;
        isLoadingMore.value = false;
      }
    };
    const loadMore = () => {
      if (!hasMore.value || isLoadingMore.value)
        return;
      pageParams.value.pageNum++;
      loadMessageList();
    };
    const refreshList = () => {
      loadMessageList(true);
    };
    const showMessageDetail = async (item) => {
      console.log("点击消息:", item);
      if (activeTab.value === "market" && item.id) {
        common_vendor.index.navigateTo({
          url: `/pages/subpackA/market/message?conversationId=${item.id}&goodsId=${item.businessId || ""}&goodsName=${encodeURIComponent(getOtherUserName(item))}&coverImage=${encodeURIComponent(item.businessUrl || "")}`
        });
        return;
      }
      try {
        if (item.content) {
          currentMessage.value = { ...item };
          showPopup.value = true;
          if (item.isRead === 0) {
            markAsRead(item);
          }
          return;
        }
        isLoadingDetail.value = true;
        common_vendor.index.showLoading({ title: "加载中..." });
        const result = await common_api_user.getDetails({ id: item.id });
        if (result.code === 200 && result.data) {
          currentMessage.value = {
            ...item,
            content: result.data.content || result.data.noticeContent || "无内容"
          };
          if (currentMessage.value.content) {
            currentMessage.value.content = currentMessage.value.content.replace(/\<img/gi, '<img style="max-width:100%;height:auto" ');
          }
          showPopup.value = true;
          item.content = currentMessage.value.content;
          if (item.isRead === 0) {
            markAsRead(item);
          }
        } else {
          common_vendor.index.showToast({
            title: "获取消息详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取消息详情出错:", error);
        common_vendor.index.showToast({
          title: "获取消息详情失败",
          icon: "none"
        });
      } finally {
        isLoadingDetail.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const closePopup = () => {
      showPopup.value = false;
      setTimeout(() => {
        currentMessage.value = {};
      }, 300);
    };
    const markAsRead = async (item) => {
      try {
        item.isRead = 1;
      } catch (error) {
        console.error("标记消息已读失败:", error);
      }
    };
    common_vendor.onLoad(() => {
      initPageHeight();
      refreshList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: activeTab.value === "system" ? 1 : "",
        b: common_vendor.o(($event) => switchTab("system")),
        c: activeTab.value === "market" ? 1 : "",
        d: common_vendor.o(($event) => switchTab("market")),
        e: isLoadingMore.value
      }, isLoadingMore.value ? {
        f: common_vendor.p({
          size: "22"
        })
      } : {}, {
        g: common_vendor.f(common_vendor.unref(currentMessageList), (item, index, i0) => {
          return common_vendor.e(activeTab.value === "market" ? {
            a: getOtherUserAvatar(item),
            b: common_vendor.t(getOtherUserName(item)),
            c: common_vendor.t(item.lastMsg || "暂无消息"),
            d: common_vendor.t(formatTimeForList(item.lastTime || item.createTime)),
            e: item.businessUrl
          } : {
            f: common_vendor.t(item.title),
            g: common_vendor.t(formatDate(item.createTime))
          }, {
            h: index,
            i: common_vendor.o(($event) => showMessageDetail(item), index),
            j: item.isRead ? "4rpx solid #cccccc" : "4rpx solid #FFCD35"
          });
        }),
        h: activeTab.value === "market",
        i: common_vendor.unref(currentMessageList).length === 0 && !isLoading.value
      }, common_vendor.unref(currentMessageList).length === 0 && !isLoading.value ? {} : {}, {
        j: isLoading.value && common_vendor.unref(currentMessageList).length === 0
      }, isLoading.value && common_vendor.unref(currentMessageList).length === 0 ? {
        k: common_vendor.p({
          size: "28"
        })
      } : {}, {
        l: common_vendor.unref(currentMessageList).length > 0 && hasMore.value === false
      }, common_vendor.unref(currentMessageList).length > 0 && hasMore.value === false ? {} : {}, {
        m: scrollHeight.value + "px",
        n: common_vendor.o(loadMore),
        o: common_vendor.t(currentMessage.value.title || "系统通知"),
        p: common_vendor.p({
          name: "close",
          color: "#333",
          size: "22"
        }),
        q: common_vendor.o(closePopup),
        r: currentMessage.value.createTime
      }, currentMessage.value.createTime ? {
        s: common_vendor.t(formatDate(currentMessage.value.createTime, true))
      } : {}, {
        t: currentMessage.value.content || "无内容",
        v: common_vendor.o(closePopup),
        w: common_vendor.o(closePopup),
        x: common_vendor.p({
          show: showPopup.value,
          mode: "center",
          round: "10",
          closeOnClickOverlay: true
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/message/message.vue"]]);
wx.createPage(MiniProgramPage);
