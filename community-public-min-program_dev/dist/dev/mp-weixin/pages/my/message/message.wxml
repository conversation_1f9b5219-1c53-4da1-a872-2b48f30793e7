<view class="message-container"><view class="tab-bar"><view class="{{['tab-item', a && 'tab-active']}}" bindtap="{{b}}"><text>系统消息</text></view><view class="{{['tab-item', c && 'tab-active']}}" bindtap="{{d}}"><text>跳蚤市场交易消息</text></view></view><view class="message-list" id="messageList"><scroll-view scroll-y="true" style="{{'height:' + m}}" bindscrolltoupper="{{n}}" upper-threshold="50" show-scrollbar="true" class="scroll-view"><view wx:if="{{e}}" class="loading-more"><uv-loading-icon wx:if="{{f}}" u-i="8e795ecc-0" bind:__l="__l" u-p="{{f}}"></uv-loading-icon><text>加载更多...</text></view><view class="list"><view wx:for="{{g}}" wx:for-item="item" wx:key="h" class="item" bindtap="{{item.i}}" style="{{'border-left:' + item.j}}"><view wx:if="{{h}}" class="content market-content"><view class="user-avatar"><image src="{{item.a}}" mode="aspectFill"></image></view><view class="message-content"><view class="user-name">{{item.b}}</view><view class="message-text">{{item.c}}</view><view class="message-time">{{item.d}}</view></view><view class="goods-image"><image src="{{item.e}}" mode="aspectFill"></image></view></view><view wx:else class="content system-content"><view class="message-info"><text class="system-title">{{item.f}}</text></view><view class="time-info"><text class="date">{{item.g}}</text></view></view></view></view><view wx:if="{{i}}" class="empty-list"><image src="/static/empty.png" mode="aspectFit"></image><text>暂无消息</text></view><view wx:if="{{j}}" class="loading-container"><uv-loading-icon wx:if="{{k}}" u-i="8e795ecc-1" bind:__l="__l" u-p="{{k}}"></uv-loading-icon><text>加载中...</text></view><view wx:if="{{l}}" class="no-more"><text>— 已加载全部 —</text></view></scroll-view></view><uv-popup wx:if="{{x}}" u-s="{{['d']}}" bindclose="{{w}}" u-i="8e795ecc-2" bind:__l="__l" u-p="{{x}}"><view class="popup-content"><view class="popup-header"><text class="popup-title">{{o}}</text><view class="popup-close" bindtap="{{q}}"><uv-icon wx:if="{{p}}" u-i="8e795ecc-3,8e795ecc-2" bind:__l="__l" u-p="{{p}}"></uv-icon></view></view><view wx:if="{{r}}" class="popup-time"><text>{{s}}</text></view><view class="popup-body"><rich-text nodes="{{t}}"></rich-text></view><view class="popup-footer"><view class="popup-btn" bindtap="{{v}}">关闭</view></view></view></uv-popup></view>