/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #F6F6F6;
}
.message-container {
  width: 100%;
  height: 100vh;
  background: #F6F6F6;
}
.tab-bar {
  display: flex;
  background: #FFFFFF;
  border-bottom: 1rpx solid #f2f2f2;
}
.tab-bar .tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 28rpx;
  color: #666666;
  transition: color 0.3s;
}
.tab-bar .tab-item.tab-active {
  color: #FFCD35;
  font-weight: 500;
}
.tab-bar .tab-item.tab-active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #FFCD35;
  border-radius: 2rpx;
}
.title-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90rpx;
  background: #FFFFFF;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.05);
}
.title-bar .title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
}
.scroll-view {
  width: 100%;
  background: #F6F6F6;
}
.list {
  padding: 20rpx 18rpx;
}
.list .item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list .item .date {
  font-size: 24rpx;
}
.list .item .circle {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #FFCD35;
  flex-shrink: 0;
  position: absolute;
  top: 15rpx;
  left: 15rpx;
}
.list .item > image {
  width: 64rpx;
  height: 64rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}
.list .item .content {
  width: 100%;
  height: 100%;
}
.list .item .content.market-content {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
}
.list .item .content.market-content .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.list .item .content.market-content .user-avatar image {
  width: 100%;
  height: 100%;
}
.list .item .content.market-content .message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 24rpx;
}
.list .item .content.market-content .message-content .user-name {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 12rpx;
}
.list .item .content.market-content .message-content .message-text {
  color: #666;
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  margin-bottom: 8rpx;
}
.list .item .content.market-content .message-content .message-time {
  color: #999;
  font-size: 24rpx;
  white-space: nowrap;
}
.list .item .content.market-content .goods-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}
.list .item .content.market-content .goods-image image {
  width: 100%;
  height: 100%;
}
.list .item .content.system-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 30rpx;
}
.list .item .content.system-content .message-info {
  flex: 1;
  margin-bottom: 14rpx;
}
.list .item .content.system-content .message-info .system-title {
  color: #333;
  font-size: 28rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}
.list .item .content.system-content .time-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.list .item .content.system-content .time-info .date {
  font-size: 24rpx;
  color: #999;
}
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-list image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-list text {
  color: #999;
  font-size: 28rpx;
}
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}
.loading-container text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}
.loading-more text {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
}
.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 弹窗样式 */
.popup-content {
  width: 600rpx;
  max-height: 800rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.popup-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border-bottom: 1rpx solid #f2f2f2;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  flex: 1;
  padding: 0 60rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.popup-close {
  position: absolute;
  right: 20rpx;
  top: 26rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-time {
  padding: 16rpx 30rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  border-bottom: 1rpx solid #f2f2f2;
}
.popup-body {
  padding: 30rpx;
  overflow-y: auto;
  max-height: 600rpx;
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}
.popup-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f2f2f2;
}
.popup-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: #FFCD35;
  color: #fff;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}