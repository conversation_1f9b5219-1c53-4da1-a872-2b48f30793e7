"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_uv_empty2 = common_vendor.resolveComponent("uv-empty");
  _easycom_uv_empty2();
}
const _easycom_uv_empty = () => "../../../uni_modules/uv-empty/components/uv-empty/uv-empty.js";
if (!Math) {
  _easycom_uv_empty();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "order",
  setup(__props) {
    const activeTab = common_vendor.ref(0);
    const orderList = common_vendor.ref([]);
    common_vendor.onLoad(() => {
      loadOrderData();
    });
    const switchTab = (index) => {
      activeTab.value = index;
      loadOrderData();
    };
    const loadOrderData = () => {
      orderList.value = [];
    };
    return (_ctx, _cache) => {
      return {
        a: activeTab.value === 0 ? 1 : "",
        b: common_vendor.o(($event) => switchTab(0)),
        c: activeTab.value === 1 ? 1 : "",
        d: common_vendor.o(($event) => switchTab(1)),
        e: activeTab.value === 2 ? 1 : "",
        f: common_vendor.o(($event) => switchTab(2)),
        g: activeTab.value === 3 ? 1 : "",
        h: common_vendor.o(($event) => switchTab(3)),
        i: common_vendor.p({
          mode: "order",
          text: "暂无订单",
          show: orderList.value.length === 0,
          marginTop: "100"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a2a92703"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/order/order.vue"]]);
wx.createPage(MiniProgramPage);
