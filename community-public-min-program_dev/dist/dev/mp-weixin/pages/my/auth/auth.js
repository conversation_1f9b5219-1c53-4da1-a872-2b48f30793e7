"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_user = require("../../../common/api/user.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_uv_picker2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_uv_button + _easycom_uv_form + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "auth",
  setup(__props) {
    stores_store.useUserStore();
    stores_store.useAreaStore();
    common_vendor.ref(false);
    common_vendor.ref(60);
    const timer = common_vendor.ref();
    common_vendor.ref(false);
    const rules = Object.freeze({
      realName: {
        type: "string",
        required: true,
        message: "请填写真实姓名",
        trigger: ["blur", "change"]
      },
      detailedAddress: {
        type: "string",
        required: false,
        message: "请填写详细地址",
        trigger: ["blur", "change"]
      },
      districtCode: {
        type: "string",
        required: true,
        message: "请选择您的社区/村",
        trigger: ["blur", "change"]
      },
      villageId: {
        type: "string",
        required: false,
        message: "请选择您的小区",
        trigger: ["blur", "change"]
      }
    });
    const form = common_vendor.reactive({
      realName: "",
      detailedAddress: "",
      districtCode: "",
      villageId: ""
    }), formRef = common_vendor.ref();
    const areaName = common_vendor.ref("");
    const villageList = common_vendor.ref([]), villageName = common_vendor.ref(), pickerRef = common_vendor.ref();
    common_vendor.onLoad(() => {
      common_vendor.index.$on("selectionRegion", async (data) => {
        form.districtCode = data.code;
        areaName.value = data.name;
        form.villageId = "";
        villageName.value = "";
        villageList.value[0] = await getVillageList(data.code);
      });
    });
    const getVillageList = async (code) => {
      const res = await common_api_user.getVillage({ districtCode: code });
      if ((res == null ? void 0 : res.code) === 200) {
        return res.data;
      } else {
        throw console.log(res);
      }
    };
    const confirm = (e) => {
      villageName.value = e.value[0].name;
      form.villageId = e.value[0].villageId;
    };
    const openPicker = () => {
      if (!form.districtCode) {
        common_vendor.index.showToast({
          title: "请选择您的社区或村"
        });
        return;
      }
      pickerRef.value.open();
    };
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    function submit() {
      formRef.value.validate().then(async (result) => {
        try {
          const res = await common_api_user.authentication(form);
          if ((res == null ? void 0 : res.code) === 200) {
            common_vendor.index.showToast({
              title: "认证成功"
            });
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/index/index"
              });
            }, 600);
          } else {
            common_vendor.index.showToast({
              title: (res == null ? void 0 : res.msg) || "认证失败",
              icon: "none"
            });
          }
        } catch (error) {
          console.error("认证失败:", error);
          common_vendor.index.showToast({
            title: "认证失败，请重试",
            icon: "none"
          });
        }
      }).catch((err) => {
        console.log(err, "err");
      });
    }
    common_vendor.onUnload(() => {
      if (timer.value) {
        clearTimeout(timer.value);
      }
      common_vendor.index.$off("selectionRegion");
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => form.realName = $event),
        b: common_vendor.p({
          placeholder: "请填写您的姓名",
          modelValue: form.realName
        }),
        c: common_vendor.p({
          label: "真实姓名",
          prop: "realName",
          required: true
        }),
        d: common_vendor.o(($event) => toPage("/pages/index/area/area?scene=auth")),
        e: common_vendor.o(($event) => areaName.value = $event),
        f: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择您的社区/村",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: areaName.value
        }),
        g: common_vendor.p({
          label: "绑定社区/村",
          prop: "districtCode",
          required: true
        }),
        h: common_vendor.o(openPicker),
        i: common_vendor.o(($event) => villageName.value = $event),
        j: common_vendor.p({
          disabled: true,
          disabledColor: "#ffffff",
          placeholder: "请选择您的小区",
          suffixIcon: "arrow-right",
          suffixIconStyle: "color: #909399",
          modelValue: villageName.value
        }),
        k: common_vendor.p({
          label: "绑定小区",
          prop: "villageId"
        }),
        l: common_vendor.o(($event) => form.detailedAddress = $event),
        m: common_vendor.p({
          placeholder: "请输入您的详细地址",
          modelValue: form.detailedAddress
        }),
        n: common_vendor.p({
          label: "详细地址",
          prop: "detailedAddress"
        }),
        o: common_vendor.o(submit),
        p: common_vendor.p({
          type: "warning",
          text: "提交",
          customStyle: "margin-top: 10px"
        }),
        q: common_vendor.sr(formRef, "43c82faa-0", {
          "k": "formRef"
        }),
        r: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        }),
        s: common_vendor.sr(pickerRef, "43c82faa-10", {
          "k": "pickerRef"
        }),
        t: common_vendor.o(confirm),
        v: common_vendor.p({
          columns: villageList.value,
          keyName: "name"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-43c82faa"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/auth/auth.vue"]]);
wx.createPage(MiniProgramPage);
