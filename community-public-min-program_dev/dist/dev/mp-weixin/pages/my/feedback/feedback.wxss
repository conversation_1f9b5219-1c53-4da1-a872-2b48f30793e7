
page {
		background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.title.data-v-ba9f533d {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0;
}
.content.data-v-ba9f533d {
  margin: 18rpx;
}
.copyright.data-v-ba9f533d {
  position: fixed;
  width: 100%;
  bottom: 40rpx;
  text-align: center;
  font-size: 24rpx;
  color: #9f9f9f;
  display: flex;
  flex-direction: column;
  line-height: 36rpx;
}
.agreement.data-v-ba9f533d {
  color: #55aaff;
}
.menu.data-v-ba9f533d {
  background: #fff;
  border-radius: 10rpx;
  margin: 18rpx;
}
.menu .menu-item.data-v-ba9f533d {
  display: flex;
  justify-content: space-between;
  margin: 0 22rpx;
  padding: 34rpx 0;
  border-bottom: 2rpx solid #eee;
  color: #333;
}
.menu .menu-item.data-v-ba9f533d:last-of-type {
  border-bottom: none;
}
.menu .menu-item > view.data-v-ba9f533d {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.menu .menu-item > view > text.data-v-ba9f533d {
  margin-left: 20rpx;
}
.menu .menu-item > view image.data-v-ba9f533d {
  width: 46rpx;
  height: 46rpx;
}