"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_user = require("../../../common/api/user.js");
const common_enum = require("../../../common/enum.js");
require("../../../common/config.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_textarea2 = common_vendor.resolveComponent("uv-textarea");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  (_easycom_uv_input2 + _easycom_uv_textarea2 + _easycom_uv_button2)();
}
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_textarea = () => "../../../uni_modules/uv-textarea/components/uv-textarea/uv-textarea.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_textarea + _easycom_uv_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "feedback",
  setup(__props) {
    const form = common_vendor.reactive({
      title: "",
      content: ""
    });
    function submit() {
      common_api_user.addFeedback({
        feedbackTitle: form.title,
        feedbackContent: form.content,
        feedbackPlatform: common_enum.PLATFORM_TYPE
      }).then((res) => {
        common_vendor.index.showToast({
          title: "提交成功"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack({
            delta: 1
          });
        }, 600);
      });
    }
    function toPage(index) {
      common_vendor.index.navigateTo({
        url: "/pages/my/agreement/agreement?index=" + index
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => form.title = $event),
        b: common_vendor.p({
          placeholder: "请输入标题",
          border: "none",
          customStyle: {
            padding: "20rpx 24rpx",
            background: "#F9F9F9",
            borderRadius: "10rpx"
          },
          modelValue: form.title
        }),
        c: common_vendor.o(($event) => form.content = $event),
        d: common_vendor.p({
          countStyle: {
            background: "#F9F9F9"
          },
          customStyle: {
            padding: "28rpx 24rpx",
            background: "#F9F9F9",
            borderRadius: "10rpx"
          },
          border: "none",
          count: true,
          maxlength: "300",
          placeholder: "请输入内容",
          modelValue: form.content
        }),
        e: common_vendor.o(($event) => submit()),
        f: common_vendor.p({
          ["custom-style"]: {
            borderRadius: "10rpx",
            boxShadow: "0rpx 5rpx 10rpx 0rpx rgba(249, 169, 31, 0.4)"
          },
          color: "#FF9F18",
          text: "提交"
        }),
        g: common_vendor.o(($event) => toPage(common_vendor.unref(common_enum.AgreementType).USER_AGREEMENT)),
        h: common_vendor.o(($event) => toPage(common_vendor.unref(common_enum.AgreementType).PRIVACY_AGREEMENT))
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ba9f533d"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/feedback/feedback.vue"]]);
wx.createPage(MiniProgramPage);
