"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const common_utils_tab = require("../../common/utils/tab.js");
const stores_store = require("../../stores/store.js");
const common_enum = require("../../common/enum.js");
const common_api_user = require("../../common/api/user.js");
require("../../common/request.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_modal2 = common_vendor.resolveComponent("uv-modal");
  const _easycom_uv_avatar2 = common_vendor.resolveComponent("uv-avatar");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  (_easycom_uv_icon2 + _easycom_uv_modal2 + _easycom_uv_avatar2 + _easycom_uv_form_item2 + _easycom_uv_input2 + _easycom_uv_form2 + _easycom_uv_button2 + _easycom_uv_popup2)();
}
const _easycom_uv_icon = () => "../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_modal = () => "../../uni_modules/uv-modal/components/uv-modal/uv-modal.js";
const _easycom_uv_avatar = () => "../../uni_modules/uv-avatar/components/uv-avatar/uv-avatar.js";
const _easycom_uv_form_item = () => "../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_input = () => "../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form = () => "../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_uv_button = () => "../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_popup = () => "../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  (_easycom_uv_icon + _easycom_uv_modal + _easycom_uv_avatar + _easycom_uv_form_item + _easycom_uv_input + _easycom_uv_form + _easycom_uv_button + _easycom_uv_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "my",
  setup(__props) {
    const userStore = stores_store.useUserStore();
    stores_store.useAreaStore();
    const menuList = common_vendor.ref(0);
    const modal = common_vendor.ref();
    const popupAuthRef = common_vendor.ref(), userInfoObj = common_vendor.ref({});
    const rules = Object.freeze({
      avatar: {
        type: "string",
        required: true,
        message: "请上传您的头像",
        trigger: ["blur", "change"]
      },
      phone: {
        type: "string",
        required: true,
        message: "请填写您的电话号码",
        pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
        trigger: ["blur", "change"]
      }
    });
    const form = common_vendor.reactive({
      avatar: "",
      phone: ""
    }), formRef = common_vendor.ref();
    let scrollHeight = common_vendor.ref();
    common_vendor.onShow(() => {
      console.log(userStore.userInfo, "userStore.userInfo");
      if (userStore.userInfo) {
        getMenuRole();
      }
    });
    common_vendor.onLoad(() => {
      getScreenHeight();
    });
    const getScreenHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      scrollHeight.value = systemInfo.windowHeight - 50;
    };
    async function getMenuRole() {
      const res = await common_api_user.getRoleListByUser();
      menuList.value = res.data;
    }
    function getUserInfoByToken() {
      const token = common_vendor.index.getStorageSync("token");
      if (token) {
        common_api_user.getUserInfo().then((res) => {
          console.log(res);
          userStore.setUser(res.data);
          if (res.data) {
            common_vendor.index.setStorageSync("userInfo", res.data);
            console.log("用户信息已保存到storage:", res.data);
          }
        });
        return;
      }
    }
    const getAvatar = async (e) => {
      const res = await uploadFile(e.detail.avatarUrl);
      form.avatar = res.url;
    };
    const getphonenumber = async (e) => {
      const res = await common_api_user.getPhoneNumber({ code: e.detail.code });
      if ((res == null ? void 0 : res.code) === 200) {
        form.phone = res.data;
      } else {
        throw console.log(res);
      }
    };
    const agreeEvent = () => {
      formRef.value.validate().then(async (result) => {
        common_vendor.index.showLoading({
          title: "正在登录中...",
          mask: true
        });
        common_vendor.index.getUserProfile({
          desc: "用于完善会员资料",
          success: async (res) => {
            userStore.setUser(res.userInfo);
            const result2 = await register(userInfoObj.value.openid);
            common_vendor.index.setStorageSync("token", result2.access_token);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "登录成功"
            });
            popupAuthRef.value.close();
            getUserInfoByToken();
          }
        });
      }).catch((err) => {
        console.log(err, "err");
      });
    };
    const refuseEvent = () => {
      popupAuthRef.value.close();
    };
    const wxAuth = async () => {
      common_vendor.index.navigateTo({
        url: "/pages/auth/login/login"
      });
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    async function register(openid) {
      const res = await common_api_user.appletRegister({
        openId: openid,
        nickname: userStore.userInfo.nickName,
        gender: userStore.userInfo.gender,
        // language: "zh_CN"
        city: userStore.userInfo.city,
        province: userStore.userInfo.province,
        // country: "China"
        avatar: form.avatar,
        phone: form.phone
      });
      return res.data;
    }
    function logout() {
      modal.value.open();
    }
    function confirm() {
      common_vendor.index.removeStorageSync("token");
      userStore.setUser("");
      modal.value.close();
      menuList.value = [];
    }
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    const handleCheckType = (id) => {
      switch (id) {
        case "1902982630547169282":
          return 1;
        case "1902982889700630529":
          return 2;
        case "1904798349735800834":
          return 3;
        case "1904798747620061185":
          return 4;
        default:
          return null;
      }
    };
    function toMenuPage(item) {
      let url = item.menuPath;
      switch (item.jumpType) {
        case common_enum.PageType.MINI_PROGRAM:
          const type = handleCheckType(item.menuId);
          toPage(url + `?type=${type}`);
          break;
        case common_enum.PageType.H5:
          toPage(`/pages/index/h5/h5?url=${encodeURIComponent(url)}`);
          break;
        case common_enum.PageType.OTHER_MINI_PROGRAM:
          common_vendor.wx$1.navigateToMiniProgram({
            shortLink: url
          });
          break;
      }
    }
    function toMiniProgram() {
      common_vendor.index.navigateToMiniProgram({
        appId: "wx0a56652d39658814",
        path: "pages/index/index",
        envVersion: "trial",
        success: function(res) {
          console.log("跳转小程序成功", res);
        },
        fail: function(err) {
          console.log("跳转小程序失败", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    }
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e;
      return common_vendor.e({
        a: common_vendor.unref(userStore).userInfo
      }, common_vendor.unref(userStore).userInfo ? {
        b: common_vendor.unref(userStore).userInfo.avatar || "../../static/my/avatar.png"
      } : {
        c: common_vendor.o(($event) => wxAuth())
      }, {
        d: common_vendor.unref(userStore).userInfo
      }, common_vendor.unref(userStore).userInfo ? {
        e: common_vendor.t((_a = common_vendor.unref(userStore).userInfo) == null ? void 0 : _a.nickname)
      } : {
        f: common_vendor.o(($event) => wxAuth())
      }, {
        g: common_vendor.unref(userStore).userInfo
      }, common_vendor.unref(userStore).userInfo ? {
        h: common_vendor.o(($event) => toPage("info/info"))
      } : {}, {
        i: common_vendor.unref(userStore).userInfo
      }, common_vendor.unref(userStore).userInfo ? {
        j: common_vendor.t(common_vendor.unref(userStore).userInfo.phone)
      } : {}, {
        k: common_vendor.t((_b = common_vendor.unref(userStore).userInfo) == null ? void 0 : _b.districtName),
        l: common_vendor.t(((_c = common_vendor.unref(userStore).userInfo) == null ? void 0 : _c.myPoints) || 0),
        m: common_vendor.t(((_d = common_vendor.unref(userStore).userInfo) == null ? void 0 : _d.accumulatePoints) || 0),
        n: common_vendor.t(((_e = common_vendor.unref(userStore).userInfo) == null ? void 0 : _e.redeemPoints) || 0),
        o: common_vendor.f(menuList.value, (item, i, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.roleName),
            b: !(item == null ? void 0 : item.menus)
          }, !(item == null ? void 0 : item.menus) ? {} : {
            c: common_vendor.f(item.menus, (info, index, i1) => {
              return {
                a: info.menuIcon,
                b: common_vendor.t(info.menuName),
                c: index,
                d: common_vendor.o(($event) => toMenuPage(info), index)
              };
            })
          }, {
            d: i,
            e: common_vendor.n(i === 0 ? "tip-first" : "")
          });
        }),
        p: common_vendor.p({
          name: "chat-fill",
          color: "#FF9F18",
          size: "26"
        }),
        q: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        r: common_vendor.o(toMiniProgram),
        s: common_vendor.p({
          name: "email-fill",
          color: "#FF9F18",
          size: "26"
        }),
        t: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        v: common_vendor.o(($event) => toPage("feedback/feedback")),
        w: common_vendor.p({
          name: "chat",
          color: "#FF9F18",
          size: "26"
        }),
        x: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        y: common_vendor.o(($event) => toPage("message/message")),
        z: common_vendor.unref(userStore).userInfo
      }, common_vendor.unref(userStore).userInfo ? {
        A: common_vendor.p({
          name: "setting",
          color: "#FF9F18",
          size: "22"
        }),
        B: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        C: common_vendor.o(($event) => logout())
      } : {}, {
        D: common_vendor.unref(scrollHeight) + "px",
        E: common_vendor.sr(modal, "d3687551-8", {
          "k": "modal"
        }),
        F: common_vendor.o(confirm),
        G: common_vendor.p({
          title: "提示",
          content: "确定要退出登录吗？",
          confirmText: "确定",
          showCancelButton: true,
          asyncClose: true
        }),
        H: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).HOME,
        I: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME,
        J: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).HOME ? "active" : ""),
        K: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).HOME)),
        L: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ASS,
        M: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS,
        N: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ASS ? "active" : ""),
        O: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ASS)),
        P: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        Q: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND,
        R: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).FOUND ? "active" : ""),
        S: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).FOUND)),
        T: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        U: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY,
        V: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY ? "active" : ""),
        W: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).ACTIVITY)),
        X: common_vendor.unref(common_utils_tab.currentTabIndex) != common_vendor.unref(common_enum.TabBarIndexType).MY,
        Y: common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY,
        Z: common_vendor.n(common_vendor.unref(common_utils_tab.currentTabIndex) == common_vendor.unref(common_enum.TabBarIndexType).MY ? "active" : ""),
        aa: common_vendor.o(($event) => common_vendor.unref(common_utils_tab.change)(common_vendor.unref(common_enum.TabBarIndexType).MY)),
        ab: common_vendor.p({
          src: form.avatar,
          size: "80"
        }),
        ac: common_vendor.o(getAvatar),
        ad: common_vendor.p({
          prop: "avatar"
        }),
        ae: common_vendor.o(($event) => form.phone = $event),
        af: common_vendor.p({
          border: "surround",
          placeholder: "请授权您的电话",
          modelValue: form.phone
        }),
        ag: common_vendor.o(getphonenumber),
        ah: common_vendor.p({
          prop: "phone"
        }),
        ai: common_vendor.sr(formRef, "d3687551-10,d3687551-9", {
          "k": "formRef"
        }),
        aj: common_vendor.p({
          labelPosition: "left",
          model: form,
          rules: common_vendor.unref(rules)
        }),
        ak: common_vendor.o(agreeEvent),
        al: common_vendor.p({
          type: "warning",
          text: "允许",
          customStyle: "margin-top: 10px;margin-bottom: 10px;"
        }),
        am: common_vendor.o(refuseEvent),
        an: common_vendor.p({
          type: "default",
          text: "拒绝",
          customStyle: "margin-top: 10px;margin-bottom: 10px;"
        }),
        ao: common_vendor.sr(popupAuthRef, "d3687551-9", {
          "k": "popupAuthRef"
        }),
        ap: common_vendor.p({
          mode: "bottom",
          round: "10"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d3687551"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/my.vue"]]);
wx.createPage(MiniProgramPage);
