<view><view class="avatar" bindtap="{{d}}"><uv-image wx:if="{{a}}" u-i="c5f7036c-0" bind:__l="__l" u-p="{{b}}"></uv-image><uv-image wx:else u-i="c5f7036c-1" bind:__l="__l" u-p="{{c||''}}"></uv-image></view><view class="menu"><view class="item"><view class="label"><text>昵称</text></view><uv-input wx:if="{{f}}" u-i="c5f7036c-2" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></uv-input></view><view class="item"><view class="label"><text>手机号</text></view><view class="val"><text class="label-val">{{g}}</text><text class="tip-text">不可修改</text></view></view><view bindtap="{{k}}" class="{{['item', l && 'disabled']}}"><view class="label"><text>社区</text></view><view class="val"><text class="label-val">{{h}}</text><text wx:if="{{i}}" class="tip-text">用户身份(业主，商家...)-不可修改</text><uv-icon wx:else u-i="c5f7036c-3" bind:__l="__l" u-p="{{j||''}}"></uv-icon></view></view><view class="item"><view class="label"><text>详细地址</text></view><uv-input wx:if="{{n}}" u-i="c5f7036c-4" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uv-input></view><view class="item"><view class="label"><text>用户身份</text></view><view class="val"><text class="label-val">{{o}}</text><text class="tip-text">不可修改</text></view></view><view class="item" bindtap="{{s}}"><view class="label"><text>性别</text></view><view class="val"><text wx:if="{{p}}" class="label-val">未知</text><text wx:elif="{{q}}" class="label-val">男</text><text wx:else class="label-val">女</text><uv-icon wx:if="{{r}}" u-i="c5f7036c-5" bind:__l="__l" u-p="{{r}}"></uv-icon></view></view></view><view class="btn-box"><uv-button wx:if="{{v}}" bindclick="{{t}}" u-i="c5f7036c-6" bind:__l="__l" u-p="{{v}}"></uv-button></view><uv-picker wx:if="{{y}}" class="r" u-r="picker" bindconfirm="{{x}}" u-i="c5f7036c-7" bind:__l="__l" u-p="{{y}}"></uv-picker></view>