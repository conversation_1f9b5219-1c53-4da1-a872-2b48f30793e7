/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #F6F6F6;
}
.menu {
  background: #fff;
  border-radius: 10rpx;
  margin: 18rpx;
}
.menu .item {
  display: flex;
  justify-content: space-between;
  margin: 0 30rpx;
  padding: 0 20rpx;
  border-bottom: 2rpx solid #eee;
  height: 98rpx;
  color: #333;
}
.menu .item.disabled {
  cursor: not-allowed;
}
.menu .item:last-of-type {
  border-bottom: none;
}
.menu .item .label,
.menu .item .val {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.menu .item .label {
  width: 130rpx;
  flex-shrink: 0;
}
.menu .item .val {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 8rpx;
}
.menu .item .val .label-val {
  margin-right: 20rpx;
}
.menu .item .val .tip-text {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}
.btn-box {
  margin: 60rpx 18rpx 0 18rpx;
}
.avatar {
  margin: 60rpx 0 50rpx 0;
  display: flex;
  justify-content: center;
}