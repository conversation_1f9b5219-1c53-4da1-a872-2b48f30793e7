"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_api_user = require("../../../common/api/user.js");
const common_config = require("../../../common/config.js");
const stores_store = require("../../../stores/store.js");
require("../../../common/request.js");
if (!Array) {
  const _easycom_uv_image2 = common_vendor.resolveComponent("uv-image");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_picker2 = common_vendor.resolveComponent("uv-picker");
  (_easycom_uv_image2 + _easycom_uv_input2 + _easycom_uv_icon2 + _easycom_uv_button2 + _easycom_uv_picker2)();
}
const _easycom_uv_image = () => "../../../uni_modules/uv-image/components/uv-image/uv-image.js";
const _easycom_uv_input = () => "../../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_icon = () => "../../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
const _easycom_uv_button = () => "../../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_picker = () => "../../../uni_modules/uv-picker/components/uv-picker/uv-picker.js";
if (!Math) {
  (_easycom_uv_image + _easycom_uv_input + _easycom_uv_icon + _easycom_uv_button + _easycom_uv_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "info",
  setup(__props) {
    var _a, _b, _c, _d, _e, _f, _g;
    const user = stores_store.useUserStore();
    const areaStore = stores_store.useAreaStore();
    const areaName = common_vendor.ref(areaStore.areaName);
    const form = common_vendor.reactive({
      nickname: ((_a = user.userInfo) == null ? void 0 : _a.nickname) || "",
      avatar: ((_b = user.userInfo) == null ? void 0 : _b.avatar) || "",
      phone: ((_c = user.userInfo) == null ? void 0 : _c.phone) || "",
      identitys: ((_d = user.userInfo) == null ? void 0 : _d.identitys) || "",
      gender: ((_e = user.userInfo) == null ? void 0 : _e.gender) || "0",
      detailedAddress: ((_f = user.userInfo) == null ? void 0 : _f.detailedAddress) || "",
      districtCode: ((_g = user.userInfo) == null ? void 0 : _g.districtCode) || areaStore.areaCode || ""
    });
    common_vendor.computed(() => {
      if (!user.userInfo.identityList || user.userInfo.identityList.length === 0) {
        return "未设置";
      }
      return user.userInfo.identityList.join("，");
    });
    function formatIdentitys(identitys) {
      if (!identitys)
        return "";
      try {
        const identityArray = JSON.parse(identitys);
        if (Array.isArray(identityArray)) {
          return identityArray.join("，");
        }
        return identitys;
      } catch (e) {
        return identitys;
      }
    }
    const canModifyCommunity = common_vendor.computed(() => {
      if (!user.userInfo.identityList || user.userInfo.identityList.length === 0) {
        return true;
      }
      for (const identity of user.userInfo.identityList) {
        if (identity.includes("业主") || identity.includes("商家")) {
          return false;
        }
      }
      return true;
    });
    const picker = common_vendor.ref();
    const columns = common_vendor.ref([
      [{
        label: "未知",
        val: "0"
      }, {
        label: "男",
        val: "1"
      }, {
        label: "女",
        val: "2"
      }]
    ]);
    const upload = common_vendor.ref(false);
    function saveInfo() {
      if (!form.nickname) {
        common_vendor.index.showToast({
          icon: "none",
          title: "昵称不能为空"
        });
        return;
      }
      if (upload.value) {
        common_vendor.index.showLoading({
          title: "正在上传…",
          mask: true
        });
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath: form.avatar,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            form.avatar = JSON.parse(uploadFileRes.data).data.url;
            updateInfo();
          },
          complete: function(e) {
            common_vendor.index.hideLoading();
          }
        });
      } else {
        updateInfo();
      }
    }
    function updateInfo() {
      common_api_user.updateUserInfo({
        nickname: form.nickname,
        avatar: form.avatar,
        gender: form.gender,
        detailedAddress: form.detailedAddress,
        districtCode: form.districtCode
      }).then(async (res) => {
        const result = await common_api_user.getUserInfo();
        user.setUser(result.data);
        common_vendor.index.showToast({
          title: "保存成功"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack({
            delta: 1
          });
        }, 600);
      });
    }
    function confirm(e) {
      form.gender = e.value[0].val;
    }
    function choose() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        success: (chooseImageRes) => {
          upload.value = true;
          form.avatar = chooseImageRes.tempFilePaths[0];
        }
      });
    }
    function toPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
    common_vendor.onLoad(() => {
      const initialDistrictCode = form.districtCode || "";
      const initialAreaName = areaName.value || "";
      console.log("初始form.districtCode:", initialDistrictCode);
      console.log("初始areaName:", initialAreaName);
      common_api_user.getUserInfo().then((res) => {
        if (res == null ? void 0 : res.data) {
          if (res.data.districtCode) {
            form.districtCode = res.data.districtCode;
            if (res.data.districtName && res.data.districtName !== areaName.value) {
              areaName.value = res.data.districtName;
            }
          }
          console.log("API获取的用户信息:", res.data);
          console.log("API获取的districtCode:", res.data.districtCode);
        }
      }).catch((err) => {
        console.error("获取用户信息出错:", err);
      });
      common_vendor.index.$on("selectionRegion", (data) => {
        if (data) {
          form.districtCode = data.code;
          areaName.value = data.name;
          console.log("社区选择变更 - code:", data.code, "name:", data.name);
        }
      });
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("selectionRegion");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: form.avatar
      }, form.avatar ? {
        b: common_vendor.p({
          src: form.avatar,
          shape: "circle",
          width: "80px",
          height: "80px"
        })
      } : {
        c: common_vendor.p({
          src: "/static/my/avatar.png",
          shape: "circle",
          width: "80px",
          height: "80px"
        })
      }, {
        d: common_vendor.o(($event) => choose()),
        e: common_vendor.o(($event) => form.nickname = $event),
        f: common_vendor.p({
          placeholder: "请输入昵称",
          border: "none",
          maxlength: "20",
          modelValue: form.nickname
        }),
        g: common_vendor.t(form.phone || "无"),
        h: common_vendor.t(areaName.value || "未选择"),
        i: !common_vendor.unref(canModifyCommunity)
      }, !common_vendor.unref(canModifyCommunity) ? {} : {
        j: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        })
      }, {
        k: common_vendor.o(($event) => common_vendor.unref(canModifyCommunity) ? toPage("/pages/index/area/area?scene=auth") : null),
        l: !common_vendor.unref(canModifyCommunity) ? 1 : "",
        m: common_vendor.o(($event) => form.detailedAddress = $event),
        n: common_vendor.p({
          placeholder: "请输入详细地址",
          border: "none",
          maxlength: "20",
          modelValue: form.detailedAddress
        }),
        o: common_vendor.t(formatIdentitys(form.identitys) || "未设置"),
        p: form.gender === "0"
      }, form.gender === "0" ? {} : form.gender === "1" ? {} : {}, {
        q: form.gender === "1",
        r: common_vendor.p({
          name: "arrow-right",
          color: "#333",
          size: "14"
        }),
        s: common_vendor.o(($event) => picker.value.open()),
        t: common_vendor.o(($event) => saveInfo()),
        v: common_vendor.p({
          ["custom-style"]: {
            borderRadius: "10rpx",
            boxShadow: "0rpx 5rpx 10rpx 0rpx rgba(#FF9F18, 0.4)"
          },
          color: "#FF9F18",
          text: "保存"
        }),
        w: common_vendor.sr(picker, "c5f7036c-7", {
          "k": "picker"
        }),
        x: common_vendor.o(confirm),
        y: common_vendor.p({
          keyName: "label",
          columns: columns.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/my/info/info.vue"]]);
wx.createPage(MiniProgramPage);
