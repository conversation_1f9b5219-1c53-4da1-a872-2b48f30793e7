"use strict";
const common_vendor = require("../../../common/vendor.js");
const stores_store = require("../../../stores/store.js");
const common_config = require("../../../common/config.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "guide",
  setup(__props) {
    const areaStore = stores_store.useAreaStore();
    const historyStore = stores_store.useHistoryStore();
    const url = common_vendor.ref(common_config.MAP_URL);
    console.log(url.value);
    common_vendor.onLoad((options) => {
      const showLine = options.showLine;
      console.log("in map...");
      if (options.toDetails == 1) {
        url.value += `/lineMap?toDetails=${1}&tourLineId=${options.tourLineId}&communityName=${options.communityName}`;
      } else {
        if (areaStore.areaCode) {
          url.value += `?code=${areaStore.areaCode}&name=${areaStore.areaName}`;
        }
        if (showLine) {
          url.value += "&showLine=" + showLine;
        }
        url.value += "&history=" + JSON.stringify(historyStore.historyList);
      }
      console.log(url.value);
    });
    function onMessage(e) {
      console.log("webview消息", e);
    }
    function getMessage(e) {
      console.log("接受webview消息");
      const dataArr = e.detail.data;
      const data = dataArr[dataArr.length - 1];
      console.log(data.action);
      areaStore.setArea(data.code, data.name);
      common_vendor.index.setStorageSync("latitude", data.latitude);
      common_vendor.index.setStorageSync("longitude", data.longitude);
      for (var i = 0; i < dataArr.length; i++) {
        historyStore.pushHistory({
          code: dataArr[i].code,
          name: dataArr[i].name
        });
      }
    }
    return (_ctx, _cache) => {
      return {
        a: url.value,
        b: common_vendor.o(getMessage),
        c: common_vendor.o(onMessage)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/map/guide/guide.vue"]]);
wx.createPage(MiniProgramPage);
