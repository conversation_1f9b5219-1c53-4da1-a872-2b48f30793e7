"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "line",
  setup(__props) {
    const webSrc = common_vendor.ref("https://qlzhsq.qlzhsq.cn:30210/lineMap");
    common_vendor.onLoad((options) => {
      const tourLineId = options.tourLineId;
      const communityName = options.communityName;
      webSrc.value += `?tourLineId=${tourLineId}&communityName=${communityName}`;
    });
    return (_ctx, _cache) => {
      return {
        a: webSrc.value
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/map/line/line.vue"]]);
wx.createPage(MiniProgramPage);
