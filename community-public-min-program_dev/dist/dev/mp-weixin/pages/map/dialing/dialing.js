"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "dialing",
  setup(__props) {
    const back = common_vendor.ref(false);
    common_vendor.onShow(() => {
      if (back.value) {
        common_vendor.index.navigateBack({
          delta: 1
        });
      } else {
        back.value = true;
      }
    });
    common_vendor.onLoad((options) => {
      const phoneNumber = options == null ? void 0 : options.phoneNumber;
      common_vendor.wx$1.makePhoneCall({
        phoneNumber
      });
    });
    return (_ctx, _cache) => {
      return {};
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/map/dialing/dialing.vue"]]);
wx.createPage(MiniProgramPage);
