"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "map",
  setup(__props) {
    common_vendor.onLoad(() => {
      common_vendor.index.reLaunch({
        url: "guide/guide"
      });
    });
    return (_ctx, _cache) => {
      return {};
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/pages/map/map.vue"]]);
wx.createPage(MiniProgramPage);
