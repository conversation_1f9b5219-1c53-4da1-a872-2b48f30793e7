"use strict";
function wxml(el) {
  return `
		<view class="container">
			<text class="time">${el.time.value}</text>
			<view class="box">
				<view class="line"></view>
				<view class="text">
					<text class="date">
						${el.date.value}
					</text>
					<text class="location">
						${el.location.value}
					</text>
				</view>
			</view>
		</view>
	`;
}
function style(el) {
  var _a, _b, _c, _d, _e;
  console.log(el);
  const style2 = {
    container: {
      width: el.width,
      height: el.container.height
    },
    time: {
      width: el.width,
      height: el.time.height,
      fontSize: 84,
      color: "#fff"
      // backgroundColor: '#2BBC4A',
    },
    box: {
      width: el.width,
      height: (_a = el.box) == null ? void 0 : _a.height,
      flexDirection: "row",
      justifyContent: "flex-start",
      alignItems: "center"
    },
    line: {
      width: 8,
      height: (_b = el.box) == null ? void 0 : _b.height,
      backgroundColor: "#2BBC4A",
      borderRadius: [4, 4, 4, 4]
    },
    text: {
      width: el.width,
      height: (_c = el.box) == null ? void 0 : _c.height,
      flexDirection: "column",
      marginLeft: 15,
      justifyContent: "space-around"
    },
    date: {
      width: el.width,
      height: (_d = el.date) == null ? void 0 : _d.height,
      fontSize: 36,
      lineHeight: 54,
      color: "#fff",
      verticalAlign: "middle"
    },
    location: {
      width: el.width,
      height: (_e = el.location) == null ? void 0 : _e.height,
      fontSize: 36,
      lineHeight: 54,
      color: "#fff",
      verticalAlign: "middle"
    }
  };
  return style2;
}
exports.style = style;
exports.wxml = wxml;
