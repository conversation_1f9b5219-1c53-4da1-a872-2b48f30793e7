<view class="data-v-1a107a5a"><view bindtap="{{i}}" class="{{['container', 'data-v-1a107a5a', j]}}" style="{{'width:' + k + ';' + ('bottom:' + l)}}"><text class="time data-v-1a107a5a" style="{{b}}">{{a}}</text><view class="box data-v-1a107a5a"><view class="line data-v-1a107a5a" style="{{c}}"></view><view class="text data-v-1a107a5a"><text class="date data-v-1a107a5a" hidden="{{!f}}">{{d}} {{e}}</text><text class="location data-v-1a107a5a" hidden="{{!h}}">{{g}}</text></view></view></view><wxml-to-canvas wx:if="{{r0}}" u-r="widget" class="widget r data-v-1a107a5a" u-t="m" u-i="1a107a5a-0" bind:__l="__l" u-p="{{n}}"></wxml-to-canvas><cc-popup wx:if="{{s}}" class="data-v-1a107a5a" u-s="{{['d']}}" u-i="1a107a5a-1" bind:__l="__l" u-p="{{s}}"><view class="cc-popup data-v-1a107a5a"><uv-form wx:if="{{r}}" class="data-v-1a107a5a" u-s="{{['d']}}" u-i="1a107a5a-2,1a107a5a-1" bind:__l="__l" u-p="{{r}}"><uv-form-item wx:for="{{o}}" wx:for-item="val" wx:key="h" class="data-v-1a107a5a" u-s="{{['right','d']}}" u-i="{{val.i}}" bind:__l="__l" u-p="{{val.j}}"><uv-input wx:if="{{val.c}}" class="data-v-1a107a5a" u-i="{{val.a}}" bind:__l="__l" bindupdateModelValue="{{val.b}}" u-p="{{val.c}}"></uv-input><view wx:if="{{val.d}}" class="form-item-right data-v-1a107a5a" slot="right"><uv-switch wx:if="{{val.g}}" class="data-v-1a107a5a" u-i="{{val.e}}" bind:__l="__l" bindupdateModelValue="{{val.f}}" u-p="{{val.g}}"></uv-switch></view></uv-form-item><uv-button wx:if="{{q}}" class="data-v-1a107a5a" bindclick="{{p}}" u-i="1a107a5a-6,1a107a5a-2" bind:__l="__l" u-p="{{q}}"></uv-button></uv-form></view></cc-popup></view>