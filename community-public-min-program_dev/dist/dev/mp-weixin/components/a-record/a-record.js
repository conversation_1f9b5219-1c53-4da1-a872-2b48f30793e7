"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "a-record",
  emits: ["emitEvent"],
  setup(__props, { emit }) {
    var plugin = requirePlugin("WechatSI");
    let manager = plugin.getRecordRecognitionManager();
    let animation = common_vendor.ref(false);
    common_vendor.index.getNetworkType({
      success(res) {
        console.log("网络类型", res.networkType);
      },
      fail(err) {
        console.error("获取网络状态失败", err);
      }
    });
    console.log(manager);
    manager.onRecognize = (res) => {
      console.log(res, "同声翻译");
    };
    manager.onStop = async (res) => {
      console.log(res, 37);
      let text = res.result;
      if (res.duration <= 1e3) {
        common_vendor.index.showLoading({
          title: "说话声音太短",
          mask: true
        });
        setTimeout(() => {
          common_vendor.index.hideLoading();
        }, 1e3);
      } else {
        const uploadRes = await uploadFile(res.tempFilePath);
        emit("emitEvent", Object.assign(uploadRes, { duration: res.duration, text }));
      }
    };
    manager.onError = (res) => {
      console.error("error msg", res);
    };
    manager.onStart = (res) => {
      console.log("成功开始录音识别", res);
    };
    const startRecord = () => {
      console.log("开始录音");
      animation.value = true;
      manager.start({
        duration: 6e4,
        lang: "zh_CN"
      });
    };
    const endRecord = () => {
      console.log("录音结束");
      manager.stop();
      animation.value = false;
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.o(startRecord),
        b: common_vendor.o(endRecord),
        c: (_a = _ctx.audioFile) == null ? void 0 : _a.text
      }, ((_b = _ctx.audioFile) == null ? void 0 : _b.text) ? {
        d: common_vendor.t((_c = _ctx.audioFile) == null ? void 0 : _c.text)
      } : {}, {
        e: common_vendor.unref(animation)
      }, common_vendor.unref(animation) ? {} : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-311a9119"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/a-record/a-record.vue"]]);
wx.createComponent(Component);
