/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 动画 */
.prompt.data-v-311a9119 {
  width: 100%;
  height: 160rpx;
  position: fixed;
  bottom: 50vh;
  right: 1vw;
}
.prompt text.data-v-311a9119 {
  position: absolute;
  bottom: 2px;
  color: white;
  left: 45%;
  animation: puls-311a9119 1.5s infinite ease-in-out;
}
.dots-container.data-v-311a9119 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 45%;
  position: absolute;
  bottom: 0px;
  left: 27.5%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 40rpx;
}
.dot.data-v-311a9119 {
  height: 28rpx;
  width: 28rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  background-image: linear-gradient(#ffbf2a, #fcd614);
  animation: pulse-311a9119 1.5s infinite ease-in-out;
}
.dot.data-v-311a9119:last-child {
  margin-right: 0;
}
.dot.data-v-311a9119:nth-child(1) {
  animation-delay: -0.3s;
}
.dot.data-v-311a9119:nth-child(2) {
  animation-delay: -0.1s;
}
.dot.data-v-311a9119:nth-child(3) {
  animation-delay: 0.1s;
}
@keyframes pulse-311a9119 {
0% {
    transform: scale(0.8);
    background-color: #ffb508;
    /* 更改为与.dot背景色相近的颜色 */
    box-shadow: 0 0 0 0 rgba(255, 218, 3, 0.7);
    /* 使用相同的颜色 */
}
50% {
    transform: scale(1.2);
    background-color: #ffb508;
    /* 稍浅的颜色，增加对比度 */
    box-shadow: 0 0 0 10px rgba(255, 218, 3, 0.7);
    /* 使用.dot的结束颜色，但透明度为0 */
}
100% {
    transform: scale(0.8);
    background-color: #ffb508;
    /* 与0%时的颜色相同 */
    box-shadow: 0 0 0 0 rgba(255, 218, 3, 0.7);
    /* 与0%时的box-shadow相同 */
}
}
@keyframes puls-311a9119 {
0% {
    transform: translateY(0px);
}
50% {
    transform: translateY(-4px);
}
100% {
    transform: translateY(0px);
}
}