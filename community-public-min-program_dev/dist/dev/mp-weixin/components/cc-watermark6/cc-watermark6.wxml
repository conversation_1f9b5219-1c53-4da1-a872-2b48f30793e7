<view class="data-v-9952b1c6"><view bindtap="{{b}}" class="{{['container', 'data-v-9952b1c6', c]}}" style="{{'width:' + d + ';' + ('bottom:' + e)}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="d" class="row data-v-9952b1c6" hidden="{{!item.c}}"><text class="data-v-9952b1c6">{{item.a}}: {{item.b}}</text></view></view><wxml-to-canvas wx:if="{{r0}}" u-r="widget" class="widget r data-v-9952b1c6" u-t="m" u-i="9952b1c6-0" bind:__l="__l" u-p="{{g}}"></wxml-to-canvas><cc-popup wx:if="{{l}}" class="data-v-9952b1c6" u-s="{{['d']}}" u-i="9952b1c6-1" bind:__l="__l" u-p="{{l}}"><view class="cc-popup data-v-9952b1c6"><uv-form wx:if="{{k}}" class="data-v-9952b1c6" u-s="{{['d']}}" u-i="9952b1c6-2,9952b1c6-1" bind:__l="__l" u-p="{{k}}"><uv-form-item wx:for="{{h}}" wx:for-item="item" wx:key="g" class="data-v-9952b1c6" u-s="{{['right','d']}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"><uv-input wx:if="{{item.c}}" class="data-v-9952b1c6" u-i="{{item.a}}" bind:__l="__l" bindupdateModelValue="{{item.b}}" u-p="{{item.c}}"></uv-input><view class="form-item-right data-v-9952b1c6" slot="right"><uv-switch wx:if="{{item.f}}" class="data-v-9952b1c6" u-i="{{item.d}}" bind:__l="__l" bindupdateModelValue="{{item.e}}" u-p="{{item.f}}"></uv-switch></view></uv-form-item><uv-button wx:if="{{j}}" class="data-v-9952b1c6" bindclick="{{i}}" u-i="9952b1c6-6,9952b1c6-2" bind:__l="__l" u-p="{{j}}"></uv-button></uv-form></view></cc-popup></view>