"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_uv_icon2 = common_vendor.resolveComponent("uv-icon");
  _easycom_uv_icon2();
}
const _easycom_uv_icon = () => "../../uni_modules/uv-icon/components/uv-icon/uv-icon.js";
if (!Math) {
  _easycom_uv_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "a-audio",
  props: {
    voiceUrl: {
      //音频数据
      type: String
    },
    duration: {
      //时长
      type: Number
    }
  },
  setup(__props) {
    const props = __props;
    const data = common_vendor.reactive({
      succes: false,
      //播放按钮
      currentTime: 0,
      //当前进度
      duration: 1e-5
      // 总进度
    });
    const innerAudioContext = common_vendor.index.createInnerAudioContext();
    common_vendor.onReady(() => {
      innerAudioContext.src = props.voiceUrl;
      innerAudioContext.onTimeUpdate(() => {
        console.log(innerAudioContext.currentTime, innerAudioContext.duration);
        data.currentTime = innerAudioContext.currentTime;
        if (data.duration === 1e-5 && innerAudioContext.duration > 0) {
          data.duration = innerAudioContext.duration;
        }
      });
      innerAudioContext.onCanplay(() => {
        if (innerAudioContext.duration > 0) {
          data.duration = innerAudioContext.duration;
        }
      });
      innerAudioContext.onEnded(() => {
        data.currentTime = 0;
        data.succes = false;
      });
      innerAudioContext.onError((res) => {
        console.error(res.errMsg);
      });
    });
    common_vendor.onUnload(() => {
      innerAudioContext.destroy();
    });
    common_vendor.onHide(() => {
      innerAudioContext.destroy();
    });
    common_vendor.watch(
      () => props.voiceUrl,
      (newVal, oldVal) => {
        if (newVal != oldVal) {
          data.currentTime = 0;
          data.duration = 1e-5;
          innerAudioContext.src = props.voiceUrl;
        }
      }
    );
    common_vendor.watch(
      () => props.duration,
      (newVal, oldVal) => {
        if (newVal != oldVal && newVal) {
          data.duration = Math.ceil(props.duration / 1e3);
          data.currentTime = 0;
        }
      }
    );
    const timer = common_vendor.computed(() => {
      return calcTimer(data.currentTime);
    });
    const overTimer = common_vendor.computed(() => {
      if (data.duration === 1e-5) {
        return "00:00";
      }
      return calcTimer(data.duration);
    });
    const plays = () => {
      if (!props.voiceUrl) {
        return;
      }
      data.succes = !data.succes;
      if (data.succes) {
        innerAudioContext.play();
      } else {
        innerAudioContext.pause();
      }
    };
    function sliderChange(e) {
      data.currentTime = e.detail.value;
      innerAudioContext.seek(e.detail.value);
      if (!data.succes) {
        data.succes = true;
        innerAudioContext.play();
      }
    }
    function sliderChanging(e) {
      data.currentTime = e.detail.value;
    }
    function calcTimer(timer2) {
      if (timer2 === 0 || typeof timer2 !== "number") {
        return "00:00";
      }
      let mm = Math.floor(timer2 / 60);
      let ss = Math.floor(timer2 % 60);
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (ss < 10) {
        ss = "0" + ss;
      }
      return mm + ":" + ss;
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => plays()),
        b: common_vendor.p({
          name: !data.succes ? "play-circle-fill" : "pause-circle-fill",
          color: "#FF9F18",
          size: "40"
        }),
        c: data.duration,
        d: data.currentTime,
        e: common_vendor.o(sliderChange),
        f: common_vendor.o(sliderChanging),
        g: common_vendor.t(common_vendor.unref(timer)),
        h: common_vendor.t(common_vendor.unref(overTimer))
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5884af55"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/a-audio/a-audio.vue"]]);
wx.createComponent(Component);
