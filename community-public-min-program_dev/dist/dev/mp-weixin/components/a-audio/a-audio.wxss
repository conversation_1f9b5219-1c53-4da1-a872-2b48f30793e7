/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-5884af55 {
  background-color: #F6F6F8;
}

/* 	#video {
	width: 100%;
} */
.audo-video.data-v-5884af55 {
  padding-bottom: 20rpx;
  color: #999;
}
.slider-box.data-v-5884af55 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 27rpx;
  color: #999;
  width: calc(100% - 46px);
}
button.data-v-5884af55 {
  display: inline-block;
  width: 100rpx;
  background-color: #fff;
  font-size: 24rpx;
  color: #000;
  padding: 0;
}
.hidden.data-v-5884af55 {
  position: fixed;
  top: 0;
  left: -10rpx;
  z-index: -1;
  width: 1rpx;
  height: 1rpx;
}
.audo-top.data-v-5884af55 {
  padding: 20rpx 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.audo-top image.data-v-5884af55 {
  width: 45rpx;
  height: 45rpx;
}
.audo-a.data-v-5884af55 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 9;
}
.beishu.data-v-5884af55 {
  position: relative;
  width: 100rpx;
  padding-top: 5rpx;
  padding-bottom: 5rpx;
  text-align: center;
  border-radius: 25rpx;
  font-size: 28rpx;
}
.absolute.data-v-5884af55 {
  position: absolute;
}
.absolute .beishu-a.data-v-5884af55 {
  width: 200rpx;
  border-radius: 20rpx;
  text-align: center;
  line-height: 90rpx;
  background: #fff;
}
.absolute .beishu-a .title.data-v-5884af55 {
  padding-left: 30rpx;
}