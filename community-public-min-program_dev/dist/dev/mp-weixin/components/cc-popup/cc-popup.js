"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "cc-popup",
  props: {
    open: <PERSON>ole<PERSON>
  },
  setup(__props) {
    const props = __props;
    const show = common_vendor.ref(props.open);
    common_vendor.watch(() => props.open, (oldVal, newVal) => {
      show.value = props.open;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.s(show.value ? "transform: translateY(-100%);opacity: 1" : "")
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7a488dd1"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/cc-popup/cc-popup.vue"]]);
wx.createComponent(Component);
