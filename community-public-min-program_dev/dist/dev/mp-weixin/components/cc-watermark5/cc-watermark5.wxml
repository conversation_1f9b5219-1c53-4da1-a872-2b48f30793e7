<view class="data-v-69d8b2dc"><view bindtap="{{c}}" class="{{['container', 'data-v-69d8b2dc', d]}}" style="{{'width:' + e + ';' + ('bottom:' + f)}}"><view class="title data-v-69d8b2dc"><text class="data-v-69d8b2dc">{{a}}</text></view><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="row data-v-69d8b2dc" hidden="{{!item.c}}"><text class="data-v-69d8b2dc">{{item.a}}: {{item.b}}</text></view></view><wxml-to-canvas wx:if="{{r0}}" u-r="widget" class="widget r data-v-69d8b2dc" u-t="m" u-i="69d8b2dc-0" bind:__l="__l" u-p="{{h}}"></wxml-to-canvas><cc-popup wx:if="{{p}}" class="data-v-69d8b2dc" u-s="{{['d']}}" u-i="69d8b2dc-1" bind:__l="__l" u-p="{{p}}"><view class="cc-popup data-v-69d8b2dc"><uv-form wx:if="{{o}}" class="data-v-69d8b2dc" u-s="{{['d']}}" u-i="69d8b2dc-2,69d8b2dc-1" bind:__l="__l" u-p="{{o}}"><uv-form-item wx:if="{{k}}" class="data-v-69d8b2dc" u-s="{{['d']}}" u-i="69d8b2dc-3,69d8b2dc-2" bind:__l="__l" u-p="{{k}}"><uv-input wx:if="{{j}}" class="data-v-69d8b2dc" u-i="69d8b2dc-4,69d8b2dc-3" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"></uv-input></uv-form-item><uv-form-item wx:for="{{l}}" wx:for-item="item" wx:key="g" class="data-v-69d8b2dc" u-s="{{['right','d']}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"><uv-input wx:if="{{item.c}}" class="data-v-69d8b2dc" u-i="{{item.a}}" bind:__l="__l" bindupdateModelValue="{{item.b}}" u-p="{{item.c}}"></uv-input><view class="form-item-right data-v-69d8b2dc" slot="right"><uv-switch wx:if="{{item.f}}" class="data-v-69d8b2dc" u-i="{{item.d}}" bind:__l="__l" bindupdateModelValue="{{item.e}}" u-p="{{item.f}}"></uv-switch></view></uv-form-item><uv-button wx:if="{{n}}" class="data-v-69d8b2dc" bindclick="{{m}}" u-i="69d8b2dc-8,69d8b2dc-2" bind:__l="__l" u-p="{{n}}"></uv-button></uv-form></view></cc-popup></view>