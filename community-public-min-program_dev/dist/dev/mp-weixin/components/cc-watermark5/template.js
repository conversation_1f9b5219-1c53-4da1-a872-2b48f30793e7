"use strict";
function wxml(el) {
  let list = "";
  el.row.forEach((item, index) => {
    if (item.show) {
      list += `<view class="row${index}">
						<text class="rowText${index}">${item.label}: ${item.value}</text>
					  </view>`;
    }
  });
  return `
		<view class="container">
			<view class="title">
				<text class="titleText">${el.title}</text>
			</view>
			${list}
		</view>
	`;
}
function style(el) {
  console.log(el);
  const style2 = {
    container: {
      width: el.width,
      height: el.container.height,
      backgroundColor: "rgba(255, 255, 255, .8)",
      borderRadius: [10, 10, 10, 10],
      flexWrap: "wrap"
    },
    title: {
      width: el.width,
      height: el.title.height,
      color: "#fff",
      backgroundColor: "rgba(43, 188, 74, .8)",
      borderRadius: [10, 10, 0, 0],
      padding: 5
    },
    titleText: {
      width: el.width - 10,
      height: el.title.height,
      fontSize: 38,
      flexWrap: "wrap",
      textAlign: "center",
      fontWeight: "bold",
      borderRadius: [10, 10, 0, 0]
    }
  };
  el.row.forEach((item, index) => {
    style2["row" + index] = {
      width: el.width,
      height: item.height,
      color: "#333",
      paddingLeft: 10,
      flexWrap: "wrap",
      borderRadius: [10, 10, 10, 10]
    };
    style2["rowText" + index] = {
      flexWrap: "wrap",
      width: el.width - 20,
      height: item.height,
      fontSize: 36,
      borderRadius: [10, 10, 10, 10],
      lineHeight: 54
    };
  });
  return style2;
}
exports.style = style;
exports.wxml = wxml;
