"use strict";
function wxml(el) {
  return `
		<view class="container">
			<view class="tabBox">
				<text class="tabText1">${el.title.value}</text>
				<view class="subheadBox">
					<text class="tabText2">${el.subhead.value}</text>
					<view class="tabPlaceholder"></view>
				</view>
			</view>
			<text class="tabLine">› › › › › › › › › › › › › › › › › › › › › › › › › › › › › › › › ›</text>
			<view class="timeBox">
				<text class="timeText">${el.time.value}</text>
				<view class="dateBox">
					<text class="dateRowText">${el.name.value}</text>
					<text class="dateRowText">${el.date.value}</text>
				</view>
			</view>
			<text class="locationText">
				${el.location.value}
			</text>
		</view>
	`;
}
function style(el) {
  const style2 = {
    container: {
      width: el.width,
      height: el.container.height + 20,
      backgroundColor: "#2BBC4A",
      borderRadius: [10, 10, 10, 10],
      padding: 10,
      flexDirection: "column",
      justifyContent: "flex-start"
    },
    tabBox: {
      width: el.tabBox.width,
      height: el.tabBox.height,
      backgroundColor: "#2BBC4A",
      flexDirection: "row",
      justifyContent: "flex-start",
      borderRadius: [10, 10, 10, 10]
    },
    subheadBox: {
      width: el.subheadBox.width,
      height: el.tabBox.height,
      backgroundColor: "#ffffff",
      flexDirection: "column",
      justifyContent: "flex-start",
      borderRadius: [0, 0, 10, 0]
    },
    tabText1: {
      width: el.tabText1.width,
      height: el.tabText1.height,
      color: "#2BBC4A",
      fontSize: 38,
      backgroundColor: "#ffffff",
      padding: 5,
      textAlign: "center",
      verticalAlign: "middle",
      borderRadius: [10, 10, 0, 10],
      fontWeight: "bold"
    },
    tabText2: {
      width: el.tabText2.width,
      height: el.tabText2.height,
      color: "#fff",
      fontSize: 34,
      textAlign: "center",
      verticalAlign: "middle",
      backgroundColor: "#2BBC4A",
      borderRadius: [0, 0, 0, 10]
    },
    tabPlaceholder: {
      width: el.tabPlaceholder.width,
      height: 8,
      backgroundColor: "#fff",
      borderRadius: [0, 5, 5, 0],
      marginLeft: 1
    },
    tabLine: {
      width: el.tabLine.width,
      height: el.tabLine.height + 8,
      color: "#fff",
      fontSize: 24
    },
    timeBox: {
      width: el.timeBox.width,
      height: el.timeBox.height,
      flexDirection: "row",
      justifyContent: "flex-start"
    },
    timeText: {
      width: el.timeText.width,
      height: el.timeText.height,
      fontSize: 84,
      color: "#fff",
      verticalAlign: "middle"
    },
    dateBox: {
      width: el.dateBox.width,
      height: el.timeBox.height,
      flexDirection: "column",
      justifyContent: "center",
      marginLeft: 10
    },
    dateRowText: {
      width: el.timeBox.width - el.timeText.width,
      height: el.dateRowText.height,
      fontSize: 32,
      lineHeight: 50,
      color: "#fff"
    },
    locationText: {
      width: el.locationText.width,
      height: el.locationText.height + 8,
      fontSize: 32,
      lineHeight: 50,
      color: "#fff",
      marginLeft: 10
    }
  };
  return style2;
}
exports.style = style;
exports.wxml = wxml;
