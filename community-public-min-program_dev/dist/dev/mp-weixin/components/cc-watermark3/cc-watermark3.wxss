/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.reduce-container.data-v-5db766a5 {
  left: 15px;
  transform: scale(0.4);
  opacity: 1 !important;
  z-index: 10;
  transition: 0.2s;
}
.default-container.data-v-5db766a5 {
  left: -200%;
}
.widget.data-v-5db766a5 {
  position: absolute;
  left: 0;
  top: -100%;
  opacity: 0;
}
.container.data-v-5db766a5 {
  background: #2BBC4A;
  border-radius: 10px;
  position: absolute;
  padding: 10px;
  transform-origin: bottom left;
  opacity: 0;
}
.container .tabBox.data-v-5db766a5 {
  display: flex;
  background: #2BBC4A;
  border-radius: 10px;
}
.container .tabBox .tabText1.data-v-5db766a5 {
  font-size: 38px;
  color: #2BBC4A;
  background: #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  text-align: center;
  padding: 0 5px;
  min-width: 40%;
  max-width: 70%;
  flex-shrink: 0;
}
.container .tabBox .subheadBox.data-v-5db766a5 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  background: #fff;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
.container .tabBox .subheadBox .tabText2.data-v-5db766a5 {
  font-size: 34px;
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
  padding: 5px;
  background: #2BBC4A;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
}
.container .tabBox .subheadBox .tabPlaceholder.data-v-5db766a5 {
  height: 13px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background: #fff;
  flex-shrink: 0;
}
.container .tabLine.data-v-5db766a5 {
  font-size: 24px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
}
.container .timeBox.data-v-5db766a5 {
  display: flex;
  color: #fff;
  align-items: center;
}
.container .timeBox .timeText.data-v-5db766a5 {
  font-size: 84px;
  padding: 0 10px;
  height: 118px;
}
.container .timeBox .dateBox.data-v-5db766a5 {
  color: #fff;
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
.container .timeBox .dateBox .dateRowText.data-v-5db766a5 {
  font-size: 32px;
  height: 50px;
}
.container .locationText.data-v-5db766a5 {
  font-size: 32px;
  color: #fff;
  margin-left: 10px;
}
.popup-form.data-v-5db766a5 {
  padding: 40rpx;
}