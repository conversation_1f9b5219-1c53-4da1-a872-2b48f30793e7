<view class="data-v-5db766a5"><view bindtap="{{l}}" class="{{['container', 'data-v-5db766a5', m]}}" style="{{'width:' + n + ';' + ('bottom:' + o)}}"><view class="tabBox data-v-5db766a5"><text class="tabText1 data-v-5db766a5">{{a}}</text><view class="subheadBox data-v-5db766a5"><text class="tabText2 data-v-5db766a5">{{b}}</text><view class="tabPlaceholder data-v-5db766a5"></view></view></view><text class="tabLine data-v-5db766a5">› › › › › › › › › › › › › › › › › › › › › › › › › › › › › ›</text><view class="timeBox data-v-5db766a5"><text class="timeText data-v-5db766a5" hidden="{{!d}}">{{c}}</text><view class="dateBox data-v-5db766a5"><text class="dateRowText data-v-5db766a5" hidden="{{!f}}">{{e}}</text><text class="dateRowText data-v-5db766a5" hidden="{{!i}}">{{g}} {{h}}</text></view></view><text class="locationText data-v-5db766a5" hidden="{{!k}}">{{j}}</text></view><wxml-to-canvas wx:if="{{r0}}" u-r="widget" class="widget r data-v-5db766a5" u-t="m" u-i="5db766a5-0" bind:__l="__l" u-p="{{q}}"></wxml-to-canvas><cc-popup wx:if="{{w}}" class="data-v-5db766a5" u-s="{{['d']}}" u-i="5db766a5-1" bind:__l="__l" u-p="{{w}}"><view class="cc-popup data-v-5db766a5"><uv-form wx:if="{{v}}" class="data-v-5db766a5" u-s="{{['d']}}" u-i="5db766a5-2,5db766a5-1" bind:__l="__l" u-p="{{v}}"><uv-form-item wx:for="{{r}}" wx:for-item="val" wx:key="h" class="data-v-5db766a5" u-s="{{['right','d']}}" u-i="{{val.i}}" bind:__l="__l" u-p="{{val.j}}"><uv-input wx:if="{{val.c}}" class="data-v-5db766a5" u-i="{{val.a}}" bind:__l="__l" bindupdateModelValue="{{val.b}}" u-p="{{val.c}}"></uv-input><view wx:if="{{val.d}}" class="form-item-right data-v-5db766a5" slot="right"><uv-switch wx:if="{{val.g}}" class="data-v-5db766a5" u-i="{{val.e}}" bind:__l="__l" bindupdateModelValue="{{val.f}}" u-p="{{val.g}}"></uv-switch></view></uv-form-item><uv-button wx:if="{{t}}" class="data-v-5db766a5" bindclick="{{s}}" u-i="5db766a5-6,5db766a5-2" bind:__l="__l" u-p="{{t}}"></uv-button></uv-form></view></cc-popup></view>