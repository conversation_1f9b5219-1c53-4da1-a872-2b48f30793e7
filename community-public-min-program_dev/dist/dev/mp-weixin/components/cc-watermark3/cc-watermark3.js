"use strict";
const common_vendor = require("../../common/vendor.js");
const common_utils_common = require("../../common/utils/common.js");
const common_watermark = require("../../common/watermark.js");
const stores_store = require("../../stores/store.js");
const components_ccWatermark3_template = require("./template.js");
require("../../common/config.js");
if (!Array) {
  const _component_wxml_to_canvas = common_vendor.resolveComponent("wxml-to-canvas");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_switch2 = common_vendor.resolveComponent("uv-switch");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_cc_popup2 = common_vendor.resolveComponent("cc-popup");
  (_component_wxml_to_canvas + _easycom_uv_input2 + _easycom_uv_switch2 + _easycom_uv_form_item2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_cc_popup2)();
}
const _easycom_uv_input = () => "../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_switch = () => "../../uni_modules/uv-switch/components/uv-switch/uv-switch.js";
const _easycom_uv_form_item = () => "../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_button = () => "../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_cc_popup = () => "../cc-popup/cc-popup.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_switch + _easycom_uv_form_item + _easycom_uv_button + _easycom_uv_form + _easycom_cc_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "cc-watermark3",
  props: {
    reduce: Boolean
  },
  emits: ["submit", "pipeline"],
  setup(__props, { expose, emit }) {
    const locationStore = stores_store.useLocationStore();
    const widget = common_vendor.ref();
    const bottom = common_vendor.ref(common_watermark.MARGIN);
    const template = common_vendor.reactive({
      className: "container",
      classList: [".container", ".tabBox", ".tabText1", ".subheadBox", ".tabText2", ".tabPlaceholder", ".tabLine", ".timeBox", ".timeText", ".dateBox", ".dateRowText", ".locationText"],
      style: {
        width: 540
      },
      wxml: {
        title: {
          label: "标题",
          value: "执勤巡逻",
          show: true,
          switch: false
        },
        subhead: {
          label: "副标题",
          value: "工作记录",
          show: true,
          switch: false
        },
        name: {
          label: "姓名",
          value: "XXXX",
          show: true,
          switch: true
        },
        time: {
          label: "时间",
          value: common_utils_common.getCurrentTime(),
          show: true,
          switch: true
        },
        date: {
          label: "日期",
          value: common_utils_common.getCurrentDate() + " " + common_utils_common.getCurrentWeek(),
          show: true,
          switch: true
        },
        location: {
          label: "地点",
          value: locationStore.location,
          show: true,
          switch: true
        }
      }
    });
    expose({ render });
    const ins = common_vendor.getCurrentInstance();
    const show = common_vendor.ref(false);
    function openEdit() {
      show.value = true;
      if (bottom.value == common_watermark.MARGIN) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery().in(ins);
          query.select(".cc-popup").boundingClientRect((res) => {
            bottom.value += res.height - common_watermark.CAMERA_BOTTOM;
          }).exec();
        });
      }
    }
    function popupClose() {
      show.value = false;
      bottom.value = common_watermark.MARGIN;
      emit("pipeline", template.wxml);
    }
    async function calculatedHeight() {
      await common_vendor.nextTick$1(async () => {
        for (let i = 0; i < template.classList.length; i++) {
          await getElementHeight(template.classList[i]);
        }
        await renderToCanvas();
      });
    }
    function getElementHeight(className) {
      return new Promise((resolve) => {
        const query = common_vendor.index.createSelectorQuery().in(ins);
        query.selectAll(className).boundingClientRect((res) => {
          const len = res.length;
          const key = className.substring(1);
          for (let i = 0; i < len; i++) {
            const height = Math.ceil(res[i].height);
            const width = Math.ceil(res[i].width);
            template.style[key] = {
              width,
              height
            };
          }
          resolve();
        }).exec();
      });
    }
    async function renderToCanvas() {
      const _widget = common_vendor.toRaw(widget.value);
      const _wxml = components_ccWatermark3_template.wxml(template.wxml);
      const _style = components_ccWatermark3_template.style(template.style);
      const res = await _widget.renderToCanvas({
        wxml: _wxml,
        style: _style
      });
      return res;
    }
    async function render(templateWxml) {
      if (templateWxml) {
        template.wxml = templateWxml;
      }
      await calculatedHeight();
      const tempPath = await extraImage();
      emit("submit", {
        width: template.style.width,
        height: template.style[template.className].height,
        tempWatermarkPath: tempPath
      });
    }
    async function extraImage() {
      const _widget = common_vendor.toRaw(widget.value);
      const res = await _widget.canvasToTempFilePath({
        fileType: "png",
        quality: 1
      });
      return res.tempFilePath;
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(template.wxml.title.value),
        b: common_vendor.t(template.wxml.subhead.value),
        c: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentTime)()),
        d: template.wxml.time.show,
        e: common_vendor.t(template.wxml.name.value),
        f: template.wxml.name.show,
        g: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentDate)()),
        h: common_vendor.t(common_vendor.unref(common_utils_common.getCurrentWeek)()),
        i: template.wxml.date.show,
        j: common_vendor.t(common_vendor.unref(locationStore).location),
        k: template.wxml.location.show,
        l: common_vendor.o(($event) => openEdit()),
        m: common_vendor.n(__props.reduce ? "reduce-container" : "default-container"),
        n: template.style.width + "px",
        o: bottom.value + "px",
        p: common_vendor.sr(widget, "5db766a5-0", {
          "k": "widget"
        }),
        q: common_vendor.p({
          width: template.style.width,
          height: 600
        }),
        r: common_vendor.f(template.wxml, (val, key, index) => {
          return common_vendor.e({
            a: "5db766a5-4-" + index + "," + ("5db766a5-3-" + index),
            b: common_vendor.o(($event) => template.wxml[key].value = $event, index),
            c: common_vendor.p({
              border: "none",
              modelValue: template.wxml[key].value
            }),
            d: val.switch
          }, val.switch ? {
            e: "5db766a5-5-" + index + "," + ("5db766a5-3-" + index),
            f: common_vendor.o(($event) => template.wxml[key].show = $event, index),
            g: common_vendor.p({
              size: "20",
              ["active-color"]: "#2BBC4A",
              modelValue: template.wxml[key].show
            })
          } : {}, {
            h: index,
            i: "5db766a5-3-" + index + ",5db766a5-2",
            j: common_vendor.p({
              label: template.wxml[key].label,
              prop: "template.wxml",
              borderBottom: true
            })
          });
        }),
        s: common_vendor.o(($event) => popupClose()),
        t: common_vendor.p({
          ["custom-style"]: {
            marginTop: "40rpx",
            borderRadius: "10rpx",
            boxShadow: "0rpx 5rpx 10rpx 0rpx rgba(43, 188, 74, 0.4)"
          },
          color: "#2BBC4A",
          text: "确定"
        }),
        v: common_vendor.p({
          labelPosition: "left",
          model: template.wxml,
          labelWidth: "80"
        }),
        w: common_vendor.p({
          open: show.value
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5db766a5"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/cc-watermark3/cc-watermark3.vue"]]);
wx.createComponent(Component);
