<view class="data-v-aad091d1"><view bindtap="{{c}}" class="{{['container', 'data-v-aad091d1', d]}}" style="{{'width:' + e + ';' + ('bottom:' + f)}}"><view class="title data-v-aad091d1"><text class="data-v-aad091d1">{{a}}</text></view><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="row data-v-aad091d1" hidden="{{!item.c}}"><text class="data-v-aad091d1">{{item.a}}{{item.b}}</text></view></view><wxml-to-canvas wx:if="{{r0}}" u-r="widget" class="widget r data-v-aad091d1" u-t="m" u-i="aad091d1-0" bind:__l="__l" u-p="{{h}}"></wxml-to-canvas><cc-popup wx:if="{{p}}" class="data-v-aad091d1" u-s="{{['d']}}" u-i="aad091d1-1" bind:__l="__l" u-p="{{p}}"><view class="cc-popup data-v-aad091d1"><uv-form wx:if="{{o}}" class="data-v-aad091d1" u-s="{{['d']}}" u-i="aad091d1-2,aad091d1-1" bind:__l="__l" u-p="{{o}}"><uv-form-item wx:if="{{k}}" class="data-v-aad091d1" u-s="{{['d']}}" u-i="aad091d1-3,aad091d1-2" bind:__l="__l" u-p="{{k}}"><uv-input wx:if="{{j}}" class="data-v-aad091d1" u-i="aad091d1-4,aad091d1-3" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"></uv-input></uv-form-item><uv-form-item wx:for="{{l}}" wx:for-item="item" wx:key="g" class="data-v-aad091d1" u-s="{{['right','d']}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"><uv-input wx:if="{{item.c}}" class="data-v-aad091d1" u-i="{{item.a}}" bind:__l="__l" bindupdateModelValue="{{item.b}}" u-p="{{item.c}}"></uv-input><view class="form-item-right data-v-aad091d1" slot="right"><uv-switch wx:if="{{item.f}}" class="data-v-aad091d1" u-i="{{item.d}}" bind:__l="__l" bindupdateModelValue="{{item.e}}" u-p="{{item.f}}"></uv-switch></view></uv-form-item><uv-button wx:if="{{n}}" class="data-v-aad091d1" bindclick="{{m}}" u-i="aad091d1-8,aad091d1-2" bind:__l="__l" u-p="{{n}}"></uv-button></uv-form></view></cc-popup></view>