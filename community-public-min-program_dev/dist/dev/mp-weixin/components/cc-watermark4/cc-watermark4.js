"use strict";
const common_vendor = require("../../common/vendor.js");
const common_utils_common = require("../../common/utils/common.js");
const common_watermark = require("../../common/watermark.js");
const components_ccWatermark4_template = require("./template.js");
require("../../common/config.js");
if (!Array) {
  const _component_wxml_to_canvas = common_vendor.resolveComponent("wxml-to-canvas");
  const _easycom_uv_input2 = common_vendor.resolveComponent("uv-input");
  const _easycom_uv_form_item2 = common_vendor.resolveComponent("uv-form-item");
  const _easycom_uv_switch2 = common_vendor.resolveComponent("uv-switch");
  const _easycom_uv_button2 = common_vendor.resolveComponent("uv-button");
  const _easycom_uv_form2 = common_vendor.resolveComponent("uv-form");
  const _easycom_cc_popup2 = common_vendor.resolveComponent("cc-popup");
  (_component_wxml_to_canvas + _easycom_uv_input2 + _easycom_uv_form_item2 + _easycom_uv_switch2 + _easycom_uv_button2 + _easycom_uv_form2 + _easycom_cc_popup2)();
}
const _easycom_uv_input = () => "../../uni_modules/uv-input/components/uv-input/uv-input.js";
const _easycom_uv_form_item = () => "../../uni_modules/uv-form/components/uv-form-item/uv-form-item.js";
const _easycom_uv_switch = () => "../../uni_modules/uv-switch/components/uv-switch/uv-switch.js";
const _easycom_uv_button = () => "../../uni_modules/uv-button/components/uv-button/uv-button.js";
const _easycom_uv_form = () => "../../uni_modules/uv-form/components/uv-form/uv-form.js";
const _easycom_cc_popup = () => "../cc-popup/cc-popup.js";
if (!Math) {
  (_easycom_uv_input + _easycom_uv_form_item + _easycom_uv_switch + _easycom_uv_button + _easycom_uv_form + _easycom_cc_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "cc-watermark4",
  props: {
    location: String,
    lnglat: String,
    reduce: Boolean
  },
  emits: ["submit", "pipeline"],
  setup(__props, { expose, emit }) {
    const props = __props;
    common_vendor.watch(() => props.location, (location, prevLocation) => {
      console.log(location);
      console.log(prevLocation);
      const row = template.wxml.row.find((item) => item.label == "地点");
      row.value = location;
    });
    expose({ render });
    common_vendor.ref();
    const widget = common_vendor.ref();
    const bottom = common_vendor.ref(common_watermark.MARGIN);
    const template = common_vendor.reactive({
      className: "container",
      classList: [".container", ".title", ".row"],
      style: {
        width: common_watermark.WATERMARK_WIDTH
      },
      wxml: {
        title: "自定义模板",
        row: [
          {
            label: "姓名: ",
            value: "XXXX",
            show: true
          },
          {
            label: "区域: ",
            value: "XXXX",
            show: true
          },
          {
            label: "内容: ",
            value: "XXXX",
            show: true
          },
          {
            label: "队伍: ",
            value: "XXXX",
            show: true
          },
          {
            label: "时间: ",
            value: common_utils_common.getCurrentTime(),
            show: true
          },
          {
            label: "日期: ",
            value: common_utils_common.getCurrentDate() + " " + common_utils_common.getCurrentWeek(),
            show: true
          },
          {
            label: "地点: ",
            value: props.location,
            show: true
          },
          {
            label: "备注: ",
            value: "一切顺利",
            show: true
          },
          {
            label: "",
            value: props.lnglat,
            show: true
          }
        ]
      }
    });
    const ins = common_vendor.getCurrentInstance();
    const show = common_vendor.ref(false);
    function openEdit() {
      show.value = true;
      if (bottom.value == common_watermark.MARGIN) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery().in(ins);
          query.select(".cc-popup").boundingClientRect((res) => {
            bottom.value += res.height - common_watermark.CAMERA_BOTTOM;
          }).exec();
        });
      }
    }
    function popupClose() {
      show.value = false;
      bottom.value = common_watermark.MARGIN;
      emit("pipeline", template.wxml);
    }
    async function calculatedHeight() {
      await common_vendor.nextTick$1(async () => {
        for (let i = 0; i < template.classList.length; i++) {
          await getElementHeight(template.classList[i]);
        }
        await renderToCanvas();
      });
    }
    function getElementHeight(className) {
      return new Promise((resolve) => {
        const query = common_vendor.index.createSelectorQuery().in(ins);
        query.selectAll(className).boundingClientRect((res) => {
          const len = res.length;
          const key = className.substring(1);
          for (let i = 0; i < len; i++) {
            const height = Math.ceil(res[i].height);
            const width = Math.ceil(res[i].width);
            if (len > 1) {
              if (!template.style.row) {
                template.style.row = [];
              }
              template.style.row.push({
                width,
                height
              });
            } else {
              template.style[key] = {
                width,
                height
              };
            }
          }
          resolve();
        }).exec();
      });
    }
    async function renderToCanvas() {
      const _widget = common_vendor.toRaw(widget.value);
      const _wxml = components_ccWatermark4_template.wxml(template.wxml);
      const _style = components_ccWatermark4_template.style(template.style);
      const res = await _widget.renderToCanvas({
        wxml: _wxml,
        style: _style
      });
      return res;
    }
    async function render(templateWxml) {
      if (templateWxml) {
        template.wxml = templateWxml;
      }
      await calculatedHeight();
      const tempPath = await extraImage();
      emit("submit", {
        width: template.style.width,
        height: template.style[template.className].height,
        tempWatermarkPath: tempPath
      });
    }
    async function extraImage() {
      const _widget = common_vendor.toRaw(widget.value);
      const res = await _widget.canvasToTempFilePath({
        fileType: "png",
        quality: 1
      });
      return res.tempFilePath;
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(template.wxml.title),
        b: common_vendor.f(template.wxml.row, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: item.show,
            d: index
          };
        }),
        c: common_vendor.o(($event) => openEdit()),
        d: common_vendor.n(__props.reduce ? "reduce-container" : "default-container"),
        e: template.style.width + "px",
        f: bottom.value + "px",
        g: common_vendor.sr(widget, "aad091d1-0", {
          "k": "widget"
        }),
        h: common_vendor.p({
          width: template.style.width,
          height: 600
        }),
        i: common_vendor.o(($event) => template.wxml.title = $event),
        j: common_vendor.p({
          border: "none",
          modelValue: template.wxml.title
        }),
        k: common_vendor.p({
          label: "标题",
          prop: "template.wxml.title",
          borderBottom: true
        }),
        l: common_vendor.f(template.wxml.row, (item, index, i0) => {
          return {
            a: "aad091d1-6-" + i0 + "," + ("aad091d1-5-" + i0),
            b: common_vendor.o(($event) => item.value = $event, index),
            c: common_vendor.p({
              border: "none",
              modelValue: item.value
            }),
            d: "aad091d1-7-" + i0 + "," + ("aad091d1-5-" + i0),
            e: common_vendor.o(($event) => item.show = $event, index),
            f: common_vendor.p({
              size: "20",
              ["active-color"]: "#2BBC4A",
              modelValue: item.show
            }),
            g: index,
            h: "aad091d1-5-" + i0 + ",aad091d1-2",
            i: common_vendor.p({
              label: item.label,
              prop: "template.wxml.row",
              borderBottom: true
            })
          };
        }),
        m: common_vendor.o(($event) => popupClose()),
        n: common_vendor.p({
          ["custom-style"]: {
            marginTop: "40rpx",
            borderRadius: "10rpx",
            boxShadow: "0rpx 5rpx 10rpx 0rpx rgba(43, 188, 74, 0.4)"
          },
          color: "#2BBC4A",
          text: "确定"
        }),
        o: common_vendor.p({
          labelPosition: "left",
          model: template.wxml,
          labelWidth: "80"
        }),
        p: common_vendor.p({
          open: show.value
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-aad091d1"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/cc-watermark4/cc-watermark4.vue"]]);
wx.createComponent(Component);
