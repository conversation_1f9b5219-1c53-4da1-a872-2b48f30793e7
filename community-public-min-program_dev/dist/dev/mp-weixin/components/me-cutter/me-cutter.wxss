
	/* 	page {
		background-color: #F7F7F7;
	} */
.tip.data-v-0a0bc55f {
		background-color: #ff9900;
		position: fixed;
		height: 45px;
		line-height: 45px;
		width: 100%;
		text-align: center;
		z-index: 999;
		color: #fff;
}
.ksp-image-cutter.data-v-0a0bc55f {
		background-color: #F1F1F1;
		position: absolute;
		width: 100%;
		height: 100vh;
		top: 0;
		bottom: 0;
		z-index: 1000;
}
.header.data-v-0a0bc55f {
		z-index: 999;
		position: relative;
		background-color: #fff;
		width: 100%;
		height: 90rpx;
		box-sizing: border-box;
		border-bottom: 1px solid #f2f2f2;
}
.toolbar.data-v-0a0bc55f {
		position: absolute;
		bottom: 0;
		height: 150rpx;
		width: 100%;
		left: 0;
		/* box-shadow: 0 0px 8px 0px rgba(0, 0, 0, 0.1); */
		background: #F1F1F1;
}
.color-op.data-v-0a0bc55f {
		display: flex;
		justify-content: space-around;
		flex-direction: row;
}
.btn-cancel.data-v-0a0bc55f {
		position: absolute;
		left: 40rpx;
		font-size: 30rpx;
		line-height: 30rpx;
		color: #000;
		line-height: 90rpx;
}
.btn-ok.data-v-0a0bc55f {
		position: absolute;
		right: 40rpx;
		font-size: 30rpx;
		line-height: 30rpx;
		color: #000;
		line-height: 90rpx;
		color: #2979ff;
}
.body.data-v-0a0bc55f {
		position: absolute;
		left: 0rpx;
		right: 0rpx;
		top: 0rpx;
		bottom: 150rpx;
		/* background: #fff; */
		overflow: hidden;
}
.mask.data-v-0a0bc55f {
		position: absolute;
		left: 0rpx;
		right: 0rpx;
		top: 0rpx;
		bottom: 0rpx;
		background: #f2f2f2;
		opacity: 0.4;
}
.plank.data-v-0a0bc55f {
		position: absolute;
		left: 0rpx;
		right: 0rpx;
		top: 0rpx;
		bottom: 0rpx;
}
.image.data-v-0a0bc55f {
		position: absolute;
		/* z-index: 10; */
}
.frame.data-v-0a0bc55f {
		/* background-color: rgba(0, 0, 0, 0); */
		position: absolute;
		/* z-index: 11; */
}
.canvas.data-v-0a0bc55f {
		position: absolute;
		display: block;
		left: 0px;
		top: 0px;
}
.rect.data-v-0a0bc55f {
		position: absolute;
		/* 	left: -2px;
	top: -2px; */
		width: 100%;
		height: 100%;
		border: 2px solid #409eff;
}
.line-one.data-v-0a0bc55f {
		position: absolute;
		width: 100%;
		height: 1px;
		background: #409eff;
		left: 0;
		top: 33.3%;
}
.line-two.data-v-0a0bc55f {
		position: absolute;
		width: 100%;
		height: 1px;
		background: #409eff;
		left: 0;
		top: 66.7%;
}
.line-three.data-v-0a0bc55f {
		position: absolute;
		width: 1px;
		height: 100%;
		background: #409eff;
		top: 0;
		left: 33.3%;
}
.line-four.data-v-0a0bc55f {
		position: absolute;
		width: 1px;
		height: 100%;
		background: #409eff;
		top: 0;
		left: 66.7%;
}
.frame-left.data-v-0a0bc55f {
		position: absolute;
		height: 100%;
		width: 8px;
		left: -4px;
		top: 0;
}
.frame-right.data-v-0a0bc55f {
		position: absolute;
		height: 100%;
		width: 8px;
		right: -4px;
		top: 0;
}
.frame-top.data-v-0a0bc55f {
		position: absolute;
		width: 100%;
		height: 8px;
		top: -4px;
		left: 0;
}
.frame-bottom.data-v-0a0bc55f {
		position: absolute;
		width: 100%;
		height: 8px;
		bottom: -4px;
		left: 0;
}
.frame-left-top.data-v-0a0bc55f {
		position: absolute;
		width: 20px;
		height: 20px;
		left: -6px;
		top: -6px;
		border-left: 4px solid #67c23a;
		border-top: 4px solid #67c23a;
}
.frame-left-bottom.data-v-0a0bc55f {
		position: absolute;
		width: 20px;
		height: 20px;
		left: -6px;
		bottom: -6px;
		border-left: 4px solid #67c23a;
		border-bottom: 4px solid #67c23a;
}
.frame-right-top.data-v-0a0bc55f {
		position: absolute;
		width: 20px;
		height: 20px;
		right: -6px;
		top: -6px;
		border-right: 4px solid #67c23a;
		border-top: 4px solid #67c23a;
}
.frame-right-bottom.data-v-0a0bc55f {
		position: absolute;
		width: 20px;
		height: 20px;
		right: -6px;
		bottom: -6px;
		border-right: 4px solid #67c23a;
		border-bottom: 4px solid #67c23a;
}
.color-box.data-v-0a0bc55f {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
}
.color-box>view.data-v-0a0bc55f {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 110rpx;
}
.color-box>view text.data-v-0a0bc55f {
		margin-top: 20rpx;
		font-size: 28rpx;
		font-size: #3F3F3F;
}
.color-block.data-v-0a0bc55f {
		box-sizing: content-box;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
}
.active.data-v-0a0bc55f {
		/* border: 8rpx solid #fff; */
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
}
.active.data-v-0a0bc55f::after {
		position: absolute;
		content: '';
		width: 62rpx;
		height: 62rpx;
		border: 6rpx solid #1890FF;
		border-radius: 50%;
}
.color1.data-v-0a0bc55f {
		background: #1890FF;
}
.color2.data-v-0a0bc55f {
		background: #F24747;
}
.color3.data-v-0a0bc55f {
		background: #FFFFFF;
}
.color4.data-v-0a0bc55f {
		background: linear-gradient(to bottom, #000000, #ffffff);
}
.color5.data-v-0a0bc55f {
		background: linear-gradient(to bottom, #1890FF, #ffffff);
}
