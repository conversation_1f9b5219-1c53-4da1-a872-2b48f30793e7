"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    url: {
      type: String,
      default: ""
    },
    fixed: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    },
    maxWidth: {
      type: Number,
      default: 1024
    },
    maxHeight: {
      type: Number,
      default: 1024
    },
    maxSize: {
      type: Number,
      default: 1024
    },
    blob: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      targetshow: false,
      mask: {
        show: false
      },
      frame: {
        left: 50,
        top: 50,
        width: this.width,
        height: this.height
      },
      image: {
        left: 20,
        top: 20,
        width: 300,
        height: 400,
        bg: ["#1890FF"]
      },
      real: {
        width: 100,
        height: 100
      },
      target: {
        width: this.width,
        height: this.height
      },
      touches: [],
      type: "",
      start: {
        frame: {
          left: 0,
          top: 0,
          width: 0,
          height: 0
        },
        image: {
          left: 0,
          top: 0,
          width: 0,
          height: 0
        }
      },
      timeoutId: -1,
      context: null
    };
  },
  mounted() {
    this.context = common_vendor.index.createCanvasContext("canvas", this);
  },
  methods: {
    changeColor(color) {
      this.image.bg = color;
      this.trimImage();
    },
    imageLoad(ev) {
      this.mask.show = true;
      this.real.width = ev.detail.width;
      this.real.height = ev.detail.height;
      this.image.width = ev.detail.width;
      this.image.height = ev.detail.height;
      this.frame.width = this.width;
      this.frame.height = this.height;
      if (!this.fixed) {
        this.frame.width = this.image.width;
        this.frame.height = this.image.height;
      }
      var query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".body").boundingClientRect((data) => {
        var bw = data.width;
        var bh = data.height;
        var fw = this.frame.width;
        var fh = this.frame.height;
        var tw = bw * 0.8;
        var th = bh * 0.8;
        var sx = tw / fw;
        var sy = th / fh;
        var scale = sx;
        if (sx < sy) {
          scale = sy;
        }
        tw = fw * scale;
        th = fh * scale;
        var tx = (bw - tw) / 2;
        var ty = (bh - th) / 2;
        this.frame.width = tw;
        this.frame.height = th;
        this.frame.left = tx;
        this.frame.top = ty;
        var iw = this.image.width;
        var ih = this.image.height;
        sx = tw / iw;
        sy = th / ih;
        scale = sx;
        if (sx < sy) {
          scale = sy;
        }
        this.image.width = iw * scale;
        this.image.height = ih * scale;
        this.image.left = (bw - this.image.width) / 2;
        this.image.top = (bh - this.image.height) / 2;
        setTimeout(() => {
          this.trimImage();
        }, 100);
      }).exec();
    },
    touchHandle() {
    },
    touchStart(ev, type) {
      this.stopTime();
      this.mask.show = false;
      if (this.touches.length == 0) {
        this.type = type;
        this.start.frame.left = this.frame.left;
        this.start.frame.top = this.frame.top;
        this.start.frame.width = this.frame.width;
        this.start.frame.height = this.frame.height;
        this.start.image.left = this.image.left;
        this.start.image.top = this.image.top;
        this.start.image.width = this.image.width;
        this.start.image.height = this.image.height;
      }
      var touches = ev.changedTouches;
      for (var i = 0; i < touches.length; i++) {
        var touch = touches[i];
        this.touches.push(touch);
      }
    },
    touchMove(ev) {
      this.stopTime();
      ev.preventDefault();
      var touches = ev.touches;
      if (this.touches.length == 1) {
        if (this.type == "plank" || this.type == "frame" || this.fixed) {
          this.moveImage(this.touches[0], touches[0]);
        } else {
          this.scaleFrame(this.touches[0], touches[0], this.type);
        }
      } else if (this.touches.length == 2 && touches.length == 2) {
        var ta = this.touches[0];
        var tb = this.touches[1];
        var tc = touches[0];
        var td = touches[1];
        if (ta.identifier != tc.identifier) {
          var temp = tc;
          tc = td;
          td = temp;
        }
        this.scaleImage(ta, tb, tc, td);
      }
    },
    touchEnd(ev) {
      this.type = "";
      this.touches = [];
      this.startTime();
    },
    touchCancel(ev) {
      this.type = "";
      this.touches = [];
      this.startTime();
    },
    startTime() {
      this.stopTime();
      this.timeoutId = setTimeout(() => {
        this.trimImage();
      }, 800);
    },
    stopTime() {
      if (this.timeoutId >= 0) {
        clearTimeout(this.timeoutId);
        this.timeoutId = -1;
      }
    },
    trimImage() {
      this.mask.show = true;
      var query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".body").boundingClientRect((data) => {
        var bw = data.width;
        var bh = data.height;
        var fw = this.frame.width;
        var fh = this.frame.height;
        var tw = bw * 0.8;
        var th = bh * 0.8;
        var sx = tw / fw;
        var sy = th / fh;
        var scale = sx;
        if (sx > sy) {
          scale = sy;
        }
        tw = fw * scale;
        th = fh * scale;
        var tx = (bw - tw) / 2;
        var ty = (bh - th) / 2;
        var ax = tx - this.frame.left + (this.frame.left - this.image.left) * (1 - scale);
        var ay = ty - this.frame.top + (this.frame.top - this.image.top) * (1 - scale);
        this.frame.width = tw;
        this.frame.height = th;
        this.frame.left = tx;
        this.frame.top = ty;
        this.image.width *= scale;
        this.image.height *= scale;
        this.image.left += ax;
        this.image.top += ay;
      }).exec();
      setTimeout(() => {
        var scale = this.image.width / this.real.width;
        var x = (this.frame.left - this.image.left) / scale;
        var y = (this.frame.top - this.image.top) / scale;
        var width = this.frame.width / scale;
        var height = this.frame.height / scale;
        this.context.drawImage(this.url, x, y, width, height, 0, 0, this.frame.width, this.frame.height);
        console.log(this.url);
      }, 200);
    },
    moveImage(ta, tb) {
      var ax = tb.clientX - ta.clientX;
      var ay = tb.clientY - ta.clientY;
      this.image.left = this.start.image.left + ax;
      this.image.top = this.start.image.top + ay;
      if (this.image.left > this.frame.left) {
        this.image.left = this.frame.left;
      }
      if (this.image.top > this.frame.top) {
        this.image.top = this.frame.top;
      }
      if (this.image.left + this.image.width < this.frame.left + this.frame.width) {
        this.image.left = this.frame.left + this.frame.width - this.image.width;
      }
      if (this.image.top + this.image.height < this.frame.top + this.frame.height) {
        this.image.top = this.frame.top + this.frame.height - this.image.height;
      }
    },
    scaleImage(ta, tb, tc, td) {
      var x1 = ta.clientX;
      var y1 = ta.clientY;
      var x2 = tb.clientX;
      var y2 = tb.clientY;
      var x3 = tc.clientX;
      var y3 = tc.clientY;
      var x4 = td.clientX;
      var y4 = td.clientY;
      var ol = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
      var el = Math.sqrt((x3 - x4) * (x3 - x4) + (y3 - y4) * (y3 - y4));
      var ocx = (x1 + x2) / 2;
      var ocy = (y1 + y2) / 2;
      var ecx = (x3 + x4) / 2;
      var ecy = (y3 + y4) / 2;
      var ax = ecx - ocx;
      var ay = ecy - ocy;
      var scale = el / ol;
      if (this.start.image.width * scale < this.frame.width) {
        scale = this.frame.width / this.start.image.width;
      }
      if (this.start.image.height * scale < this.frame.height) {
        scale = this.frame.height / this.start.image.height;
      }
      if (this.start.image.width * scale < this.frame.width) {
        scale = this.frame.width / this.start.image.width;
      }
      this.image.left = this.start.image.left + ax - (ocx - this.start.image.left) * (scale - 1);
      this.image.top = this.start.image.top + ay - (ocy - this.start.image.top) * (scale - 1);
      this.image.width = this.start.image.width * scale;
      this.image.height = this.start.image.height * scale;
      if (this.image.left > this.frame.left) {
        this.image.left = this.frame.left;
      }
      if (this.image.top > this.frame.top) {
        this.image.top = this.frame.top;
      }
      if (this.image.left + this.image.width < this.frame.left + this.frame.width) {
        this.image.left = this.frame.left + this.frame.width - this.image.width;
      }
      if (this.image.top + this.image.height < this.frame.top + this.frame.height) {
        this.image.top = this.frame.top + this.frame.height - this.image.height;
      }
    },
    scaleFrame(ta, tb, type) {
      var ax = tb.clientX - ta.clientX;
      var ay = tb.clientY - ta.clientY;
      var x1 = this.start.frame.left;
      var y1 = this.start.frame.top;
      var x2 = this.start.frame.left + this.start.frame.width;
      var y2 = this.start.frame.top + this.start.frame.height;
      if (type == "left") {
        x1 += ax;
      } else if (type == "right") {
        x2 += ax;
      } else if (type == "top") {
        y1 += ay;
      } else if (type == "bottom") {
        y2 += ay;
      } else if (type == "left-top") {
        x1 += ax;
        y1 += ay;
      } else if (type == "left-bottom") {
        x1 += ax;
        y2 += ay;
      } else if (type == "right-top") {
        x2 += ax;
        y1 += ay;
      } else if (type == "right-bottom") {
        x2 += ax;
        y2 += ay;
      }
      if (x1 < this.image.left) {
        x1 = this.image.left;
      }
      if (y1 < this.image.top) {
        y1 = this.image.top;
      }
      if (x2 > this.image.left + this.image.width) {
        x2 = this.image.left + this.image.width;
      }
      if (y2 > this.image.top + this.image.height) {
        y2 = this.image.top + this.image.height;
      }
      this.frame.left = x1;
      this.frame.top = y1;
      this.frame.width = x2 - x1;
      this.frame.height = y2 - y1;
    },
    parseBlob(base64) {
      var arr = base64.split(",");
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      for (var i = 0; i < n; i++) {
        u8arr[i] = bstr.charCodeAt(i);
      }
      var url = URL || webkitURL;
      return url.createObjectURL(new Blob([u8arr], {
        type: mime
      }));
    },
    onok() {
      this.targetshow = true;
      this.targetContext = common_vendor.index.createCanvasContext("target", this);
      var scale = this.image.width / this.real.width;
      var x = (this.frame.left - this.image.left) / scale;
      var y = (this.frame.top - this.image.top) / scale;
      var width = this.frame.width / scale;
      var height = this.frame.height / scale;
      var tw = width;
      var th = height;
      if (this.fixed) {
        tw = this.width / 2;
        th = this.height / 2;
      } else {
        if (tw > this.maxWidth / 2) {
          var sc = this.maxWidth / 2 / tw;
          tw = tw * sc;
          th = th * sc;
        }
        if (th > this.maxHeight / 2) {
          var sc = this.maxHeight / 2 / th;
          th = th * sc;
          tw = tw * sc;
        }
      }
      this.target.width = tw;
      this.target.height = th;
      common_vendor.index.showLoading({
        title: "正在裁剪"
      });
      setTimeout(() => {
        if (this.image.bg.length > 1) {
          const gradient = this.targetContext.createLinearGradient(0, 0, 0, height);
          gradient.addColorStop(0, this.image.bg[0]);
          gradient.addColorStop(1, this.image.bg[1]);
          this.targetContext.setFillStyle(gradient);
        } else {
          this.targetContext.setFillStyle(this.image.bg[0]);
        }
        this.targetContext.fillRect(0, 0, tw, th);
        this.targetContext.drawImage(this.url, x, y, width, height, 0, 0, tw, th);
        this.targetContext.draw(false, () => {
          console.log("width", width, height, tw, th);
          common_vendor.index.canvasToTempFilePath(
            {
              destWidth: tw * 2,
              destHeight: th * 2,
              canvasId: "target",
              success: (res) => {
                var path = res.tempFilePath;
                this.$emit("ok", {
                  path
                });
                this.targetshow = false;
              },
              fail: (ev) => {
                console.log(ev);
              },
              complete: () => {
                common_vendor.index.hideLoading();
              }
            },
            this
          );
        });
      }, 100);
    },
    oncancle() {
      this.$emit("cancel");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.oncancle && $options.oncancle(...args)),
    b: common_vendor.o((...args) => $options.onok && $options.onok(...args)),
    c: $data.targetshow
  }, $data.targetshow ? {
    d: $data.target.width + "px",
    e: $data.target.height + "px"
  } : {}, {
    f: $props.url
  }, $props.url ? {
    g: common_vendor.o((...args) => $options.imageLoad && $options.imageLoad(...args)),
    h: $data.image.left + "px",
    i: $data.image.top + "px",
    j: $data.image.width + "px",
    k: $data.image.height + "px",
    l: $data.image.bg.length > 1 ? "linear-gradient(to bottom, " + $data.image.bg[0] + "," + $data.image.bg[1] + ");" : $data.image.bg[0],
    m: $props.url
  } : {}, {
    n: $data.mask.show
  }, $data.mask.show ? {} : {}, {
    o: $data.mask.show
  }, $data.mask.show ? {
    p: $data.frame.width + "px",
    q: $data.frame.height + "px"
  } : {}, {
    r: common_vendor.o(($event) => $options.touchStart($event, "left")),
    s: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    t: common_vendor.o(($event) => $options.touchStart($event, "right")),
    v: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    w: common_vendor.o(($event) => $options.touchStart($event, "top")),
    x: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    y: common_vendor.o(($event) => $options.touchStart($event, "bottom")),
    z: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    A: common_vendor.o(($event) => $options.touchStart($event, "left-top")),
    B: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    C: common_vendor.o(($event) => $options.touchStart($event, "left-bottom")),
    D: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    E: common_vendor.o(($event) => $options.touchStart($event, "right-top")),
    F: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    G: common_vendor.o(($event) => $options.touchStart($event, "right-bottom")),
    H: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    I: common_vendor.o(($event) => $options.touchStart($event, "frame")),
    J: common_vendor.o((...args) => $options.touchHandle && $options.touchHandle(...args)),
    K: $data.frame.left + "px",
    L: $data.frame.top + "px",
    M: $data.frame.width + "px",
    N: $data.frame.height + "px",
    O: common_vendor.o(($event) => $options.touchStart($event, "plank")),
    P: common_vendor.o((...args) => $options.touchMove && $options.touchMove(...args)),
    Q: common_vendor.o((...args) => $options.touchEnd && $options.touchEnd(...args)),
    R: common_vendor.o((...args) => $options.touchCancel && $options.touchCancel(...args)),
    S: common_vendor.n($data.image.bg[0] == "#1890FF" ? "active" : ""),
    T: common_vendor.o(($event) => $options.changeColor(["#1890FF"])),
    U: common_vendor.n($data.image.bg[0] == "#F24747" ? "active" : ""),
    V: common_vendor.o(($event) => $options.changeColor(["#F24747"])),
    W: common_vendor.n($data.image.bg[0] == "#ffffff" ? "active" : ""),
    X: common_vendor.o(($event) => $options.changeColor(["#ffffff"])),
    Y: common_vendor.n($data.image.bg[0] == "#000000" && $data.image.bg[1] == "#ffffff" ? "active" : ""),
    Z: common_vendor.o(($event) => $options.changeColor(["#000000", "#ffffff"])),
    aa: common_vendor.n($data.image.bg[0] == "#1890FF" && $data.image.bg[1] == "#ffffff" ? "active" : ""),
    ab: common_vendor.o(($event) => $options.changeColor(["#1890FF", "#ffffff"])),
    ac: $props.url
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0a0bc55f"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/me-cutter/me-cutter.vue"]]);
wx.createComponent(Component);
