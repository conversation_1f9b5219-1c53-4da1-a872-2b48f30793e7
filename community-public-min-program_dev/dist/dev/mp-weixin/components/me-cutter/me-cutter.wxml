<view hidden="{{!ac}}" class="ksp-image-cutter data-v-0a0bc55f"><view class="header data-v-0a0bc55f"><text bindtap="{{a}}" class="btn-cancel data-v-0a0bc55f">返回</text><text bindtap="{{b}}" class="btn-ok data-v-0a0bc55f">确定</text></view><canvas wx:if="{{c}}" class="data-v-0a0bc55f" style="{{'width:' + d + ';' + ('height:' + e)}}" canvas-id="target"></canvas><view class="body data-v-0a0bc55f"><image wx:if="{{f}}" class="image data-v-0a0bc55f" bindload="{{g}}" style="{{'left:' + h + ';' + ('top:' + i) + ';' + ('width:' + j) + ';' + ('height:' + k) + ';' + ('background:' + l)}}" src="{{m}}"></image><view wx:if="{{n}}" class="mask data-v-0a0bc55f"></view><view bindtouchstart="{{O}}" bindtouchmove="{{P}}" bindtouchend="{{Q}}" bindtouchcancel="{{R}}" class="plank data-v-0a0bc55f"><view class="frame data-v-0a0bc55f" bindtouchstart="{{I}}" catchtouchstart="{{J}}" style="{{'left:' + K + ';' + ('top:' + L) + ';' + ('width:' + M) + ';' + ('height:' + N)}}"><canvas wx:if="{{o}}" class="canvas data-v-0a0bc55f" style="{{'width:' + p + ';' + ('height:' + q)}}" canvas-id="canvas"></canvas><view class="rect data-v-0a0bc55f"></view><view class="line-one data-v-0a0bc55f"></view><view class="line-two data-v-0a0bc55f"></view><view class="line-three data-v-0a0bc55f"></view><view class="line-four data-v-0a0bc55f"></view><view bindtouchstart="{{r}}" catchtouchstart="{{s}}" class="frame-left data-v-0a0bc55f"></view><view bindtouchstart="{{t}}" catchtouchstart="{{v}}" class="frame-right data-v-0a0bc55f"></view><view bindtouchstart="{{w}}" catchtouchstart="{{x}}" class="frame-top data-v-0a0bc55f"></view><view bindtouchstart="{{y}}" catchtouchstart="{{z}}" class="frame-bottom data-v-0a0bc55f"></view><view bindtouchstart="{{A}}" catchtouchstart="{{B}}" class="frame-left-top data-v-0a0bc55f"></view><view bindtouchstart="{{C}}" catchtouchstart="{{D}}" class="frame-left-bottom data-v-0a0bc55f"></view><view bindtouchstart="{{E}}" catchtouchstart="{{F}}" class="frame-right-top data-v-0a0bc55f"></view><view bindtouchstart="{{G}}" catchtouchstart="{{H}}" class="frame-right-bottom data-v-0a0bc55f"></view></view></view></view><view class="toolbar data-v-0a0bc55f"><view class="color-box data-v-0a0bc55f"><view class=" data-v-0a0bc55f" bindtap="{{T}}"><view class="{{['color-block', 'color1', 'data-v-0a0bc55f', S]}}"></view><text class="data-v-0a0bc55f">蓝色</text></view><view class=" data-v-0a0bc55f" bindtap="{{V}}"><view class="{{['color-block', 'color2', 'data-v-0a0bc55f', U]}}"></view><text class="data-v-0a0bc55f">红色</text></view><view class=" data-v-0a0bc55f" bindtap="{{X}}"><view class="{{['color-block', 'color3', 'data-v-0a0bc55f', W]}}"></view><text class="data-v-0a0bc55f">白色</text></view><view class=" data-v-0a0bc55f" bindtap="{{Z}}"><view class="{{['color-block', 'color4', 'data-v-0a0bc55f', Y]}}"></view><text class="data-v-0a0bc55f">渐变灰</text></view><view class=" data-v-0a0bc55f" bindtap="{{ab}}"><view class="{{['color-block', 'color5', 'data-v-0a0bc55f', aa]}}"></view><text class="data-v-0a0bc55f">渐变蓝</text></view></view></view></view>