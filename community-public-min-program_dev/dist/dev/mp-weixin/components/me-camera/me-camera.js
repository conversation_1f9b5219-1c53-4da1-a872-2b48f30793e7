"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      cameraContext: {},
      cameraHeight: "",
      position: "back"
    };
  },
  created() {
    let systemInfo = common_vendor.index.getSystemInfoSync();
    this.windowHeight = systemInfo.windowHeight;
    this.cameraHeight = systemInfo.windowHeight - 80;
    if (common_vendor.index.createCameraContext) {
      this.cameraContext = common_vendor.index.createCameraContext();
    } else {
      common_vendor.index.showModal({
        title: "提示",
        content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
      });
    }
  },
  methods: {
    back() {
      if (this.position == "back") {
        this.position = "front";
      } else {
        this.position = "back";
      }
    },
    // 拍照
    takePhoto() {
      this.cameraContext.takePhoto({
        quality: "normal",
        success: (res) => {
          this.$emit("photo", res.tempImagePath);
          this.$emit("show", false);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "拍照失败，请检查系统是否授权",
            icon: "none",
            duration: 1200
          });
        }
      });
    },
    // 从相册选取
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album"],
        success: (res) => {
          const src = res.tempFilePaths[0];
          common_vendor.index.getFileInfo({
            filePath: src,
            success: async (info) => {
              const compressRes = await this.compress(info.size, src);
              common_vendor.index.getImageInfo({
                src: compressRes.tempFilePath,
                success: (imageInfo) => {
                  this.$emit("photo", imageInfo.path);
                  this.$emit("show");
                }
              });
            }
          });
        }
      });
    },
    async compress(size, src) {
      const maxSize = 4096e3;
      let option = {};
      if (size > maxSize) {
        const quality = maxSize / size;
        option.quality = parseInt(quality * 100);
      } else {
        option.quality = 100;
      }
      const imageInfo = await common_vendor.index.getImageInfo({
        src
      });
      const width = imageInfo.width;
      const height = imageInfo.height;
      const max = 4096;
      if (width > max && height > max) {
        if (width > height) {
          option.compressedWidth = max;
        } else {
          option.compressedHeight = max;
        }
      } else if (width > max) {
        option.compressedWidth = max;
      } else if (height > max) {
        option.compressedHeight = max;
      }
      option.src = src;
      return common_vendor.index.compressImage(option);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.position,
    b: $data.cameraHeight + "px",
    c: common_vendor.o((...args) => $options.back && $options.back(...args)),
    d: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    e: _ctx.windowHeight + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e8c95d73"], ["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/me-camera/me-camera.vue"]]);
wx.createComponent(Component);
