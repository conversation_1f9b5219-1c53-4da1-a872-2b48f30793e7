/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.controls.data-v-e8c95d73 {
  position: relative;
  display: flex;
  justify-content: center;
}
.controls .icon-w569-h828.data-v-e8c95d73 {
  margin-top: 200rpx;
  width: 692rpx;
  height: 684rpx;
}
.bottom.data-v-e8c95d73 {
  width: 100%;
  background-color: #000;
}
.bottom .icon-w131-h131.data-v-e8c95d73 {
  width: 100rpx;
  height: 100rpx;
}
.bottom .wrap.data-v-e8c95d73 {
  color: #fefefe;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  padding: 0 73rpx;
}
.bottom .wrap .icon-box .icon.data-v-e8c95d73 {
  width: 80rpx;
  height: 80rpx;
}