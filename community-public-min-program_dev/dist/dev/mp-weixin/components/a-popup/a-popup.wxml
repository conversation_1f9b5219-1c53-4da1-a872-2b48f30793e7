<view><uv-popup wx:if="{{l}}" class="r" u-s="{{['d']}}" u-r="popup" u-i="5d875bea-0" bind:__l="__l" u-p="{{l}}"><view style="width:300px;height:440rpx;background:linear-gradient( 180deg, #FFF3CF 0%, #FFFFFF 32%, #FFFFFF 100%);border-radius:8px"><view style="width:80%;height:100%;margin:0 auto;display:flex;flex-direction:column;justify-content:space-around;align-items:center"><view>{{a}}</view><view style="text-align:left;font-weight:400;font-size:14px;color:#999999">{{b}}</view><view style="width:100%;display:flex;flex-direction:row;justify-content:space-between"><button hidden="{{!d}}" style="{{'width:' + e + ';' + 'background-color:#CCCCCC;font-weight:500;font-size:16px;color:#FFFFFF'}}" bindtap="{{f}}">{{c}}</button><button hidden="{{!h}}" style="{{'width:' + i + ';' + 'background-color:#2BBC4A;font-weight:500;font-size:16px;color:#FFFFFF'}}" bindtap="{{j}}">{{g}}</button></view></view></view></uv-popup></view>