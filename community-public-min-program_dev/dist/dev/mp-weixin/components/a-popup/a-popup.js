"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_uv_popup2 = common_vendor.resolveComponent("uv-popup");
  _easycom_uv_popup2();
}
const _easycom_uv_popup = () => "../../uni_modules/uv-popup/components/uv-popup/uv-popup.js";
if (!Math) {
  _easycom_uv_popup();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "a-popup",
  props: {
    title: {
      type: String
    },
    content: {
      type: String
    },
    cancelText: {
      type: String
    },
    confimText: {
      type: String,
      default: "已知晓"
    }
  },
  emits: ["emitEvent"],
  setup(__props, { expose, emit }) {
    const props = __props;
    const popup = common_vendor.ref();
    const open = () => {
      popup.value.open();
    };
    const close = () => {
      popup.value.close();
    };
    const closeEmit = () => {
      popup.value.close();
      emit("emitEvent");
    };
    expose({ open, close });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(props.title),
        b: common_vendor.t(props.content),
        c: common_vendor.t(props.cancelText),
        d: props.cancelText,
        e: props.cancelText && props.confimText ? "45%" : "100%",
        f: common_vendor.o(close),
        g: common_vendor.t(props.confimText),
        h: props.confimText,
        i: props.cancelText && props.confimText ? "45%" : "100%",
        j: common_vendor.o(closeEmit),
        k: common_vendor.sr(popup, "5d875bea-0", {
          "k": "popup"
        }),
        l: common_vendor.p({
          mode: "center",
          closeable: true
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/a-popup/a-popup.vue"]]);
wx.createComponent(Component);
