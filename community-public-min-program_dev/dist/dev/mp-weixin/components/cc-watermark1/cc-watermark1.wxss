/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.reduce-container.data-v-a93db8b1 {
  left: 15px;
  transform: scale(0.4);
  opacity: 1 !important;
  z-index: 10;
  transition: 0.2s;
}
.default-container.data-v-a93db8b1 {
  left: -200%;
}
.widget.data-v-a93db8b1 {
  position: absolute;
  left: 0;
  top: -100%;
  opacity: 0;
}
.watermark-content.data-v-a93db8b1 {
  overflow-y: auto;
}
.container.data-v-a93db8b1 {
  position: absolute;
  border-radius: 10rpx;
  background-color: rgba(255, 255, 255, 0.8);
  opacity: 0;
  transform-origin: bottom left;
}
.container .title.data-v-a93db8b1 {
  background-color: #2BBC4A;
  font-size: 38px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  font-weight: bold;
  border-radius: 10rpx;
}
.container .row.data-v-a93db8b1 {
  font-size: 36px;
  color: #333;
  padding: 0 10px;
  line-height: 54px;
}
.popup-form.data-v-a93db8b1 {
  padding: 40rpx;
}