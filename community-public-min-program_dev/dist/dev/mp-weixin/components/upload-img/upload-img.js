"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
if (!Array) {
  const _easycom_uv_upload2 = common_vendor.resolveComponent("uv-upload");
  _easycom_uv_upload2();
}
const _easycom_uv_upload = () => "../../uni_modules/uv-upload/components/uv-upload/uv-upload.js";
if (!Math) {
  _easycom_uv_upload();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "upload-img",
  props: {
    modelValue: {
      type: Array
    },
    maxCount: {
      type: Number,
      default: 9
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: "image"
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit }) {
    const prop = __props;
    const fileList = common_vendor.computed({
      get() {
        return prop.modelValue;
      },
      set(fileList2) {
        emit("update:modelValue", fileList2);
      }
    });
    const afterRead = (event) => {
      if (prop.multiple) {
        event.file.forEach((file) => {
          upload(file.url);
        });
      } else {
        upload(event.file.url);
      }
    };
    const deleteFile = (e) => {
      if (prop.disabled) {
        return;
      }
      fileList.value.splice(e.index);
    };
    const upload = (filePath) => {
      var _a;
      if (((_a = fileList.value) == null ? void 0 : _a.length) >= prop.maxCount) {
        common_vendor.index.showToast({ title: `最多允许上传${prop.maxCount}张图片`, icon: "none" });
        return false;
      }
      uploadFile(filePath).then((res) => {
        var _a2;
        if (((_a2 = fileList.value) == null ? void 0 : _a2.length) < prop.maxCount)
          fileList.value.push({ url: res.url, message: res.ossId });
      }).catch(() => {
      });
    };
    const uploadFile = (filePath) => {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${common_config.API_URL}/resource/oss/upload`,
          //仅为示例，非真实的接口地址
          filePath,
          name: "file",
          header: {
            clientid: common_config.CLIENT_ID,
            Authorization: common_vendor.index.getStorageSync("token")
          },
          success: function(uploadFileRes) {
            resolve(JSON.parse(uploadFileRes.data).data);
          },
          complete: function(e) {
          }
        });
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o((e) => {
          afterRead(e);
        }),
        b: common_vendor.o((e) => {
          deleteFile(e);
        }),
        c: common_vendor.p({
          fileList: common_vendor.unref(fileList),
          multiple: prop.multiple,
          accept: prop.accept,
          disabled: prop.disabled,
          maxCount: prop.maxCount,
          previewFullImage: true
        }),
        d: prop.multiple && prop.maxCount > 0
      }, prop.multiple && prop.maxCount > 0 ? {
        e: common_vendor.t(common_vendor.unref(fileList).length),
        f: common_vendor.t(prop.maxCount)
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/waibao/cz/community-public-min-program/community-public-min-program_dev/src/components/upload-img/upload-img.vue"]]);
wx.createComponent(Component);
