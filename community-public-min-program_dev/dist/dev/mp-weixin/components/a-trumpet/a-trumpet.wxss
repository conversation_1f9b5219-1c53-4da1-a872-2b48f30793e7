/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-e5e05040 {
  box-sizing: border-box;
}
.box.data-v-e5e05040 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.audio-style.data-v-e5e05040 {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.audio-style > view.data-v-e5e05040 {
  border: 2px solid transparent;
  border-radius: 50%;
}
.small.data-v-e5e05040 {
  border: 0px solid;
  width: 3px;
  height: 3px;
}
.middle.data-v-e5e05040 {
  width: 16px;
  height: 16px;
  margin-left: -11px;
  opacity: 1;
}
.large.data-v-e5e05040 {
  width: 24px;
  height: 24px;
  margin-left: -19px;
  opacity: 1;
}
.animation .middle.data-v-e5e05040 {
  animation: middle-e5e05040 1.2s ease-in-out infinite;
}
.animation .large.data-v-e5e05040 {
  animation: large-e5e05040 1.2s ease-in-out infinite;
}
@keyframes middle-e5e05040 {
0% {
    opacity: 0;
}
10% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes large-e5e05040 {
0% {
    opacity: 0;
}
50% {
    opacity: 1;
}
60% {
    opacity: 0;
}
100% {
    opacity: 0;
}
}