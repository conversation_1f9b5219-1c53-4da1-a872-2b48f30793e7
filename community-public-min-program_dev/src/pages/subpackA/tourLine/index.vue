<template>
  <z-paging ref="paging" v-model="dataList" @query="queryList" :auto-show-back-to-top="true">
    <template #top>
        <view style="position: relative">
          <uv-swiper height="500rpx" :list="list"></uv-swiper>

          <!-- 切换辖区按钮 -->
          <view class="district-selector">
            <uv-button
              type="primary"
              size="small"
              :customStyle="{
                backgroundColor: '#2BBC4A',
                borderRadius: '20rpx',
                fontSize: '28rpx',
                padding: '10rpx 20rpx'
              }"
              @click="showDistrictPicker"
            >
              {{ selectedDistrictName || '切换辖区' }}
            </uv-button>
          </view>

<!--          <view style="position:absolute;bottom: 50rpx;" color="#2BBC4A" >
            <view style="justify-content: left;display: flex;flex-wrap: wrap;">
              <view class="btn tlBg">旅游路线</view>
              <view class="btn npBg">特产列表</view>
            </view>
          </view>-->
        </view>
        <!-- <view class="content">
          <view class="search">
            <image style="height: 40rpx;width: 40rpx;min-width:40rpx;" src="/static/tourLine/search.png" mode=""></image>
            <view style="width: 100%;">
              <uv-input v-model="attractionName" placeholder="搜索" color="#395C16" :placeholderStyle="{color: '#a0a969'}" border="none" ></uv-input>
            </view>
            <uv-button type="warning" :customStyle="{height:'50rpx'}" @click="search">搜索</uv-button>
          </view>
          <view class="hots">
            <view class="title" style="min-width: 90px;">热门景点：</view>
            <view class="labels">
              <view class="label">南宝山</view>
              <view class="label">天台山</view>
              <view class="label">平乐古镇</view>
            </view>
          </view>
        </view> -->
    </template>
      <view class="list" v-for="(item,index) in dataList" :key="index"
            @click="itemClick(item)">
        <view class="imgList">
          <image 
            v-for="(attraction, idx) in (item.attractionsList || []).slice(0, 3)" 
            :key="idx"
            mode="aspectFill" 
             
            :src="attraction?.attractionImages" 
            >
          </image>
        </view>
        <view class="info">
          <view class="title">{{item.tourLineName}}</view>
  <!--          <view class="tag">#{{}}</view>-->
          <view class="desc" style="display: flex;">
             <text>总行程{{ item.travelDays }}天 推荐地{{ item.attractionsList ? item.attractionsList.length : 0 }}处</text>
          </view>
          <!-- <view class="time">{{item.updateTime}}</view> -->
        </view>
      </view>
  </z-paging>

  <!-- 辖区选择器 -->
  <uv-picker
    ref="districtPickerRef"
    :columns="districtColumns"
    @confirm="onDistrictConfirm"
    @cancel="onDistrictCancel"
    :closeOnClickOverlay="true"
  ></uv-picker>
</template>

<script setup lang="ts">
  import {ref} from "vue";
  import {getPageList, getDistrictData} from "@/common/api/tourLine";
  const list = ref<any[]>([]);

  // 辖区选择相关变量
  const districtPickerRef = ref<any>();
  const districtColumns = ref<any[]>([]);
  const selectedDistrictName = ref('邛崃市');
  const selectedDistrictCode = ref('');

  function itemClick(item: any) {
    // 现在点击的是旅游线路，可以跳转到线路详情页
    const url = '/pages/subpackA/tourLine/details?id=' + item.tourLineId;
    uni.navigateTo({
      url
    })
  }
  const queryList = (pageNo : number, pageSize : number) => {
    getDataList(pageNo, pageSize)
  }
  const paging = ref<any>()
  const dataList = ref<any[]>([])
  const attractionName = ref('');

  /**
   * 搜索
   */
  function search(){
    paging.value.reload()
  }
  /** 获取列表 */
  function getDataList(offset : number, count : number) {
    // 使用选中的辖区代码，如果没有选中则使用存储的区域码
    const districtCode = selectedDistrictCode.value || uni.getStorageSync('areaCode');

    getPageList({
      pageNum: offset,
      pageSize: count,
      districtCode: districtCode, // 使用选中的辖区代码
      sortType: 1, // 1-最新, 可根据需要调整
      attractionName: attractionName.value, // 添加搜索关键词
    }).then((res: any) => {
      console.log('getPageList返回数据:', res)
      
      // 直接使用旅游线路数据，不展平景点
      if (res.data && res.data.records && Array.isArray(res.data.records)) {
        const tourLines = res.data.records;
        
        // 处理轮播图数据 - 从第一条线路的第一个景点获取图片
        if (list.value.length === 0 && tourLines.length > 0) {
          const firstTourLine = tourLines[0];
          if (firstTourLine.attractionsList && firstTourLine.attractionsList.length > 0) {
            const firstAttraction = firstTourLine.attractionsList[0];
            if (firstAttraction.attractionImages) {
              list.value.push(firstAttraction.attractionImages);
            }
          }
        }
        
        // 判断是否还有更多数据
        if (res.data.total > offset * count || offset < 2) {
          paging.value.complete(tourLines);
        } else {
          paging.value.complete([]);
        }
      } else {
        console.log('数据结构不符合预期，请检查接口返回格式');
        paging.value.complete([]);
      }
    }).catch((error: any) => {
      console.error('获取景点列表失败:', error);
      paging.value.complete([]);
    })
  }

  /**
   * 显示辖区选择器
   */
  function showDistrictPicker() {
    uni.showLoading({
      title: '加载中...'
    });

    getDistrictData({}).then((res: any) => {
      uni.hideLoading();
      console.log('获取辖区数据:', res);

      if (res && res.data) {
        // 处理数据，提取社区信息
        const communities: any[] = [];

        // 添加"全部"选项（邛崃市）
        communities.push({
          text: '全部',
          value: '',
          code: ''
        });

        // 处理district数据
        if (res.data.district && Array.isArray(res.data.district)) {
          res.data.district.forEach((item: any) => {
            communities.push({
              text: item.name,
              value: item.code,
              code: item.code
            });
          });
        }

        // 处理hotCommunity数据
        if (res.data.hotCommunity && Array.isArray(res.data.hotCommunity)) {
          res.data.hotCommunity.forEach((item: any) => {
            communities.push({
              text: item.districtName,
              value: item.districtCode,
              code: item.districtCode
            });
          });
        }

        // 设置picker数据
        districtColumns.value = [communities];

        // 显示picker
        districtPickerRef.value.open();
      } else {
        uni.showToast({
          title: '获取辖区数据失败',
          icon: 'none'
        });
      }
    }).catch((error: any) => {
      uni.hideLoading();
      console.error('获取辖区数据失败:', error);
      uni.showToast({
        title: '获取辖区数据失败',
        icon: 'none'
      });
    });
  }

  /**
   * 辖区选择确认
   */
  function onDistrictConfirm(event: any) {
    console.log('选择的辖区:', event);
    const selectedItem = event.value[0];

    selectedDistrictName.value = selectedItem.text;
    selectedDistrictCode.value = selectedItem.code;

    // 重新加载数据
    paging.value.reload();
  }

  /**
   * 辖区选择取消
   */
  function onDistrictCancel() {
    console.log('取消选择辖区');
  }

  function openLocation(item: any){
    uni.openLocation({
      latitude: parseFloat(item.latitude),
      longitude: parseFloat(item.longitude),
      //name: item.name,
      address: item.attractionAddress,
      success: function (res) {
        console.log('打开系统位置地图成功')
      },
      fail: function (error) {
        console.log(error)
      }
    });
  }

</script>
<style lang="scss">
page{
  background: #F5F0C3;
}

/* 辖区选择器样式 */
.district-selector {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
}
</style>
<style lang="scss" scoped>
.icon{
  height: 40rpx;width: 40rpx;min-width: 40rpx;
}
.btn{
  width: 200rpx;
  height: 80rpx;
  background-size: 100%;
  margin-left: 10rpx;
  text-align: center;
  line-height: 65rpx;
}
.tlBg{
  background-image: url('/static/tourLine/button.png');
  background-repeat: repeat;
  color: #FFFFFF;
}
.npBg{
  background-image: url('/static/tourLine/nativeProduct.png');
  background-repeat: repeat;
  color: #333333;
}
.search{
  background:#E0D99C;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 10rpx;
  color: #395C16;
}
.content {
  position: relative;
  /*height: calc(100vh - 500rpx);*/
  min-height: 50rpx;
  background: #F5F0C3;
  border-radius: 50rpx 50rpx 0 0;
  margin-top: -50rpx;
  padding: 30rpx 20rpx 0 20rpx;
}
.hots{
  display: flex;
  justify-content: left;
  align-items: baseline;
  padding: 20rpx 0;
}
.title{
  // font-weight: bold;
  color: #000;
  // min-width: 140rpx;
  font-size: 28rpx;
}
.labels{
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10rpx;
  .label{
    border:2rpx solid #E0D99C;
    border-radius: 10rpx;
    padding: 10rpx;
    color: #a0a969;
    margin: 5rpx 10rpx;
  }
  .label:hover{
    background: #395C16;
    color: #FFFFFF;
  }
}
.list{
  // display: flex;
  // justify-content: left;
  // align-items: center;
  border-radius: 10rpx;
  background: #ffffff;
  margin: 20rpx;
  color: #395C16;
  .info{
    padding: 10rpx;
    
  }
  .desc{
    text{
      font-size: 24rpx;
      color: #999;
    }
  }
  .tag{
    color: #FF9F18;
  }
  .time{
    color: #a0a969;
  }
  .imgList{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5rpx;
    image{
      width: 100%;
      height: 160rpx;
      &:nth-child(1){
        border-radius: 20rpx 0 0 0;
      }

      &:nth-child(3){
        border-radius: 0 20rpx 0 0;
      }
    }
  }
}
</style>