<template>
  <view class="page-container" :class="{ fullscreen: isFullScreen }">
    <!-- 地图区域 -->
    <!-- GPS轨迹地图区域 -->
    <view class="gps-map-container">

      <!-- 当前位置信息显示 -->
      <view class="location-info" v-if="currentLocation && showCurrentLocation">
        <!-- 距离信息行 -->
        <view class="distance-info-row">
          <view class="distance-item">
            <text class="distance-value">{{ locationStats.distanceToStart }}</text>
            <text class="distance-label">距起点(km)</text>
          </view>
          <view class="distance-item">
            <text class="distance-value">{{ locationStats.distanceToEnd }}</text>
            <text class="distance-label">距终点(km)</text>
          </view>
          <view class="distance-item">
            <text class="distance-value">{{ locationStats.distanceToMe }}</text>
            <text class="distance-label">距离我(km)</text>
          </view>
          <view class="distance-item">
            <text class="distance-value">{{ locationStats.altitude }}</text>
            <text class="distance-label">海拔(m)</text>
          </view>
        </view>

        <!-- 坐标信息行 -->
        <view class="coordinate-row">
          <text class="coordinate-text">{{ currentLocation.latitude.toFixed(7) }}°N, {{ currentLocation.longitude.toFixed(7) }}°E</text>
          <text class="coordinate-text">海拔{{ locationStats.altitude }}m误差{{ locationStats.accuracy }}m</text>
          <!-- <view class="gps-status">
            <view class="gps-icon"></view>
            <text class="gps-text">GPS</text>
            <text class="gps-signal">{{ locationStats.signalStrength }}</text>
          </view> -->
        </view>
      </view>

      <map
        id="gps-track-map"
        :latitude="gpsMapCenter.latitude"
        :longitude="gpsMapCenter.longitude"
        :scale="mapScale"
        :markers="dynamicGPSMarkers"
        :polyline="gpsPolyline"
        :show-location="true"
        enable-3D="false"
        show-compass="true"
        enable-overlooking="false"
        enable-zoom="true"
        enable-scroll="true"
        enable-rotate="false"
        :style="mapStyle"
        @markertap="onGPSMarkerTap"
        @tap="onGPSMapTap"
        @regionchange="onGPSRegionChange"
      ></map>

      <!-- 全屏按钮 -->
      <view class="screen" @click="toggleFullScreen">
        <image :src="isFullScreen ? '/static/tourLine/exitfullscreen.png' : '/static/tourLine/fullscreen.png'" class="location-icon"></image>
      </view>
      <!-- 当前位置定位按钮 -->
      <view class="location-btn" @click="getCurrentLocation">
        <image src="/static/tourLine/person_map.png" class="location-icon"></image>
      </view>

    </view>

    <!-- 下方内容区域 - 全屏时隐藏 -->
    <view class="content-section" v-show="!isFullScreen">

    <!-- 拍摄点图片弹窗 -->
    <view class="photo-detail-modal" v-if="showPointDetail" @click="closePointDetail">
      <view class="photo-modal-content" @click.stop>
        <view class="photo-modal-header">
          <text class="photo-modal-title">位置详情</text>
          <view class="photo-close-btn" @click="closePointDetail">×</view>
        </view>
        <view class="photo-modal-body" v-if="selectedGPSPoint">
          <!-- 主要图片展示区域 -->
          <view class="photo-container" v-if="selectedGPSPoint.attractionImages || selectedGPSPoint.image">
            <image 
              :src="selectedGPSPoint.attractionImages || selectedGPSPoint.image" 
              mode="aspectFit" 
              class="main-photo"
              @click="previewImage(selectedGPSPoint.attractionImages || selectedGPSPoint.image)"
              @error="onImageError"
              @load="onImageLoad"
            ></image>
            <view class="photo-overlay">
              <text class="photo-tip">点击查看大图</text>
            </view>
          </view>
          
         
        </view>
      </view>
    </view>
    
    <!-- 顶部区域：线路标题和基本信息 -->
    <view class="header-section">
      <view class="title-area">
        <text class="tour-title">{{ data.tourLineName }}</text>
      </view>
      
      <!-- 行程信息 -->
      <view class="trip-info">
        <text class="trip-text">总行程{{ data.travelDays }}天</text>
        <text class="spots-text">推荐地{{ filteredAttractionsList.length }}处</text>
        <text class="area-text">{{ data.districtAddress || '文君井社区' }}</text>
      </view>
      
      <!-- 介绍按钮 -->
    
    </view>
    
    <!-- 景点列表 -->
    <view class="attractions-section">
      <view class="attraction-list">
        <text class="intro-text">简介</text>
        <view class="attraction-list-content">
          <view 
          class="attraction-item" 
          v-for="(attraction, index) in filteredAttractionsList" 
          :key="attraction.attractionId" 
          @click="selectAttraction(attraction, index)"
          :class="{ 'selected': selectedAttractionId == attraction.attractionId }"
        >
          <view class="attraction-image">
            <image 
              :src="attraction.attractionImages" 
              mode="aspectFill"
            ></image>
          </view>
          <view class="attraction-info">
            <text class="attraction-name">{{ attraction.attractionName }}</text>
          </view>
        </view>
        </view>
      
      </view>
      <view class="attraction-text">
        <text>{{ selectedAttraction ? selectedAttraction.attractionIntroduction?.replace(/<[^>]*>/g, '') : data.tourLineIntroduction }}</text>
        <button class="guide-btn" @click="toGuide" v-if="selectedAttraction">查看路线</button>
      </view>
    </view>

    </view> <!-- 关闭 content-section -->
  </view>
</template>

<script setup lang="ts">
  import {ref, nextTick, computed} from "vue";
  import {getPageItemDetail} from "@/common/api/tourLine";
  import {onLoad, onShow} from "@dcloudio/uni-app";
  import {useShare} from "@/common/utils/share";
  // @ts-ignore
  import coordtransform from 'coordtransform';
  
  const id = ref('')
  const data = ref<any>({})
  const showIntroduction = ref(false)
  const selectedAttraction = ref<any>(null)
  
  // GPS地图相关数据
  const gpsMapCenter = ref({
    latitude: 30.3262018,
    longitude: 103.2816216
  })
  const gpsMarkers = ref<any[]>([])
  const gpsPolyline = ref<any[]>([])
  const selectedGPSPoint = ref<any>(null)
  const showPointDetail = ref(false)

  // 当前位置相关数据
  const currentLocation = ref<any>(null)
  const showCurrentLocation = ref(false)
  const locationOffset = ref<string>('')

  // 位置统计数据
  const locationStats = ref({
    distanceToStart: '0.00',
    distanceToEnd: '0.00',
    distanceToMe: '0.00',
    altitude: '0',
    accuracy: '0',
    signalStrength: '0'
  })

  // 全屏状态
  const isFullScreen = ref(false)

  // 地图样式计算属性
  const mapStyle = computed(() => {
    if (isFullScreen.value) {
      return 'width: 100%; height: 100vh; position: fixed; top: 0; left: 0; z-index: 9999;'
    } else {
      return 'width: 100%; height: 600rpx;'
    }
  })
  
  // 新增：选中的景点ID和动画状态
  const selectedAttractionId = ref<number | null>(null)
  const markerAnimationState = ref<{[key: number]: boolean}>({})
  const mapScale = ref(14) // 地图缩放级别
  
  // 分享
  const {setShare} = useShare();
  let share = {};

  // 过滤后的景点列表（只显示途径点，不显示起点和终点）
  const filteredAttractionsList = computed(() => {
    if (!data.value.attractionsList) return [];
    
    return data.value.attractionsList.filter((attraction: any) => {
      const pointFlag = attraction.pointFlag;
      // 只显示途径点（pointFlag为1），隐藏起点（0）和终点（2）
      return pointFlag !== 0 && pointFlag !== '0' && pointFlag !== 2 && pointFlag !== '2';
    });
  });
  
  // 动态GPS标记点
  const dynamicGPSMarkers = computed(() => {
    const markers = gpsMarkers.value.map((marker: any, index: number) => {
      const isSelected = selectedAttractionId.value == marker.attractionData?.attractionId;
      // 只有选中的景点才能有动画状态
      const isAnimating = isSelected && markerAnimationState.value[marker.attractionData?.attractionId];

      // 图标尺寸保持不变，只有选中时稍微大一点
      let iconPath = marker.iconPath;
      let width = isSelected ? 30 : 20;
      let height = isSelected ? 30 : 20;

      // 图标本身不跳动，保持固定尺寸
      // 动画效果只体现在callout（提示框）上

      // 创建完全独立的标记对象
      return {
        id: marker.id,
        latitude: marker.latitude,
        longitude: marker.longitude,
        iconPath: iconPath,
        width: width,
        height: height,
        alpha: 1, // 图标透明度保持不变
        title: marker.title,
        attractionData: marker.attractionData,
        // 图标保持不变，不需要特殊标识
        callout: {
          content: marker.callout?.content || '',
          display: isSelected ? 'ALWAYS' : 'BYCLICK',
          // 动画时保持绿色，只改变提示框大小
          bgColor: isSelected ? '#19993E' : '#ffffff',
          color: isSelected ? '#ffffff' : '#003F03',
          fontSize: (isSelected && isAnimating) ? 18 : 14, // 动画时字体更大
          borderRadius: (isSelected && isAnimating) ? 15 : 8,
          borderWidth: (isSelected && isAnimating) ? 4 : 2, // 动画时边框更粗
          borderColor: isSelected ? '#ffffff' : '#ffffff',
          padding: (isSelected && isAnimating) ? 16 : 8, // 动画时内边距更大
          textAlign: 'center'
        }
      };
    });

    // 如果有当前位置，添加当前位置标记
    if (currentLocation.value && showCurrentLocation.value) {
      markers.push({
        id: 99999, // 使用特殊ID避免冲突
        latitude: currentLocation.value.latitude,
        longitude: currentLocation.value.longitude,
        iconPath: '/static/tourLine/person_map.png',
        width: 25,
        height: 25,
        alpha: 1,
        title: '我的位置',
        attractionData: null, // 当前位置不是景点数据
        callout: {
          content: '我的位置',
          color: '#ffffff',
          bgColor: '#006633',
          borderRadius: 8,
          borderWidth: 2,
          borderColor: '#ffffff',
          padding: 8,
          fontSize: 14,
          display: 'ALWAYS',
          textAlign: 'center'
        }
      });
    }

    return markers;
  });

  // 地图中心点
  const mapCenter = ref({
    latitude: 30.411349, // 默认邛崃市中心
    longitude: 103.461507
  })

  // 地图标记点
  const mapMarkers = ref<any[]>([])
  
  // 地图路线
  const mapPolyline = ref<any[]>([])
  
  // 新地图中心点
  const newMapCenter = ref({
    latitude: 30.411349, // 默认邛崃市中心
    longitude: 103.461507
  })

  // 新地图标记点
  const newMapMarkers = ref<any[]>([])
  
  // 新地图路线
  const newMapPolyline = ref<any[]>([])
  
  const toGuide = ()=>{
    if (!selectedAttraction.value) {
      uni.showToast({
        title: '请先选择景点',
        icon: 'none'
      });
      return;
    }

    if (!selectedAttraction.value.latitude || !selectedAttraction.value.longitude) {
      uni.showToast({
        title: '该景点暂无位置信息',
        icon: 'none'
      });
      return;
    }

    // 使用uni.openLocation打开地图导航
    uni.openLocation({
      latitude: parseFloat(selectedAttraction.value.latitude),
      longitude: parseFloat(selectedAttraction.value.longitude),
      name: selectedAttraction.value.attractionName,
      address: selectedAttraction.value.attractionAddress || '',
      scale: 18,
      success: function () {
        console.log('打开地图成功');
      },
      fail: function (err) {
        console.error('打开地图失败:', err);
        uni.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    });
  }

  // 计算两点间距离（单位：米）
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371000; // 地球半径，单位：米
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  // 计算当前位置偏移量
  const calculateLocationOffset = (currentLat: number, currentLng: number) => {
    if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
      return '暂无参考点';
    }

    let minDistance = Infinity;
    let nearestAttraction: any = null;

    // 找到最近的景点
    data.value.attractionsList.forEach((attraction: any) => {
      if (attraction.latitude && attraction.longitude) {
        const distance = calculateDistance(
          currentLat,
          currentLng,
          parseFloat(attraction.latitude),
          parseFloat(attraction.longitude)
        );
        if (distance < minDistance) {
          minDistance = distance;
          nearestAttraction = attraction;
        }
      }
    });

    if (nearestAttraction) {
      if (minDistance < 1000) {
        return `距离${nearestAttraction.attractionName} ${minDistance.toFixed(0)}米`;
      } else {
        return `距离${nearestAttraction.attractionName} ${(minDistance / 1000).toFixed(1)}公里`;
      }
    }

    return '暂无参考点';
  };

  // 计算位置统计数据
  const calculateLocationStats = (currentLat: number, currentLng: number, altitude: number = 0, accuracy: number = 0) => {
    const stats = {
      distanceToStart: '0.00',
      distanceToEnd: '0.00',
      distanceToMe: '0.00',
      altitude: altitude.toFixed(0),
      accuracy: accuracy.toFixed(0),
      signalStrength: '38' // 模拟GPS信号强度
    };

    if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
      return stats;
    }

    // 过滤有效的景点（有经纬度的）
    const validAttractions = data.value.attractionsList.filter((attraction: any) =>
      attraction.latitude &&
      attraction.longitude &&
      !isNaN(parseFloat(attraction.latitude)) &&
      !isNaN(parseFloat(attraction.longitude))
    );

    if (validAttractions.length === 0) {
      return stats;
    }

    // 起点：数组第一个元素
    const startPoint = validAttractions[0];
    // 终点：数组最后一个元素
    const endPoint = validAttractions[validAttractions.length - 1];

    console.log('起点:', startPoint.attractionName, startPoint.latitude, startPoint.longitude);
    console.log('终点:', endPoint.attractionName, endPoint.latitude, endPoint.longitude);

    // 计算距起点距离
    if (startPoint) {
      const distanceToStart = calculateDistance(
        currentLat,
        currentLng,
        parseFloat(startPoint.latitude),
        parseFloat(startPoint.longitude)
      );
      stats.distanceToStart = (distanceToStart / 1000).toFixed(2);
      console.log('距起点距离:', stats.distanceToStart, 'km');
    }

    // 计算距终点距离
    if (endPoint) {
      const distanceToEnd = calculateDistance(
        currentLat,
        currentLng,
        parseFloat(endPoint.latitude),
        parseFloat(endPoint.longitude)
      );
      stats.distanceToEnd = (distanceToEnd / 1000).toFixed(2);
      console.log('距终点距离:', stats.distanceToEnd, 'km');
    }

    // 计算到最近景点的距离
    let minDistanceToMe = Infinity;
    validAttractions.forEach((attraction: any) => {
      const distance = calculateDistance(
        currentLat,
        currentLng,
        parseFloat(attraction.latitude),
        parseFloat(attraction.longitude)
      );
      if (distance < minDistanceToMe) {
        minDistanceToMe = distance;
      }
    });

    // 距离我（最近景点的距离）
    stats.distanceToMe = (minDistanceToMe / 1000).toFixed(2);
    console.log('距离我:', stats.distanceToMe, 'km');

    return stats;
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    uni.showLoading({
      title: '定位中...'
    });

    uni.getLocation({
      type: 'gcj02', // 使用gcj02坐标系，与地图坐标系一致
      altitude: true, // 获取海拔信息
      success: function (res) {
        console.log('获取当前位置成功:', res);

        // 保存当前位置
        currentLocation.value = {
          latitude: res.latitude,
          longitude: res.longitude
        };

        // 计算偏移量
        locationOffset.value = calculateLocationOffset(res.latitude, res.longitude);

        // 计算位置统计数据
        locationStats.value = calculateLocationStats(
          res.latitude,
          res.longitude,
          res.altitude || 0,
          res.accuracy || 0
        );

        // 显示当前位置标记
        showCurrentLocation.value = true;

        // 将地图中心移动到当前位置
        gpsMapCenter.value = {
          latitude: res.latitude,
          longitude: res.longitude
        };

        // 设置合适的缩放级别
        mapScale.value = 16;

        // 移动地图到当前位置
        if (gpsMapContext) {
          gpsMapContext.moveToLocation({
            latitude: res.latitude,
            longitude: res.longitude,
            success: () => {
              console.log('地图移动到当前位置成功');
            },
            fail: (err: any) => {
              console.error('地图移动失败:', err);
            }
          });
        }

        uni.hideLoading();
        uni.showToast({
          title: '定位成功',
          icon: 'success',
          duration: 1500
        });
      },
      fail: function (err) {
        console.error('获取位置失败:', err);
        uni.hideLoading();

        // 根据错误类型给出不同提示
        let errorMsg = '定位失败';
        if (err.errMsg && err.errMsg.includes('auth deny')) {
          errorMsg = '请开启定位权限';
        } else if (err.errMsg && err.errMsg.includes('timeout')) {
          errorMsg = '定位超时，请重试';
        }

        uni.showModal({
          title: '定位失败',
          content: errorMsg + '，是否前往设置开启定位权限？',
          confirmText: '去设置',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              uni.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userLocation']) {
                    // 用户开启了定位权限，重新获取位置
                    getCurrentLocation();
                  }
                }
              });
            }
          }
        });
      }
    });
  }

  // 切换全屏状态
  const toggleFullScreen = () => {
    isFullScreen.value = !isFullScreen.value;
    console.log('切换全屏状态:', isFullScreen.value);
  };

  // 地图上下文对象
  let mapContext: any = null
  let newMapContext: any = null
  let gpsMapContext: any = null

  // 选择景点
  function selectAttraction(attraction: any, index: number) {
    selectedAttraction.value = attraction
    selectedAttractionId.value = attraction.attractionId || index
    
    console.log('选中景点:', {
      attractionId: attraction.attractionId,
      selectedAttractionId: selectedAttractionId.value,
      attractionName: attraction.attractionName,
      latitude: attraction.latitude,
      longitude: attraction.longitude
    });
    
    // 更新地图中心点到选中的景点
    if (attraction.latitude && attraction.longitude) {
      const lat = parseFloat(attraction.latitude);
      const lng = parseFloat(attraction.longitude);
      
      // 将WGS84坐标转换为GCJ02坐标，与标记坐标保持一致
      const [gcjLng, gcjLat] = coordtransform.wgs84togcj02(lng, lat);
      
      const newCenter = {
        latitude: gcjLat,
        longitude: gcjLng
      }
      mapCenter.value = newCenter
      newMapCenter.value = newCenter
      gpsMapCenter.value = newCenter
      
      // 增加地图缩放级别，放大更多
      mapScale.value = 18
      
      // 移动地图到指定位置
      if (mapContext) {
        mapContext.moveToLocation({
          latitude: newCenter.latitude,
          longitude: newCenter.longitude
        })
      }
      
      // 同时移动新地图
      if (newMapContext) {
        newMapContext.moveToLocation({
          latitude: newCenter.latitude,
          longitude: newCenter.longitude
        })
      }
      
            // 移动GPS地图到指定位置并居中显示
      // 确保GPS地图上下文存在
      if (!gpsMapContext) {
        gpsMapContext = uni.createMapContext('gps-track-map');
      }
      
      if (gpsMapContext) {
        console.log('移动地图到:', newCenter);
        
        // 立即更新地图中心点
        gpsMapCenter.value = {
          latitude: newCenter.latitude,
          longitude: newCenter.longitude
        };
        
        console.log('地图中心点已更新为:', gpsMapCenter.value);
        
        // 先设置一个中等的缩放级别
        mapScale.value = 16;
        
        // 延迟执行地图操作，确保响应式更新完成
        setTimeout(() => {
          console.log('开始执行地图居中操作，坐标:', newCenter);
          
          // 直接移动到指定位置
          gpsMapContext.moveToLocation({
            latitude: newCenter.latitude,
            longitude: newCenter.longitude,
            success: () => {
              console.log('moveToLocation成功');
              
              // 移动成功后，进一步放大到最终级别
              setTimeout(() => {
                mapScale.value = 18;
                console.log('地图已放大到最终级别18');
              }, 200);
            },
            fail: (err: any) => {
              console.error('moveToLocation失败:', err);
            }
          });
          
        }, 100);
      } else {
        console.error('GPS地图上下文未找到');
      }
    }
    
    // 更新标记点动画状态
    updateMarkerAnimation()
    
    // 强制刷新地图以确保居中
    forceMapCenter()
    
    // 不再刷新地图标记，避免影响其他标记
    // refreshMapMarkers() // 移除这行
  }
  
  // 更新标记点动画状态
  function updateMarkerAnimation() {
    // 重置所有动画状态，确保只有一个景点动画
    markerAnimationState.value = {}
    
    // 为选中的景点启动动画
    if (selectedAttractionId.value !== null) {
     // console.log('开始提示框跳动动画，景点ID:', selectedAttractionId.value);
      
      // 只为当前选中的景点启动动画
      markerAnimationState.value[selectedAttractionId.value] = true
      
      // 创建绿色跳动动画效果
      let bounceCount = 0;
      const maxBounces = 6; // 6次跳动就足够了
      
             const bounceAnimation = () => {
         if (bounceCount < maxBounces && selectedAttractionId.value !== null) {
           // 切换动画状态来创建跳动效果
           markerAnimationState.value[selectedAttractionId.value!] = !markerAnimationState.value[selectedAttractionId.value!];
           bounceCount++;
           
          //  console.log(`提示框跳动第${bounceCount}次，景点ID: ${selectedAttractionId.value}，动画状态:`, markerAnimationState.value[selectedAttractionId.value!]);
           
           // 不再刷新标记，只依靠响应式更新
           // animateMarkerBounce(); // 移除这行，避免影响其他标记
           
           // 每400ms切换一次，自然的跳动节奏
           setTimeout(bounceAnimation, 400);
         } else {
           // 动画结束，清除当前景点的动画状态
           if (selectedAttractionId.value !== null) {
             markerAnimationState.value[selectedAttractionId.value!] = false;
           }
          //  console.log('提示框跳动动画结束');
           
           // 不再刷新标记，依靠响应式更新就足够了
           // refreshMapMarkers(); // 移除这行，避免影响其他标记
         }
       };
      
      // 立即开始跳动动画
      setTimeout(bounceAnimation, 50);
    }
  }
  
  // 强制地图居中
  function forceMapCenter() {
    if (!selectedAttraction.value) return;
    
    // 确保GPS地图上下文存在
    if (!gpsMapContext) {
      gpsMapContext = uni.createMapContext('gps-track-map');
    }
    
    if (!gpsMapContext) {
      console.error('无法创建GPS地图上下文');
      return;
    }
    
    const lat = parseFloat(selectedAttraction.value.latitude);
    const lng = parseFloat(selectedAttraction.value.longitude);
    
    // 将WGS84坐标转换为GCJ02坐标，与标记坐标保持一致
    const [gcjLng, gcjLat] = coordtransform.wgs84togcj02(lng, lat);
    
    const center = {
      latitude: gcjLat,
      longitude: gcjLng
    };
    
    // console.log('强制地图居中到:', center);
    
    // 立即更新地图中心点
    gpsMapCenter.value = center;
    
    // 设置更大的缩放级别，放大更多
    mapScale.value = 18;
    
    // 延迟执行确保地图已渲染
    setTimeout(() => {
      // 使用moveToLocation移动到指定位置
      gpsMapContext.moveToLocation({
        latitude: center.latitude,
        longitude: center.longitude,
        success: () => {
          // console.log('强制居中成功');
        },
        fail: (err: any) => {
          console.error('强制居中失败:', err);
        }
      });
    }, 100);
  }

  // 刷新地图标记
  function refreshMapMarkers() {
    // 强制重新渲染地图标记
    const currentMarkers = [...gpsMarkers.value];
    gpsMarkers.value = [];
    
    nextTick(() => {
      gpsMarkers.value = currentMarkers;
      // console.log('地图标记已刷新');
    });
  }
  
  // 不再需要动画标记刷新函数
  // function animateMarkerBounce() {
  //   if (selectedAttractionId.value !== null) {
  //     // 通过快速刷新标记来增强跳动效果
  //     refreshMapMarkers();
  //   }
  // }

  // 处理地图标记点击
  function onMarkerTap(e: any) {
    const markerId = e.detail.markerId
    const attraction = data.value.attractionsList?.[markerId]
    if (attraction) {
      selectAttraction(attraction, markerId)
    }
  }

  // 处理地图点击
  function onMapTap(e: any) {
    // console.log('地图点击:', e.detail)
  }

  // 处理地图区域变化
  function onRegionChange(e: any) {
    // console.log('地图区域变化:', e.detail)
    if (e.detail.type === 'end') {
      // 用户手动移动地图后，更新地图中心点
      mapCenter.value = {
        latitude: e.detail.centerLocation.latitude,
        longitude: e.detail.centerLocation.longitude
      }
    }
  }

  // 处理新地图标记点击
  function onNewMarkerTap(e: any) {
    const markerId = e.detail.markerId
    const attraction = data.value.attractionsList?.[markerId]
    if (attraction) {
      selectAttraction(attraction, markerId)
    }
  }

  // 处理新地图点击
  function onNewMapTap(e: any) {
    // console.log('新地图点击:', e.detail)
  }

  // 处理新地图区域变化
  function onNewRegionChange(e: any) {
    // console.log('新地图区域变化:', e.detail)
    if (e.detail.type === 'end') {
      // 用户手动移动地图后，更新地图中心点
      newMapCenter.value = {
        latitude: e.detail.centerLocation.latitude,
        longitude: e.detail.centerLocation.longitude
      }
    }
  }

  // 初始化地图数据
  function initMapData() {
    if (!data.value.attractionsList || data.value.attractionsList.length === 0) {
      return
    }

    const attractions = data.value.attractionsList
    
    // 过滤有效坐标的景点
    const validAttractions = attractions.filter((attraction: any) => 
      attraction.latitude && 
      attraction.longitude && 
      !isNaN(parseFloat(attraction.latitude)) && 
      !isNaN(parseFloat(attraction.longitude))
    )

    if (validAttractions.length === 0) {
      console.warn('没有有效的景点坐标数据')
      return
    }
    
    // console.log('有效景点数据:', validAttractions)
    
    // 设置标记点
    const markers = validAttractions.map((attraction: any, index: number) => ({
      id: index,
      latitude: parseFloat(attraction.latitude),
      longitude: parseFloat(attraction.longitude),
      title: attraction.attractionName,
      width: 1,
      height: 1,
      anchor: {
        x: 0.5,
        y: 0.5
      },
      alpha: 0,
      label: {
        content: attraction.attractionName,
        color: '#ffffff',
        bgColor: '#006633',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#ffffff',
        padding: 6,
        fontSize: 12,
        textAlign: 'center',
        anchorX: 0,
        anchorY: 0
      }
    }))
    
    mapMarkers.value = markers

    // 绘制旅游线路
    if (validAttractions.length > 1) {
      drawTourRoute(validAttractions)
    }

    // 设置地图中心为第一个有效景点
    const firstAttraction = validAttractions[0]
    mapCenter.value = {
      latitude: parseFloat(firstAttraction.latitude),
      longitude: parseFloat(firstAttraction.longitude)
    }

    // 设置新地图数据，使用不同的样式
    const newMarkers = validAttractions.map((attraction: any, index: number) => ({
      id: index,
      latitude: parseFloat(attraction.latitude),
      longitude: parseFloat(attraction.longitude),
      title: attraction.attractionName,
      width: 40,
      height: 40,
      iconPath: '/static/index/location.png', // 使用可用的图标
      anchor: {
        x: 0.5,
        y: 0.5
      },
      label: {
        content: `${index + 1}`,
        color: '#ffffff',
        bgColor: '#2bbc4a',
        borderRadius: 50,
        borderWidth: 2,
        borderColor: '#ffffff',
        padding: 4,
        fontSize: 14,
        textAlign: 'center',
        anchorX: 0.5,
        anchorY: 0.5
      },
      callout: {
        content: attraction.attractionName,
        color: '#333',
        bgColor: '#ffffff',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#2bbc4a',
        padding: 8,
        fontSize: 14,
        display: 'ALWAYS'
      }
    }))
    
    newMapMarkers.value = newMarkers
    
    // 设置新地图路线样式
    if (validAttractions.length > 1) {
      const sortedAttractions = validAttractions.sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0))
      const points = sortedAttractions.map((attraction: any) => ({
        latitude: parseFloat(attraction.latitude),
        longitude: parseFloat(attraction.longitude)
      }))
      
      newMapPolyline.value = [{
        points: points,
        color: '#2bbc4a', // 绿色路线
        width: 6,
        dottedLine: true, // 虚线样式
        arrowLine: true,
        borderColor: '#ffffff',
        borderWidth: 2
      }]
    } else {
      newMapPolyline.value = []
    }
    
    newMapCenter.value = mapCenter.value

    // 获取地图上下文
    nextTick(() => {
      mapContext = uni.createMapContext('tour-map')
      newMapContext = uni.createMapContext('new-tour-map')
      gpsMapContext = uni.createMapContext('gps-track-map')
      
      // 调整地图视野以包含所有景点
      if (validAttractions.length > 1) {
        includeAllPoints(validAttractions)
      }
      
      // 调整新地图视野包含所有GPS轨迹点
      if (newMapPolyline.value.length > 0 && newMapPolyline.value[0].points.length > 1) {
        const gpsPoints = newMapPolyline.value[0].points;
        
        if (newMapContext) {
          newMapContext.includePoints({
            points: gpsPoints,
            padding: [50, 50, 50, 50]
          });
        }
      }
    })
  }

  // 绘制旅游线路（腾讯地图原生实现）
  function drawTourRoute(attractions: any[]) {
    // 按照sort字段排序景点
    const sortedAttractions = attractions.sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0))
    
    // 构建路线点
    const points = sortedAttractions.map((attraction: any) => ({
      latitude: parseFloat(attraction.latitude),
      longitude: parseFloat(attraction.longitude)
    }))

    // console.log('绘制路线点:', points)

    // 创建路线
    mapPolyline.value = [{
      points: points,
      color: '#006633', // 绿色主题
      width: 8,
      dottedLine: false,
      arrowLine: true, // 显示箭头方向
      borderColor: '#ffffff',
      borderWidth: 3
    }]
  }

  // 调整地图视野包含所有景点
  function includeAllPoints(attractions: any[]) {
    if (!mapContext || attractions.length === 0) return
    
    const points = attractions.map((attraction: any) => ({
      latitude: parseFloat(attraction.latitude),
      longitude: parseFloat(attraction.longitude)
    }))
    
    mapContext.includePoints({
      points: points,
      padding: [50, 50, 50, 50] // 上右下左的边距
    })
  }

  // 调整新地图视野包含所有景点
  function includeAllPointsNewMap(attractions: any[]) {
    if (!newMapContext || attractions.length === 0) return
    
    const points = attractions.map((attraction: any) => ({
      latitude: parseFloat(attraction.latitude),
      longitude: parseFloat(attraction.longitude)
    }))
    
    newMapContext.includePoints({
      points: points,
      padding: [50, 50, 50, 50] // 上右下左的边距
    })
  }

  // 初始化GPS地图数据（使用接口返回的tracks数据）
  function initGPSMapData() {
    try {
     // console.log('使用接口数据初始化GPS地图');
      
      // 使用接口返回的tracks数据
      const tracksData = data.value.tracks || [];
      let routePoints = [];
      
      if (tracksData && tracksData.length > 0) {
      //  console.log('使用tracks数据创建轨迹线');
        // 使用tracks数组数据创建轨迹线，并进行坐标转换
        routePoints = tracksData.map((track: any) => {
          // 将坐标转换为数字类型
          const lat = parseFloat(track.latitude);
          const lng = parseFloat(track.longitude);
          
          // 将WGS84坐标转换为GCJ02坐标
          const [gcjLng, gcjLat] = coordtransform.wgs84togcj02(lng, lat);
          return {
            latitude: gcjLat,
            longitude: gcjLng
          };
        });
      } else if (data.value.attractionsList && data.value.attractionsList.length > 0) {
        //('tracks数据为空，使用attractionsList数据创建轨迹线');
        // 当tracks为null时，使用attractionsList创建轨迹线
        const validAttractions = data.value.attractionsList.filter((attraction: any) => 
          attraction.latitude && 
          attraction.longitude && 
          !isNaN(parseFloat(attraction.latitude)) && 
          !isNaN(parseFloat(attraction.longitude))
        );
        
        // 按照sort字段排序，如果没有sort则按pointFlag排序（起点->途径点->终点）
        const sortedAttractions = validAttractions.sort((a: any, b: any) => {
          if (a.sort !== undefined && b.sort !== undefined) {
            return (a.sort || 0) - (b.sort || 0);
          }
          // 按pointFlag排序：起点(0) -> 途径点(1) -> 终点(2)
          const aFlag = parseInt(a.pointFlag) || a.pointFlag || 1;
          const bFlag = parseInt(b.pointFlag) || b.pointFlag || 1;
          return aFlag - bFlag;
        });
        
        routePoints = sortedAttractions.map((attraction: any) => {
          const lat = parseFloat(attraction.latitude);
          const lng = parseFloat(attraction.longitude);
          
          // 将WGS84坐标转换为GCJ02坐标
          const [gcjLng, gcjLat] = coordtransform.wgs84togcj02(lng, lat);
          return {
            latitude: gcjLat,
            longitude: gcjLng
          };
        });
      }
      
      // 设置轨迹线（如果有数据的话）
      if (routePoints.length > 0) {
        gpsPolyline.value = [{
          points: routePoints,
          color: '#066239', 
          width: 2,
          arrowLine: false, // 不显示箭头，更干净
          borderColor: '#ffffff',
          borderWidth: 1,
          dottedLine: false
        }];
      } else {
        gpsPolyline.value = [];
      }
        
      // 清空GPS标记点
      gpsMarkers.value = [];
      
      // 添加景点标记点（使用attractionsList数据，根据pointFlag区分类型）
      if (data.value.attractionsList && data.value.attractionsList.length > 0) {
        data.value.attractionsList.forEach((attraction: any, index: number) => {
          if (attraction.latitude && attraction.longitude) {
            const lat = parseFloat(attraction.latitude);
            const lng = parseFloat(attraction.longitude);
            
            // 坐标转换
            const [gcjLng, gcjLat] = coordtransform.wgs84togcj02(lng, lat);
            
            // 根据pointFlag字段确定标记类型
            let iconPath = '';
            let width = 20;
            let height = 20;
            
            // 0:起点，1:途径点，2:终点（支持字符串和数字类型）
            const pointFlag = parseInt(attraction.pointFlag) || attraction.pointFlag;
            
            // 检查是否为选中的景点
            const isSelected = selectedAttractionId.value === (attraction.attractionId || index);
            
            if (pointFlag === 0 || pointFlag === "0") {
              // 起点 - 使用自定义图标
              iconPath = 'https://www.2bulu.com/images/icon/icon_start.png';
              if (isSelected) {
                width = 30;
                height = 30;
              }
            } else if (pointFlag === 2 || pointFlag === "2") {
              // 终点 - 使用自定义图标
              iconPath = 'https://www.2bulu.com/images/icon/icon_end.png';
              if (isSelected) {
                width = 30;
                height = 30;
              }
            } else if (pointFlag === 1 || pointFlag === "1") {
              // 途经点 - 使用自定义图标
              iconPath = '/static/tourLine/marker.png';
              if (isSelected) {
                width = 30;
                height = 30;
              }
            }

            const markerObj: any = {
              id: index + 1,
              latitude: gcjLat,
              longitude: gcjLng,
              iconPath: iconPath,
              width: width,
              height: height,
              title: attraction.attractionName || '',
              attractionData: attraction,
              // 新增：动态标记样式
              callout: {
                content: attraction.attractionName || '',
                color: '#003F03',
                bgColor: '#ffffff',
                borderRadius: 8,
                borderWidth: 2,
                borderColor: '#ffffff',
                padding: 8,
                fontSize: 14,
                display: 'ALWAYS',
                textAlign: 'center'
              }
            };
            
            gpsMarkers.value.push(markerObj);
          }
        });
      }
      
      // 计算地图中心点
      if (routePoints.length > 0) {
        const lats = routePoints.map((p: { latitude: number; longitude: number }) => p.latitude);
        const lngs = routePoints.map((p: { latitude: number; longitude: number }) => p.longitude);
        
        gpsMapCenter.value = {
          latitude: (Math.max(...lats) + Math.min(...lats)) / 2,
          longitude: (Math.max(...lngs) + Math.min(...lngs)) / 2
        };
      }
      
      // console.log('GPS地图数据初始化完成:', {
      //   markers: gpsMarkers.value.length,
      //   routePoints: routePoints.length,
      //   attractionsList: data.value.attractionsList?.length || 0
      // });
      
      // 调试：打印所有标记点信息
      // console.log('所有标记点详情:', gpsMarkers.value.map(m => ({
      //   id: m.id,
      //   iconPath: m.iconPath,
      //   pointFlag: m.attractionData?.pointFlag,
      //   name: m.attractionData?.attractionName,
      //   width: m.width,
      //   height: m.height
      // })));
      
      // 最终检查：确保有标记点数据
      if (gpsMarkers.value.length === 0) {
        console.warn('警告：没有创建任何标记点！');
      } else {
        console.log(`成功创建了 ${gpsMarkers.value.length} 个标记点`);
      }
      
    } catch (error) {
      console.error('初始化GPS地图数据失败:', error);
      initFallbackGPSData();
    }
  }
  
  // 备用GPS数据初始化
  function initFallbackGPSData() {
    console.log('使用备用数据初始化GPS地图');
    
    // 创建一个简单的轨迹线作为备用
    const fallbackRoute = [
      { latitude: 30.3262018, longitude: 103.2816216 },
      { latitude: 30.3244852, longitude: 103.2788938 },
      { latitude: 30.3239008, longitude: 103.2778191 },
      { latitude: 30.3240108, longitude: 103.2775803 }
    ];
    
    gpsPolyline.value = [{
      points: fallbackRoute,
      color: '#4A90E2',
      width: 6,
      arrowLine: true,
      borderColor: '#ffffff',
      borderWidth: 2
    }];
    
    gpsMarkers.value = [
      {
        id: 1,
        latitude: 30.3262018,
        longitude: 103.2816216,
        iconPath: 'https://www.2bulu.com/images/icon/icon_start.png',
        width: 20,
        height: 20,
        title: ''
      }
    ];
    
    gpsMapCenter.value = {
      latitude: 30.3262018,
      longitude: 103.2816216
    };
    
    console.log('备用GPS地图数据初始化完成');
  }
  
  // GPS地图相关事件处理
  const onGPSMarkerTap = (e: any) => {
    const markerId = e.markerId;
    const marker = gpsMarkers.value.find(m => m.id === markerId);

    console.log('标记点击:', markerId, marker);

    // 检查是否点击的是当前位置标记
    if (markerId === 99999) {
      // 点击的是当前位置标记
      uni.showToast({
        title: '这是您的当前位置',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (marker && marker.attractionData) {
      // 选择景点并更新地图
      selectAttraction(marker.attractionData, markerId - 1);

      // 处理景点点击
      selectedGPSPoint.value = {
        ...marker.attractionData,
        coordinates: [marker.longitude, marker.latitude],
        timestamp: new Date().toISOString()
      };
      showPointDetail.value = true;
    }
    // 移除了显示"标记点"文字的toast提示
  };
  
  const onGPSMapTap = (e: any) => {
    // console.log('GPS地图点击:', e);
  };
  
  const onGPSRegionChange = (e: any) => {
    // console.log('GPS地图区域变化:', e);
  };
  
  const closePointDetail = () => {
    showPointDetail.value = false;
    selectedGPSPoint.value = null;
  };
  
  const formatTime = (timestamp: string) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  // 预览图片
  const previewImage = (imageUrl: string) => {
    uni.previewImage({
      urls: [imageUrl],
      current: imageUrl,
      indicator: 'number',
      loop: false
    });
  };
  
  // 图片加载错误处理
  const onImageError = (e: any) => {
    console.error('图片加载失败:', e);
    uni.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 2000
    });
  };
  
  // 图片加载成功处理
  const onImageLoad = (e: any) => {
    console.log('图片加载成功:', e);
  };

  onLoad((options: any) => {
    id.value = decodeURIComponent(options.id)
    console.log('旅游线路ID:', id.value)
    
    getPageItemDetail({
      tourLineId: id.value
    }).then((res: any) => {
      console.log('详情数据:', res.data)
      data.value = res.data;
      
      // 调试：查看景点列表中的pointFlag
      // if (data.value.attractionsList) {
      //   console.log('所有景点的pointFlag:', data.value.attractionsList.map((item: any) => ({
      //     name: item.attractionName,
      //     pointFlag: item.pointFlag,
      //     type: typeof item.pointFlag
      //   })));
      // }
      
      // 初始化地图数据
      initMapData()
      
      // 初始化GPS地图数据（使用接口返回的真实数据）
      initGPSMapData()
      
      // 设置分享信息
      share = {
        title: data.value.tourLineName,
        desc: data.value.tourLineIntroduction,
        imageUrl: data.value.attractionsList?.[0]?.attractionImages || '',
      }
      setShare({
        weapp: {
          ...share
        }
      });
    }).catch((error: any) => {
      console.error('获取详情失败:', error);
    })
  })
  
  onShow(() => {
    // 页面需要返回时，自定义分享内容
    setShare({
      weapp: {
        ...share
      }
    });
  });
</script>

<style lang="scss">
page {
  background: #f8f8f8;
}
</style>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f8f8f8;
}
.guide-btn{
  background: #2bbc4a;
  color: #fff;
  border-radius: 8rpx;
  padding: 8rpx 14rpx;
  margin-top: 30rpx;
  font-size: 28rpx;
}
.map-container {
  position: relative;
  width: 100%;
  height: 700rpx;
  background: #f0f0f0;
}

.gps-map-container {
  position: relative;
  width: 100%;
  background: #ffffff;
  // border-radius: 16rpx;
  overflow: hidden;
  // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .map-title {
    padding: 20rpx 30rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    text-align: center;

    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      display: block;
      margin-bottom: 8rpx;
    }

    .subtitle-text {
      font-size: 24rpx;
      opacity: 0.9;
      display: block;
    }
  }

  .map-info {
    padding: 20rpx 30rpx;
    background: #f8f9fa;
    border-top: 2rpx solid #e9ecef;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
}

/* 当前位置信息显示样式 */
.location-info {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  z-index: 10000; /* 提高层级，确保在地图之上 */
  backdrop-filter: blur(10rpx);

  /* 距离信息行样式 */
  .distance-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12rpx;

    .distance-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .distance-value {
        font-size: 32rpx;
        color: #00FF00;
        font-weight: bold;
        line-height: 1.2;
      }

      .distance-label {
        font-size: 20rpx;
        color: #ffffff;
        opacity: 0.8;
        margin-top: 4rpx;
      }
    }
  }

  /* 坐标信息行样式 */
  .coordinate-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8rpx;

    .coordinate-text {
      font-size: 20rpx;
      color: #ffffff;
      opacity: 0.9;
    }

    .gps-status {
      display: flex;
      align-items: center;
      gap: 4rpx;

      .gps-icon {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background: #00FF00;
      }

      .gps-text {
        font-size: 20rpx;
        color: #ffffff;
        opacity: 0.8;
      }

      .gps-signal {
        font-size: 20rpx;
        color: #00FF00;
        font-weight: bold;
      }
    }
  }
}
/* 定位按钮和全屏按钮样式 */
.location-btn,.screen {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  width: 80rpx;
  height: 80rpx;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 10000; /* 确保在地图之上 */
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  }

  .location-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.screen{
  right: 120rpx!important; /* 全屏按钮位置调整 */
}

/* 全屏状态下的按钮和信息框样式 */
.page-container.fullscreen .location-btn,
.page-container.fullscreen .screen {
  position: fixed !important;
  z-index: 10001 !important;
}

.page-container.fullscreen .location-info {
  position: fixed !important;
  z-index: 10001 !important;
}

.map-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 400rpx;
  background: #f8f8f8;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 拍摄点图片弹窗样式 */
.photo-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.photo-modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  width: 95%;
  max-width: 650rpx;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25);
  animation: photoModalFadeIn 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes photoModalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.photo-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  background: #FFCC35;
  color: white;
}

.photo-modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.photo-close-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  // background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: background-color 0.3s;
  
  &:active {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

.photo-modal-body {
  max-height: 80vh;
  overflow-y: auto;
}

.photo-container {
  position: relative;
  width: 100%;
  background: #f8f9fa;
}

.main-photo {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
  display: block;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 20rpx 20rpx;
  text-align: center;
}

.photo-tip {
  color: white;
  font-size: 24rpx;
  opacity: 0.9;
}

.gps-info-card {
  padding: 30rpx;
  background: white;
}

.attraction-info-card {
  padding: 30rpx;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

.header-section {
  background: #ffffff;
  padding: 30rpx 30rpx 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .tour-title {
    font-size: 36rpx;
    // font-weight: bold;
    text-align: center;
    color: #333;
    flex: 1;
  }
}

.share-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  
  &::after {
    border: none;
  }
  
  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.trip-info {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 20rpx;
  
  .trip-text, .spots-text {
    font-size: 28rpx;
    color: #666;
  }
  
  .area-text {
    font-size: 24rpx;
    color: #999;
  }
}

.intro-section {

  .intro-btn {
    display: inline-block;
    background: #e8f4e8;
    border: 2rpx solid #4caf50;
    border-radius: 8rpx;
    padding: 12rpx 24rpx;
    

  }
  
  .intro-content {
    margin-top: 20rpx;
    padding: 20rpx;
    background: #f9f9f9;
    border-radius: 8rpx;
    
    .intro-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}
.attraction-list-content{
  height: 500rpx;
    width: 200rpx;
    // flex:1;
    overflow-y: auto;
}
.attractions-section {
  padding: 0 30rpx;
  background: #ffffff;
  display: flex;
  justify-content: space-between;  
  .attraction-list{
  
    .intro-text {
      color: #2bbc4a;
    border: 2rpx solid #2bbc4a;
      font-size: 28rpx;
      width: 100%;
      color: #4caf50;
      width: 195rpx;
      height: 60rpx;
      text-align: center;
      display: block;
      line-height: 60rpx;
      margin-bottom: 10rpx;
    }
  }
  .attraction-text{
    margin-left: 24rpx;
    flex:1;
    text{
      font-size: 28rpx;
      color: #333;
    }
  }
}

.attraction-item {
  display: flex;
  width:200rpx;
  align-items: center;
  position: relative;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  // transition: all 0.3s ease;
  cursor: pointer;
  
  // &:hover {
  //   transform: scale(1.02);
  // }
  
  &.selected {
    // background: linear-gradient(135deg, #2bbc4a 0%, #1a8c3a 100%);
    // transform: scale(1.05);
    // box-shadow: 0 8rpx 24rpx rgba(43, 188, 74, 0.4);
    border: 3rpx solid #ffffff;
    .attraction-name{
      background: #19993E !important;
    // text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
    border: 2rpx solid #ffffff !important;
    }
  }
  
  .attraction-image {
    width:200rpx;
    image {
      width: 100%;
      height: 160rpx;
      border-radius: 8rpx;
    }
  }
  
  .attraction-info {
      position: absolute;
      left:50%;
      top:50%;
      width:100%;
      text-align: center;
      transform: translate(-50%,-50%);
    .attraction-name {
      font-size: 28rpx;
      color: #fff;
      text-align: center;
      // text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.8);
      font-weight: bold;
      // background: rgba(0,0,0,0.3);
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
    }
  }
}

/* 地图标记动画样式 */
@keyframes markerBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes markerPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.marker-animation {
  animation: markerBounce 0.6s ease-in-out infinite, markerPulse 1.2s ease-in-out infinite;
}

  .attraction-info .attraction-name {
    color: #ffffff !important;
   
    font-weight: bold;

    border-radius: 8rpx;
    padding: 8rpx 12rpx;
  }
</style>