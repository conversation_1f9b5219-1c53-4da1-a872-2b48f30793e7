import { API_URL } from '../config';
import { request } from '../request';
/**
 * 获取列表
 * @param params 参数
 * @returns
 */
export function  getList(params : Record<string, any>) {
    return request({
        url: `${API_URL}/applet/communityTourLine/getAttractionsList`,
        method: 'GET',
        data: params,
        await: true,
    })
}

/**
 * 详情
 * @param params 参数
 * @returns
 */
export const getAttractionsDetails = (params: Record<string, any>) => {
    return request({
        url: `${API_URL}/applet/communityTourLine/getAttractionsDetails`,
        method: 'GET',
        data: params,
        await: true,
    })
}
export const getPageList = (params: Record<string, any>) => {
    return request({
        url: `${API_URL}/applet/communityTourLine/getPage`,
        method: 'GET',
        data: params,
        await: true,
    })
}
export const getPageItemDetail = (params: Record<string, any>) => {
    return request({
        url: `${API_URL}/applet/communityTourLine/getDetails`,
        method: 'GET',
        data: params,
        await: true,
    })
}

/**
 * 获取辖区数据
 * @param params 参数
 * @returns
 */
export const getDistrictData = (params: Record<string, any>) => {
    return request({
        url: `${API_URL}/applet/district/getDistrictByHeadChar?name=`,
        method: 'GET',
        data: params,
        await: true,
    })
}
